from django.contrib.contenttypes.models import ContentType
from django.http.response import Http404
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from pos.models import POSSession, POSSessionTransaction, TransactionType
from utils.pagination import PaginationClass
from utils.permissions import IsAdminOnly, IsAdminOrCashierOrManager
from ..models import Account
from ..models import TransactionType as AccountTransactionType
from ..serializers import AccountSerializer, AccountTransactionSerializer
from ..serializers.balance import BalanceSerializer


class AccountViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing accounts with different permission levels:
    - Admin: Full CRUD access
    - Manager: List access
    - Cashier: List access
    """

    queryset = Account.objects.all()
    serializer_class = AccountSerializer
    pagination_class = PaginationClass

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in [
            "list",
            "balances",
            "balance_transactions",
            "balance_create_transaction",
        ]:
            permission_classes = [IsAdminOrCashierOrManager]
        else:  # create, update, partial_update, destroy
            permission_classes = [IsAdminOnly]
        return [permission() for permission in permission_classes]

    def get_balance_queryset(self, account):
        """
        Get the balance queryset for the account
        """
        if not self.request.user.is_admin:
            balances = account.balances.filter(
                pos__employee__user=self.request.user,
            )
        else:
            balances = account.balances.all()
        return balances

    def get_balance(self, account, balance_pk):
        """
        Get the balance for the account
        """
        balance = self.get_balance_queryset(account).filter(id=balance_pk)
        if not balance.exists():
            raise Http404
        return balance.first()

    @action(detail=True, methods=["get"])
    def balances(self, request, pk=None):
        """
        Get transactions for a specific account
        """
        account = self.get_object()
        balances = self.get_balance_queryset(account)
        page = self.paginate_queryset(balances)
        if page is not None:
            serializer = BalanceSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = BalanceSerializer(balances, many=True)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["get"],
        url_path="balance/(?P<balance_pk>[^/.]+)/transactions",
    )
    def balance_transactions(self, request, pk=None, balance_pk=None):
        """
        Get transactions for a specific account
        """
        account = self.get_object()
        balance = self.get_balance(account, balance_pk)
        transactions = balance.transactions.all()
        page = self.paginate_queryset(transactions)

        if page is not None:
            serializer = AccountTransactionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = AccountTransactionSerializer(transactions, many=True)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["post"],
        url_path="balance/(?P<balance_pk>[^/.]+)/create-transactions",
    )
    def balance_create_transaction(self, request, pk=None, balance_pk=None):
        """
        Create a new transaction for the balance and optionally create a POS session transaction.
        """

        account = self.get_object()
        balance = self.get_balance(account, balance_pk)
        data = request.data.copy()
        data["account"] = account.id
        data["balance"] = balance.id
        session_id = data.pop("session_id", None)

        # Create the account transaction
        serializer = AccountTransactionSerializer(
            data=data, context={"request": request}
        )
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        transaction = serializer.save()

        # Create a POS session transaction if session_id is provided
        if session_id:
            try:
                session = POSSession.objects.get(
                    id=session_id,
                    status=POSSession.Status.OPEN,
                    pos=balance.pos,
                )
                # Create the POS session transaction
                pos_transaction = POSSessionTransaction.objects.create(
                    session=session,
                    transaction_type=TransactionType.PURCHASE
                    if transaction.type == AccountTransactionType.DEBIT
                    else TransactionType.CASH_IN,
                    amount=data["amount"],
                    description=data.get("description", ""),
                    content_type=ContentType.objects.get_for_model(transaction),
                    object_id=transaction.id,
                )

                # Update the response to include POS transaction ID
                response_data = serializer.data
                response_data["pos_transaction_id"] = pos_transaction.id
                return Response(response_data, status=status.HTTP_201_CREATED)

            except POSSession.DoesNotExist:
                return Response(
                    {"detail": "POS session not found or not open"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        return Response(serializer.data, status=status.HTTP_201_CREATED)
