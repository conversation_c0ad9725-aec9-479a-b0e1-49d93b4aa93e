from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from .models.purchase import Purchase
from .models.purchase_item import PurchaseItem
from .models.supplier import Supplier


class PurchaseItemInline(admin.TabularInline):
    model = PurchaseItem
    extra = 1
    readonly_fields = ("total_cost", "created", "modified")
    fields = (
        "product",
        "quantity",
        "unit_cost",
        "unit_price",
        "total_cost",
        "notes",
        "created",
        "modified",
    )
    show_change_link = True


@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    list_display = (
        "supplier_link",
        "warehouse",
        "total_amount",
        "payment_method",
        "created",
    )
    list_filter = ("warehouse", "payment_method", "created")
    search_fields = (
        "supplier__name",
        "notes",
    )
    readonly_fields = (
        "created",
        "modified",
        "net_amount",
    )
    date_hierarchy = "created"
    inlines = [PurchaseItemInline]
    fieldsets = (
        (_("Basic Information"), {"fields": ("warehouse", "supplier", "notes")}),
        (
            _("Financial Details"),
            {
                "fields": (
                    "total_amount",
                    "discount",
                    "bonus",
                    "net_amount",
                    "paid_amount",
                    "reminder_amount",
                    "payment_method",
                )
            },
        ),
        (_("Timestamps"), {"fields": ("created", "modified")}),
    )

    def supplier_link(self, obj):
        if obj.supplier:
            url = reverse("admin:purchases_supplier_change", args=[obj.supplier.id])
            return format_html('<a href="{}">{}</a>', url, obj.supplier)
        return "-"

    supplier_link.short_description = _("Supplier")
    supplier_link.admin_order_field = "supplier__name"


@admin.register(PurchaseItem)
class PurchaseItemAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "purchase_link",
        "quantity",
        "unit_cost",
        "unit_price",
        "total_cost",
        "created",
    )
    list_filter = ("created",)
    search_fields = ("product__name", "notes")
    readonly_fields = ("total_cost", "created", "modified")
    raw_id_fields = ("product", "purchase")

    def purchase_link(self, obj):
        url = reverse("admin:purchases_purchase_change", args=[obj.purchase.id])
        return format_html('<a href="{}">{}</a>', url, f"Purchase #{obj.purchase.id}")

    purchase_link.short_description = _("Purchase")
    purchase_link.admin_order_field = "purchase__id"


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ("name", "phone_number", "created", "modified")
    search_fields = ("name", "phone_number")
    readonly_fields = ("created", "modified")
    fieldsets = (
        (_("Basic Information"), {"fields": ("name", "phone_number", "account")}),
        (_("Timestamps"), {"fields": ("created", "modified")}),
    )
