from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from purchases_return.views.purchase_return import PurchaseReturnViewSet

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r"purchase-returns", PurchaseReturnViewSet, basename="purchase-return")

# The API URLs are now determined automatically by the router.
app_name = "purchases_return"
urlpatterns = [
    path("", include(router.urls)),
]
