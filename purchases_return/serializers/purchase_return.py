from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from rest_framework import serializers

from pos.models import POSSession, POSSessionTransaction
from pos.models.pos_session_transaction import TransactionType as POSTransactionType
from purchases.models.supplier import Supplier
from purchases_return.models.purchase_return import PurchaseReturn
from purchases_return.serializers.purchase_return_item import (
    PurchaseReturnItemSerializer,
)
from warehouses.models.stock_item import StockItem


class PurchaseReturnSerializer(serializers.ModelSerializer):
    """
    Serializer for the PurchaseReturn model.
    Handles creation and updating of PurchaseReturn instances along with their associated items.
    """

    pos_session_id = serializers.PrimaryKeyRelatedField(
        queryset=POSSession.objects.filter(status=POSSession.Status.OPEN).all(),
        source="pos_session",
        write_only=True,
        required=True,
    )
    warehouse_name = serializers.StringRelatedField(
        source="warehouse.name", read_only=True
    )

    supplier_id = serializers.PrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), source="supplier", write_only=True
    )
    supplier_name = serializers.StringRelatedField(
        source="supplier.name", read_only=True
    )

    items = PurchaseReturnItemSerializer(many=True, required=True)
    total_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="Calculated automatically from items",
    )

    class Meta:
        model = PurchaseReturn
        fields = [
            "id",
            "warehouse_name",
            "pos_session_id",
            "supplier_id",
            "supplier_name",
            "total_amount",
            "payment_method",
            "notes",
            "items",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
            "total_amount",
        ]

    def validate_items(self, value):
        """Validate that at least one item is provided."""
        if not value:
            raise serializers.ValidationError("At least one return item is required.")
        return value

    def validate(self, data):
        """
        Validate the purchase return data.
        """
        # Validate POS session ownership for non-admin users
        pos_session = data.get("pos_session")
        if pos_session and not self.context["request"].user.is_admin:
            if not pos_session.pos.employee_set.filter(
                user=self.context["request"].user
            ).exists():
                raise serializers.ValidationError(
                    "Cannot create return for POS session not assigned to you"
                )

        # Set warehouse from POS session if provided, otherwise use existing warehouse for updates
        if pos_session:
            data["warehouse"] = pos_session.pos.warehouse
        elif not self.instance:
            # For new instances without POS session, try to determine warehouse
            if not self.context["request"].user.is_admin:
                # Get warehouse from user's employee record
                employee = getattr(self.context["request"].user, "employee", None)
                if employee and employee.pos:
                    data["warehouse"] = employee.pos.warehouse
                else:
                    raise serializers.ValidationError(
                        "Cannot determine warehouse for return. Please specify a POS session."
                    )
        # For updates without pos_session, keep existing warehouse (no change needed)

        return data

    @transaction.atomic
    def create(self, validated_data):
        """
        Create a new PurchaseReturn with its associated items.
        """
        items_data = validated_data.pop("items", [])
        pos_session = validated_data.get("pos_session", None)

        # Create the purchase return
        purchase_return = PurchaseReturn.objects.create(**validated_data)

        # Create return items and calculate total
        total_amount = Decimal("0.00")
        product_total_cost = Decimal("0.00")
        for item_data in items_data:
            item_data["purchase_return"] = purchase_return
            item = PurchaseReturnItemSerializer().create(item_data)
            total_amount += item.total_cost
            product_total_cost += item.quantity * item.product.cost

        # Update total amount
        purchase_return.total_amount = total_amount
        purchase_return.product_total_cost = product_total_cost
        purchase_return.save(
            update_fields=["total_amount", "product_total_cost", "modified"]
        )

        # Update stock items (decrease quantities)
        self._update_stock_items(purchase_return)

        # Handle POS transaction if session provided
        if pos_session:
            self._handle_pos_transaction(purchase_return, pos_session)

        return purchase_return

    @transaction.atomic
    def update(self, instance, validated_data):
        """
        Update an existing PurchaseReturn with its associated items.
        """
        items_data = validated_data.pop("items", None)
        pos_session = validated_data.pop("pos_session", None)

        # Store old values for stock adjustment
        old_items = list(instance.items.all())

        # Update purchase return fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Handle items update if provided
        if items_data is not None:
            # Delete existing items
            instance.items.all().delete()

            # Create new items
            total_amount = Decimal("0.00")
            for item_data in items_data:
                item_data["purchase_return"] = instance
                item = PurchaseReturnItemSerializer().create(item_data)
                total_amount += item.total_cost

            # Update total amount
            instance.total_amount = total_amount

        instance.save()

        # Update stock items (reverse old changes and apply new ones)
        if items_data is not None:
            self._reverse_stock_changes(old_items)
            self._update_stock_items(instance)

        # Handle POS transaction update
        if pos_session:
            self._update_pos_transaction(instance, pos_session)

        return instance

    def _update_stock_items(self, purchase_return):
        """Decrease stock quantities for returned items."""
        for item in purchase_return.items.all():
            try:
                stock_item = StockItem.objects.get(
                    warehouse=purchase_return.warehouse, product=item.product
                )
                # Decrease stock quantity (return reduces available stock)
                stock_item.quantity -= item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                # If stock item doesn't exist, create it with negative quantity
                StockItem.objects.create(
                    warehouse=purchase_return.warehouse,
                    product=item.product,
                    quantity=-item.quantity,
                )

    def _reverse_stock_changes(self, old_items):
        """Reverse stock changes from old items (increase quantities back)."""
        for item in old_items:
            try:
                stock_item = StockItem.objects.get(
                    warehouse=item.purchase_return.warehouse, product=item.product
                )
                # Increase stock quantity back (reverse the decrease)
                stock_item.quantity += item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                pass  # If stock item doesn't exist, nothing to reverse

    def _handle_pos_transaction(self, purchase_return, pos_session):
        """Create POS session transaction for cash returns."""
        POSSessionTransaction.objects.create(
            session=pos_session,
            transaction_type=POSTransactionType.REFUND_PURCHASE,
            amount=purchase_return.total_amount,
            content_type=ContentType.objects.get_for_model(PurchaseReturn),
            object_id=purchase_return.id,
            description=f"Purchase return to {purchase_return.supplier.name}",
        )
        # Update session total expenses (returns reduce expenses)
        pos_session.save(update_fields=["total_expenses", "modified"])

    def _update_pos_transaction(self, purchase_return, pos_session):
        """Update existing POS session transaction."""
        try:
            pos_transaction = POSSessionTransaction.objects.get(
                content_type=ContentType.objects.get_for_model(PurchaseReturn),
                object_id=purchase_return.id,
            )
            old_amount = pos_transaction.amount
            pos_transaction.amount = purchase_return.total_amount
            pos_transaction.save(update_fields=["amount", "modified"])
            # Update session total sales
            pos_transaction.update_session_totals()
            pos_transaction.session.total_sales -= old_amount
            pos_transaction.session.save(update_fields=["total_sales", "modified"])

        except POSSessionTransaction.DoesNotExist:
            # Create new transaction if it doesn't exist
            self._handle_pos_transaction(purchase_return, pos_session)
