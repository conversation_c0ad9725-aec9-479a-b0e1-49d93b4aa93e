from decimal import Decimal

from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from pos.models import POSSession, POSSessionTransaction, TransactionType
from purchases_return.models import PurchaseReturn
from utils.test.base_test import BaseTestCase
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.product.product import ProductFactory
from utils.test.factories.purchases.purchase import PurchaseFactory
from utils.test.factories.purchases.purchase_item import PurchaseItemFactory
from utils.test.factories.purchases.purchase_return import PurchaseReturnFactory
from utils.test.factories.purchases.purchase_return_item import (
    PurchaseReturnItemFactory,
)
from utils.test.factories.purchases.supplier import SupplierFactory
from utils.test.factories.warehouse.stock_item import StockItemFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models import StockItem


class PurchaseReturnViewSetTestCase(BaseTestCase):
    """
    Test cases for PurchaseReturnViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("purchases_return:purchase-return-list")
        self.detail_url_name = "purchases_return:purchase-return-detail"

        # Create test suppliers
        self.supplier1 = SupplierFactory.create(name="Test Supplier 1")
        self.supplier2 = SupplierFactory.create(name="Test Supplier 2")

        # Create test POS sessions
        # Use the existing POS from base test setup
        self.pos_session1 = POSSessionFactory.create(
            pos=self.pos,  # Use existing POS from BaseTestCase
            status=POSSession.Status.OPEN,
        )
        # Create another warehouse for the second POS session
        self.warehouse2 = WarehouseFactory.create(name="Test Warehouse 2")
        self.pos_session2 = POSSessionFactory.create(
            pos__warehouse=self.warehouse2, status=POSSession.Status.OPEN
        )

        # Create test products
        self.product1 = ProductFactory.create(
            name="Test Product 1", cost=Decimal("10.00"), price=Decimal("15.00")
        )
        self.product2 = ProductFactory.create(
            name="Test Product 2", cost=Decimal("20.00"), price=Decimal("30.00")
        )

        # Create stock items for products
        self.stock1 = StockItemFactory.create(
            warehouse=self.warehouse, product=self.product1, quantity=Decimal("50.000")
        )
        self.stock2 = StockItemFactory.create(
            warehouse=self.warehouse, product=self.product2, quantity=Decimal("30.000")
        )

        # Create some purchases so we can return items (business rule validation)
        self.purchase1 = PurchaseFactory.create(
            warehouse=self.warehouse,
            supplier=self.supplier1,
            total_amount=Decimal("500.00"),
            net_amount=Decimal("500.00"),
            paid_amount=Decimal("500.00"),
        )

        # Create purchase items for the products we want to return
        self.purchase_item1 = PurchaseItemFactory.create(
            purchase=self.purchase1,
            product=self.product1,
            quantity=Decimal("10.00"),
            unit_cost=Decimal("12.00"),
            unit_price=Decimal("15.00"),
        )

        self.purchase_item2 = PurchaseItemFactory.create(
            purchase=self.purchase1,
            product=self.product2,
            quantity=Decimal("5.00"),
            unit_cost=Decimal("22.00"),
            unit_price=Decimal("30.00"),
        )

        # Create test purchase returns
        self.return1 = PurchaseReturnFactory.create(
            warehouse=self.warehouse,
            supplier=self.supplier1,
            payment_method=PurchaseReturn.PaymentMethod.CASH,
            notes="Test return 1",
        )

        self.return2 = PurchaseReturnFactory.create(
            warehouse=self.pos_session2.pos.warehouse,
            supplier=self.supplier2,
            payment_method=PurchaseReturn.PaymentMethod.CASH,
            notes="Test return 2",
        )

        # Create return items
        self.item1 = PurchaseReturnItemFactory.create(
            purchase_return=self.return1,
            product=self.product1,
            quantity=Decimal("5.00"),
            unit_cost=Decimal("12.00"),
        )

        self.item2 = PurchaseReturnItemFactory.create(
            purchase_return=self.return1,
            product=self.product2,
            quantity=Decimal("3.00"),
            unit_cost=Decimal("22.00"),
        )

        # Recalculate total amounts for the returns
        self.return1.save()  # This will trigger total_amount calculation
        self.return2.save()
        self.pos_transaction1 = POSSessionTransaction.objects.create(
            session=self.pos_session1,
            transaction_type=TransactionType.REFUND_PURCHASE,
            amount=self.return1.total_amount,
            related_object=self.return1,
            description=f"Return from {self.return1.supplier.name}",
        )
        self.pos_session1.refresh_from_db()
        self.return1.pos_session = self.pos_session1
        self.return1.save()

    def get_detail_url(self, return_id):
        """Helper method to get detail URL for a purchase return."""
        return reverse(self.detail_url_name, kwargs={"pk": return_id})

    # Authentication and Permission Tests
    def test_list_returns_unauthenticated(self):
        """Test that unauthenticated users cannot list returns."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_returns_as_admin(self):
        """Admin should see all returns."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should include our test returns
        self.assertGreaterEqual(len(response.data["results"]), 2)

    def test_list_returns_as_manager(self):
        """Manager should see all returns."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should include our test returns
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_list_returns_as_cashier(self):
        """Cashier should not be able to list returns."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_return_as_admin(self):
        """Admin should be able to retrieve any return."""
        url = self.get_detail_url(self.return1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check response data
        self.assertEqual(response.data["id"], self.return1.id)
        # Total amount should be calculated from items: (5*12 + 3*22) = 60 + 66 = 126
        # But we need to refresh the return to get the calculated total
        self.return1.refresh_from_db()
        expected_total = str(self.return1.total_amount)
        self.assertEqual(response.data["total_amount"], expected_total)
        self.assertEqual(response.data["payment_method"], "cash")
        self.assertEqual(response.data["notes"], "Test return 1")
        self.assertIn("warehouse_name", response.data)
        self.assertIn("supplier_name", response.data)
        self.assertIn("items", response.data)
        self.assertGreaterEqual(len(response.data["items"]), 2)

    def test_retrieve_return_as_manager(self):
        """Manager should be able to retrieve returns."""
        url = self.get_detail_url(self.return1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_return_as_cashier(self):
        """Cashier should not be able to retrieve returns."""
        url = self.get_detail_url(self.return1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_nonexistent_return(self):
        """Test retrieving a non-existent return."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_return_as_admin(self):
        """Admin should be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "New test return",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_cost": "12.00",
                    "notes": "Item 1",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "1.00",
                    "unit_cost": "22.00",
                    "notes": "Item 2",
                },
            ],
        }
        initial_return_count = PurchaseReturn.objects.count()
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity
        initial_pos_transaction_count = POSSessionTransaction.objects.count()
        initial_total_sales = self.pos_session1.total_sales
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Check that return was created
        self.assertEqual(PurchaseReturn.objects.count(), initial_return_count + 1)
        # Check response data
        purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
        self.assertEqual(purchase_return.supplier, self.supplier1)
        self.assertEqual(purchase_return.warehouse, self.pos_session1.pos.warehouse)
        self.assertEqual(purchase_return.payment_method, "cash")
        self.assertEqual(purchase_return.notes, "New test return")
        # Check that items were created
        self.assertEqual(purchase_return.items.count(), 2)
        # Check that total_amount was calculated (sum of item total_costs)
        # Item 1: 2 * 12 = 24, Item 2: 1 * 22 = 22, Total = 46
        expected_total_amount = Decimal("46.00")
        self.assertEqual(purchase_return.total_amount, expected_total_amount)
        # Item 1: 2 * 10 = 20, Item 2: 1 * 20 = 20, Total = 40
        expected_product_total_cost = Decimal("40.00")
        self.assertEqual(
            purchase_return.product_total_cost, expected_product_total_cost
        )

        # Check that stock was decreased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 - Decimal("2.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 - Decimal("1.000"))

        # Check that POS session transaction was created for cash return
        self.assertEqual(
            POSSessionTransaction.objects.count(), initial_pos_transaction_count + 1
        )
        pos_transaction = POSSessionTransaction.objects.filter(
            object_id=purchase_return.id
        ).first()
        self.assertIsNotNone(pos_transaction)
        self.assertEqual(pos_transaction.amount, purchase_return.total_amount)
        self.pos_session1.refresh_from_db()
        self.assertEqual(
            self.pos_session1.total_sales,
            initial_total_sales + purchase_return.total_amount,
        )

    def test_create_return_as_manager(self):
        """Manager should be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Manager return",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_return_as_cashier(self):
        """Cashier should not be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_return_unauthenticated(self):
        """Unauthenticated users should not be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_return_without_items(self):
        """Test creating return without items should fail."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Return without items",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"items": [ErrorDetail(string="This field is required.", code="required")]},
        )

    def test_create_return_empty_items_list(self):
        """Test creating return with empty items list should fail."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    ErrorDetail(
                        string="At least one return item is required.", code="invalid"
                    )
                ]
            },
        )

    def test_create_return_missing_required_fields(self):
        """Test creating return with missing required fields."""
        data = {
            "supplier_id": self.supplier1.id,
            "payment_method": "cash"
            # Missing pos_session_id and items
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_invalid_pos_session(self):
        """Test creating return with non-existent POS session."""
        data = {
            "pos_session_id": 99999,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "pos_session_id": [
                    ErrorDetail(
                        string='Invalid pk "99999" - object does not exist.',
                        code="does_not_exist",
                    )
                ]
            },
        )

    def test_create_return_closed_pos_session(self):
        """Test creating return with closed POS session should fail."""
        # Create a new warehouse for this test to avoid unique constraint issues
        test_warehouse = WarehouseFactory.create(
            name="Test Warehouse for Closed Session"
        )
        closed_session = POSSessionFactory.create(
            pos__warehouse=test_warehouse, status=POSSession.Status.CLOSED
        )

        data = {
            "pos_session_id": closed_session.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "pos_session_id": [
                    ErrorDetail(
                        string=f'Invalid pk "{closed_session.id}" - object does not exist.',
                        code="does_not_exist",
                    )
                ]
            },
        )

    def test_create_return_invalid_supplier(self):
        """Test creating return with non-existent supplier."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": 99999,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "supplier_id": [
                    ErrorDetail(
                        string='Invalid pk "99999" - object does not exist.',
                        code="does_not_exist",
                    )
                ]
            },
        )

    def test_create_return_invalid_item_data(self):
        """Test creating return with invalid item data."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "-1.00",  # Negative quantity
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "quantity": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.001.",
                                code="min_value",
                            )
                        ]
                    }
                ]
            },
        )

    def test_create_return_negative_unit_cost(self):
        """Test creating return with negative unit cost."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "-12.00",  # Negative unit cost
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "unit_cost": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ]
                    }
                ]
            },
        )

    def test_create_return_zero_quantity(self):
        """Test creating return with zero quantity."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "0.00",  # Zero quantity
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "quantity": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.001.",
                                code="min_value",
                            )
                        ]
                    }
                ]
            },
        )

    def test_create_return_zero_unit_cost(self):
        """Test creating return with zero unit cost."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "0.00",  # Zero unit cost
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "items": [
                    {
                        "unit_cost": [
                            ErrorDetail(
                                string="Ensure this value is greater than or equal to 0.01.",
                                code="min_value",
                            )
                        ]
                    }
                ]
            },
        )

    def test_create_return_invalid_payment_method(self):
        """Test creating return with invalid payment method."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "invalid_method",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {
                "payment_method": [
                    ErrorDetail(
                        string='"invalid_method" is not a valid choice.',
                        code="invalid_choice",
                    )
                ]
            },
        )

    # Update Tests
    def test_update_return_as_admin(self):
        """Admin should be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that return was updated
        self.return1.refresh_from_db()
        self.assertEqual(self.return1.payment_method, "cash")
        self.assertEqual(self.return1.notes, "Updated return notes")

        # Check that total amount was recalculated
        expected_total = Decimal("39.00")  # 3 * 13
        self.assertEqual(self.return1.total_amount, expected_total)

    def test_partial_update_return_as_admin(self):
        """Admin should be able to partially update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {"notes": "Partially updated notes", "payment_method": "card"}

        response = self.admin_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_update_return_as_manager(self):
        """Manager should be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }
        response = self.manager_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_return_as_cashier(self):
        """Cashier should not be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }
        response = self.cashier_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_return_unauthenticated(self):
        """Unauthenticated users should not be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }

        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_return_with_closed_pos_session(self):
        """Test updating return with closed POS session should fail."""
        # Create a new POS session for this test
        test_pos_session = POSSessionFactory.create(
            pos=self.pos, status=POSSession.Status.OPEN
        )

        # Create a return with POS session
        return_with_pos = PurchaseReturnFactory.create(
            warehouse=self.warehouse,
            supplier=self.supplier1,
            pos_session=test_pos_session,
        )

        # Close the POS session
        test_pos_session.status = POSSession.Status.CLOSED
        test_pos_session.save()

        url = self.get_detail_url(return_with_pos.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            {"detail": "Cannot update return for closed POS session"}, response.data
        )

    def test_update_nonexistent_return(self):
        """Test updating a non-existent return."""
        url = self.get_detail_url(99999)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                }
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_return_items_stock_adjustment(self):
        """Test that updating return items properly adjusts stock."""
        # Get initial stock quantities
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",  # Changed from 5.00 to 2.00
                    "unit_cost": "12.00",
                }
                # Removed product2 item entirely
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check stock adjustments
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()

        # Stock1: should have been restored by 5.00 (old quantity) and decreased by 2.00 (new quantity)
        # Net effect: +3.00 from initial
        expected_stock1 = initial_stock1 + Decimal("3.000")
        self.assertEqual(self.stock1.quantity, expected_stock1)

        # Stock2: should have been restored by 3.00 (old quantity) with no new decrease
        # Net effect: +3.00 from initial
        expected_stock2 = initial_stock2 + Decimal("3.000")
        self.assertEqual(self.stock2.quantity, expected_stock2)

    def test_update_return_pos_transaction_updated(self):
        """Test that updating a return properly updates POS transactions."""
        # Get initial POS transaction amount and session total_expenses
        initial_pos_transaction_amount = self.pos_transaction1.amount
        initial_total_salse = self.pos_session1.total_sales
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",  # Changed from 5.00 to 2.00
                    "unit_cost": "12.00",
                }
                # Removed product2 item entirely
            ],
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.pos_transaction1.refresh_from_db()
        self.assertEqual(self.pos_transaction1.amount, Decimal("24.00"))
        self.pos_session1.refresh_from_db()
        self.assertEqual(
            self.pos_session1.total_sales,
            initial_total_salse
            - initial_pos_transaction_amount
            + self.pos_transaction1.amount,
        )

    # Delete Tests
    def test_delete_return_as_admin(self):
        """Admin should be able to delete returns."""
        url = self.get_detail_url(self.return2.id)
        initial_count = PurchaseReturn.objects.count()

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that return was deleted
        self.assertEqual(PurchaseReturn.objects.count(), initial_count - 1)
        self.assertFalse(PurchaseReturn.objects.filter(id=self.return2.id).exists())

    def test_delete_return_as_manager(self):
        """Manager should be able to delete returns."""
        url = self.get_detail_url(self.return1.id)

        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_delete_return_as_cashier(self):
        """Cashier should not be able to delete returns."""
        url = self.get_detail_url(self.return1.id)

        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_return_unauthenticated(self):
        """Unauthenticated users should not be able to delete returns."""
        url = self.get_detail_url(self.return1.id)

        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_return_with_closed_pos_session(self):
        """Test deleting return with closed POS session should fail."""
        # Create a new POS session for this test
        test_pos_session = POSSessionFactory.create(
            pos=self.pos, status=POSSession.Status.OPEN
        )

        # Create a return with POS session
        return_with_pos = PurchaseReturnFactory.create(
            warehouse=self.warehouse,
            supplier=self.supplier1,
            pos_session=test_pos_session,
        )

        # Close the POS session
        test_pos_session.status = POSSession.Status.CLOSED
        test_pos_session.save()

        url = self.get_detail_url(return_with_pos.id)

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Cannot delete return for closed POS session", str(response.data))

    def test_delete_nonexistent_return(self):
        """Test deleting a non-existent return."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_return_stock_restoration(self):
        """Test that deleting a return restores stock quantities."""
        # Create a new return to delete
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "5.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        # Create the return
        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        return_id = response.data["id"]

        # Get stock after creation
        self.stock1.refresh_from_db()
        stock_after_creation = self.stock1.quantity

        # Delete the return
        url = self.get_detail_url(return_id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that stock was restored
        self.stock1.refresh_from_db()
        expected_stock = stock_after_creation + Decimal("5.000")
        self.assertEqual(self.stock1.quantity, expected_stock)

    def test_delete_return_pos_transaction_removal(self):
        """Test that deleting a return restores stock quantities."""
        initial_pos_transaction_amount = self.pos_transaction1.amount
        initial_total_sales = self.pos_session1.total_sales
        # Delete the return
        url = self.get_detail_url(self.return1.id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            POSSessionTransaction.objects.filter(id=self.pos_transaction1.id).exists()
        )
        self.pos_session1.refresh_from_db()
        self.assertEqual(
            self.pos_session1.total_sales,
            initial_total_sales - initial_pos_transaction_amount,
        )

    # Business Logic Tests - Stock Management
    def test_stock_decrease_on_create(self):
        """Test that stock is decreased when creating a return."""
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "5.00",
                    "unit_cost": "12.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "3.00",
                    "unit_cost": "22.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that stock was decreased
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 - Decimal("5.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 - Decimal("3.000"))

    def test_stock_creation_for_nonexistent_stock_item(self):
        """Test that stock item is created with negative quantity if it doesn't exist."""
        # Create a product without stock item
        new_product = ProductFactory.create(name="New Product")

        # Create purchase for this product so it can be returned
        purchase_item = PurchaseItemFactory.create(
            purchase=self.purchase1,
            product=new_product,
            quantity=Decimal("2.00"),
            unit_cost=Decimal("15.00"),
            unit_price=Decimal("20.00"),
        )

        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {"product_id": new_product.id, "quantity": "1.00", "unit_cost": "15.00"}
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that stock item was created with negative quantity
        stock_item = StockItem.objects.get(
            warehouse=self.warehouse, product=new_product
        )
        self.assertEqual(stock_item.quantity, Decimal("-1.000"))

    def test_pos_transaction_removal_on_delete(self):
        """Test that POS transactions are removed when returns are deleted."""
        # Get initial POS session expenses before creating return
        initial_total_expenses = self.pos_session1.total_expenses

        # Create a cash return with POS transaction
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        return_id = response.data["id"]

        # Verify POS transaction exists
        purchase_return = PurchaseReturn.objects.get(id=return_id)
        pos_transaction = POSSessionTransaction.objects.filter(
            object_id=purchase_return.id
        ).first()
        self.assertIsNotNone(pos_transaction)

        # Delete the return
        url = self.get_detail_url(return_id)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that POS transaction was removed
        pos_transaction_exists = POSSessionTransaction.objects.filter(
            object_id=return_id
        ).exists()
        self.assertFalse(pos_transaction_exists)

        # Check that POS session total_expenses was restored to initial value
        self.pos_session1.refresh_from_db()
        # After deletion, expenses should be restored to initial value
        self.assertEqual(self.pos_session1.total_expenses, initial_total_expenses)

    # Return Items Tests
    def test_return_items_creation(self):
        """Test that return items are created correctly with nested serializer."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "12.00",
                    "notes": "Special return item note",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that item was created with correct values
        purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
        item = purchase_return.items.first()
        self.assertEqual(item.product, self.product1)
        self.assertEqual(item.quantity, Decimal("3.00"))
        self.assertEqual(item.unit_cost, Decimal("12.00"))
        self.assertEqual(item.total_cost, Decimal("36.00"))  # 3 * 12
        self.assertEqual(item.notes, "Special return item note")

    def test_total_amount_calculation(self):
        """Test that total_amount is calculated correctly from items."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.50",
                    "unit_cost": "10.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "1.25",
                    "unit_cost": "20.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
        # Item 1: 2.50 * 10.00 = 25.00
        # Item 2: 1.25 * 20.00 = 25.00
        # Total: 50.00
        expected_total = Decimal("50.00")
        self.assertEqual(purchase_return.total_amount, expected_total)
        self.assertEqual(response.data["total_amount"], "50.00")

    # Edge Cases and Validation Tests
    def test_return_with_large_amounts(self):
        """Test handling of large return amounts."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1000.00",
                    "unit_cost": "999.99",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        if response.status_code == status.HTTP_201_CREATED:
            purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
            expected_total = Decimal("999990.00")  # 1000 * 999.99
            self.assertEqual(purchase_return.total_amount, expected_total)

    def test_precision_handling(self):
        """Test that decimal precision is handled correctly."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.123",  # 3 decimal places for quantity
                    "unit_cost": "12.999",  # 3 decimal places for unit_cost
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        if response.status_code == status.HTTP_201_CREATED:
            purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
            item = purchase_return.items.first()
            self.assertEqual(item.quantity, Decimal("1.123"))
            # Unit cost should be rounded to 2 decimal places
            self.assertEqual(item.unit_cost, Decimal("13.00"))
            # Total cost should be calculated correctly
            expected_total = Decimal("14.60")  # 1.123 * 13.00 rounded
            self.assertEqual(item.total_cost, expected_total)

    def test_return_without_pos_session(self):
        """Test creating return without POS session (should use warehouse from user)."""
        # This test would need to be adjusted based on how the serializer handles
        # warehouse determination for non-admin users without POS session
        data = {
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_cost": "12.00",
                }
            ],
        }

        # For admin users, this might fail because warehouse can't be determined
        response = self.admin_client.post(self.list_url, data, format="json")
        # The exact behavior depends on the serializer implementation
        # This test documents the expected behavior

    def test_multiple_items_same_product(self):
        """Test creating return with multiple items for the same product."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "supplier_id": self.supplier1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_cost": "12.00",
                    "notes": "First batch",
                },
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_cost": "13.00",
                    "notes": "Second batch",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        purchase_return = PurchaseReturn.objects.get(id=response.data["id"])
        self.assertEqual(purchase_return.items.count(), 2)

        # Total should be (2*12) + (3*13) = 24 + 39 = 63
        expected_total = Decimal("63.00")
        self.assertEqual(purchase_return.total_amount, expected_total)

        # Stock should be decreased by total quantity: 2 + 3 = 5
        self.stock1.refresh_from_db()
        # Note: This assumes initial stock was not affected by other tests

    # Model String Representation Tests
    def test_return_string_representation(self):
        """Test the string representation of PurchaseReturn model."""
        expected_str = f"Return {self.return1.id} - {self.return1.supplier.name} - ${self.return1.total_amount}"
        self.assertEqual(str(self.return1), expected_str)

    def test_return_item_string_representation(self):
        """Test the string representation of PurchaseReturnItem model."""
        expected_str = f"{self.item1.quantity} x {self.item1.product.name} - ${self.item1.total_cost}"
        self.assertEqual(str(self.item1), expected_str)

    # Search and Filtering Tests
    def test_search_returns_by_supplier_name(self):
        """Test searching returns by supplier name."""
        url = f"{self.list_url}?search={self.supplier1.name}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return returns from supplier1
        supplier_names = [
            result["supplier_name"] for result in response.data["results"]
        ]
        self.assertIn(self.supplier1.name, supplier_names)

    def test_search_returns_by_notes(self):
        """Test searching returns by notes."""
        url = f"{self.list_url}?search=Test return 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return the return with matching notes
        notes_list = [result["notes"] for result in response.data["results"]]
        self.assertIn("Test return 1", notes_list)

    def test_ordering_returns(self):
        """Test ordering returns by different fields."""
        # Test ordering by created date (default)
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test ordering by total_amount
        url = f"{self.list_url}?ordering=total_amount"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test ordering by supplier name
        url = f"{self.list_url}?ordering=supplier__name"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    # Pagination Tests
    def test_pagination(self):
        """Test that pagination works correctly."""
        # Create additional returns to test pagination (create enough to exceed page size)
        for i in range(25):
            PurchaseReturnFactory.create(
                warehouse=self.warehouse, supplier=self.supplier1
            )

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

        # Should have more items than displayed on first page
        total_count = response.data["count"]
        results_count = len(response.data["results"])

        # If pagination is working, either we have a next page or all items fit on one page
        if response.data["next"]:
            self.assertGreater(total_count, results_count)
        else:
            # All items fit on one page, which is also valid
            self.assertEqual(total_count, results_count)

    def tearDown(self):
        """Clean up after tests."""
        # Clean up any test data if needed
        super().tearDown()
