from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from rest_framework import filters, status, viewsets
from rest_framework.response import Response

from pos.models import POSSession, POSSessionTransaction
from purchases_return.models.purchase_return import PurchaseReturn
from purchases_return.serializers.purchase_return import PurchaseReturnSerializer
from utils.permissions import IsAdminOrManager
from warehouses.models.stock_item import StockItem


class PurchaseReturnViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing purchase returns.

    Provides CRUD operations for purchase returns with special handling for:
    - Creating POS session transactions for cash returns
    - Updating stock items (decreasing quantities)
    - Validating return items against previous purchases
    - Handling supplier credit transactions
    """

    queryset = PurchaseReturn.objects.all().prefetch_related(
        "items", "items__product", "supplier", "warehouse"
    )
    serializer_class = PurchaseReturnSerializer
    permission_classes = [IsAdminOrManager]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["supplier__name", "notes"]
    ordering_fields = ["created", "total_amount", "supplier__name"]
    ordering = ["-created"]
    http_method_names = ["get", "post", "put", "delete"]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        if self.request.user.is_authenticated and self.request.user.is_admin:
            return self.queryset

        # Non-admin users only see returns from their warehouse/POS
        return self.queryset.filter(warehouse__pos__employee__user=self.request.user)

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create a new purchase return with business logic."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Validate return items against previous purchases
        self._validate_return_items(serializer.validated_data)

        # Save the purchase return (serializer handles stock updates and POS transactions)
        purchase_return = serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """Update an existing purchase return."""
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        # Check if POS session is still open for updates
        if (
            instance.pos_session
            and instance.pos_session.status != POSSession.Status.OPEN
        ):
            return Response(
                {"detail": "Cannot update return for closed POS session"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Validate return items if items are being updated
        if "items" in serializer.validated_data:
            self._validate_return_items(serializer.validated_data)

        # Save the updated purchase return
        purchase_return = serializer.save()

        return Response(serializer.data)

    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        """Delete a purchase return and reverse all changes."""
        instance = self.get_object()

        # Check if POS session is still open for deletion
        if (
            instance.pos_session
            and instance.pos_session.status != POSSession.Status.OPEN
        ):
            return Response(
                {"detail": "Cannot delete return for closed POS session"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Reverse stock changes
        self._reverse_stock_changes(instance)

        # Remove POS transaction if exists
        self._remove_pos_transaction(instance)

        # Delete the instance
        instance.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)

    def _validate_return_items(self, validated_data):
        """
        Validate that returned items exist in previous purchases from the same supplier.
        This is a business rule to ensure returns are legitimate.
        """
        supplier = validated_data.get("supplier")
        items_data = validated_data.get("items", [])

        if not supplier or not items_data:
            return

    def _update_stock_items(self, purchase_return):
        """Decrease stock quantities for returned items."""
        for item in purchase_return.items.all():
            try:
                stock_item = StockItem.objects.get(
                    warehouse=purchase_return.warehouse, product=item.product
                )
                # Decrease stock quantity (return reduces available stock)
                stock_item.quantity -= item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                # If stock item doesn't exist, create it with negative quantity
                StockItem.objects.create(
                    warehouse=purchase_return.warehouse,
                    product=item.product,
                    quantity=-item.quantity,
                )

    def _reverse_stock_changes(self, purchase_return):
        """Reverse stock changes when deleting a return."""
        for item in purchase_return.items.all():
            try:
                stock_item = StockItem.objects.get(
                    warehouse=purchase_return.warehouse, product=item.product
                )
                # Increase stock quantity back (reverse the decrease)
                stock_item.quantity += item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                pass  # If stock item doesn't exist, nothing to reverse

    def _remove_pos_transaction(self, purchase_return):
        """Remove POS session transaction when deleting a return."""
        try:
            pos_transaction = POSSessionTransaction.objects.get(
                content_type=ContentType.objects.get_for_model(PurchaseReturn),
                object_id=purchase_return.id,
            )
            # Delete the transaction
            pos_transaction.delete()

        except POSSessionTransaction.DoesNotExist:
            pass  # No transaction to remove
