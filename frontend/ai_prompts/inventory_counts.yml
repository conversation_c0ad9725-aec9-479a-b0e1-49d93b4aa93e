GenerateInventoryCountPages:
  description: |
    In the `frontend/` project, generate CRUD pages for the `inventory_count` feature using the 'inventory-counts/' and 'inventory-counts/<id>/items/' APIs.
    You should use "react-bootstrap" to create the UI, similar to the "warehouses/" pages.
  rules:
    - name: Component Structure
      description: |
        - Create a new directory `src/components/inventory-counts/`
        - Create the following components:
          - `InventoryCountsList.jsx` - For listing all inventory counts
          - `InventoryCountView.jsx` - For viewing a single inventory count
          - `InventoryCountCreate.jsx` - For creating a new inventory count
          - `InventoryCountForm.jsx` - Reusable form component
          - `DeleteConfirmModal.jsx` - Confirmation dialog for deletion

    - name: API Integration
      description: |
        - Create a new hook `src/hooks/useInventoryCounts.js` for API calls
        - Include methods for CRUD operations:
          - fetchInventoryCounts
          - fetchInventoryCount
          - createInventoryCount
          - updateInventoryCount
          - deleteInventoryCount
          - completeInventoryCount
          - cancelInventoryCount

        - Create a new hook `src/hooks/useInventoryCountItems.js` for inventory count items API calls:
          - fetchInventoryCountItems(inventoryCountId)
          - fetchInventoryCountItem(inventoryCountId, itemId)
          - createInventoryCountItem(inventoryCountId, itemData)
          - updateInventoryCountItem(inventoryCountId, itemId, itemData)
          - bulkUpdateInventoryCountItems(inventoryCountId, itemsData)
          - deleteInventoryCountItem(inventoryCountId, itemId)

    - name: List View (InventoryCountsList)
      description: |
        - Display a table of inventory counts with columns:
          - ID
          - Warehouse
          - Status (with color coding)
          - Started At
          - Ended At
          - Item Count
          - Total Cost
          - Total Price
          - Actions (View, Edit, Delete)
        - Add search and filter functionality
        - Add pagination
        - Add "New Inventory Count" button

    - name: View Page (InventoryCountView)
      description: |
        - Display all details of a single inventory count
        - Show a table of counted items with:
          - Product Name (with link to product)
          - Product Code
          - System Quantity (readonly)
          - Recorded Quantity (editable field)
          - Difference (color-coded, calculated automatically)
            - Red for negative differences (shortages)
            - Green for positive differences (surplus)
          - Unit Type
          - Notes (editable field)
        - Add bulk edit functionality for recorded quantities
        - Add buttons for:
          - Save Changes (saves all item updates)
          - Complete Count (marks as completed)
          - Cancel Count (marks as cancelled)
          - Back to List
        - Show summary statistics:
          - Total Items Counted
          - Items with Discrepancies
          - Total Cost Difference
          - Total Price Difference

    - name: Create/Edit Form (InventoryCountForm)
      description: |
        - Form fields:
          - Warehouse (dropdown, required)
          - Status (readonly, derived from workflow)
          - Notes (textarea)
        - On create:
          - Automatically create inventory count items for all stock items in the warehouse
          - Set initial status to 'Draft'
        - Form validation
        - Error handling

    - name: Inventory Count Items Management
      description: |
        - Component: `InventoryCountItemsTable` (src/components/inventory-counts/InventoryCountItemsTable.jsx)
          - Displays all items in the inventory count in a sortable and searchable table
          - Columns:
            - Product (name, code, with link to product)
            - System Quantity (readonly)
            - Recorded Quantity (editable field)
            - Difference (auto-calculated, color-coded)
            - Unit Type
            - Notes (editable)
            - Actions (Edit, Delete)
          - Features:
            - Inline editing of recorded quantities and notes
            - Color-coded differences (red for shortages, green for surplus)
            - Search by product name or code
            - Sort by any column
            - Pagination
            - Row count selector (10/25/50/100 items per page)
            - Status indicators (loading, error states)

        - Component: `InventoryCountItemForm` (src/components/inventory-counts/InventoryCountItemForm.jsx)
          - Reusable form for adding/editing inventory count items
          - Fields:
            - Product (dropdown with search, disabled in edit mode)
            - System Quantity (readonly)
            - Recorded Quantity (number input, required)
            - Unit Type (readonly, based on product)
            - Notes (textarea)
          - Validation:
            - Recorded quantity must be a non-negative number
            - Required field validation
            - Server-side error handling
          - Props:
            - initialData: Object - Initial form values
            - onSubmit: Function - Form submission handler
            - onCancel: Function - Cancel handler
            - loading: Boolean - Loading state
            - error: String - Error message
            - isEdit: Boolean - Whether in edit mode

        - Component: `DeleteInventoryCountItemModal` (src/components/inventory-counts/DeleteInventoryCountItemModal.jsx)
          - Confirmation dialog for deleting an inventory count item
          - Shows item details for confirmation
          - Props:
            - show: Boolean - Whether to show the modal
            - onHide: Function - Hide handler
            - onConfirm: Function - Confirmation handler
            - item: Object - The item to be deleted
            - loading: Boolean - Loading state

        - Bulk Operations:
          - Bulk update recorded quantities:
            - Select multiple items
            - Set quantity for all selected
            - Apply to selected items
          - Filtering:
            - By discrepancy status (all, with discrepancies, exact matches)
            - By product category
          - Export:
            - Export to CSV/Excel
            - Print view
            - Custom column selection

        - Integration with useInventoryCountItems hook:
          - Fetch items with pagination, search, and filters
          - Handle loading and error states
          - Provide CRUD operations
          - Cache invalidation on updates

    - name: Delete Confirmation
      description: |
        - Show a confirmation dialog before deleting
        - Disable delete for completed/cancelled counts

    - name: Status Workflow
      description: |
        - Draft:
          - Can be edited, deleted
          - Items can be added/removed
          - No restrictions on changes

        - In Progress:
          - Can be completed or cancelled
          - Recorded quantities can be updated
          - No items can be added/removed
          - Shows real-time discrepancy calculations

        - Completed:
          - Read-only view
          - Shows final counts and discrepancies
          - Stock levels are updated based on recorded quantities

        - Cancelled:
          - Read-only view
          - No changes to stock levels
          - Can be used as reference

    - name: UI/UX
      description: |
        - Use Bootstrap components consistently
        - Add loading states
        - Show success/error messages
        - Add confirmation dialogs for important actions
        - Make it responsive
        - Add proper navigation

    - name: Routing
      description: |
        - Add routes in App.jsx:
          - `/inventory-counts` - List view
          - `/inventory-counts/create` - Create new
          - `/inventory-counts/:id` - View details
          - `/inventory-counts/:id/edit` - Edit

    - name: Permissions
      description: |
        - Only admin and manager roles can create/edit/delete
        - All authenticated users can view

    - name: Error Handling
      description: |
        - Handle API errors gracefully
        - Show user-friendly error messages
        - Log detailed errors to console in development
        - Specific error handling for:
          - Concurrent modifications
          - Invalid quantities
          - Unauthorized actions
          - Network issues
          - Server errors
        - Automatic retry for failed requests
        - Offline support with queued changes

    - name: Testing
      description: |
        - Test all CRUD operations
        - Test form validation
        - Test error cases
        - Test permissions
        - Test responsive behavior
