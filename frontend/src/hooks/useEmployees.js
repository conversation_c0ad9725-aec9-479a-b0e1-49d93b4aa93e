import { useState, useEffect, useCallback } from 'react';
import employeesService from '../services/employeesService';

/**
 * Custom hook for managing employees data and operations
 */
export const useEmployees = () => {
    const [employees, setEmployees] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10,
        total_pages: 1
    });

    /**
     * Fetch employees with optional parameters
     */
    const fetchEmployees = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await employeesService.getEmployees(params);
            setEmployees(response.data.results || []);

            // Update pagination info
            setPagination({
                count: response.data.count || 0,
                next: response.data.next,
                previous: response.data.previous,
                page: params.page || 1,
                page_size: params.page_size || 10,
                total_pages: Math.ceil((response.data.count || 0) / (params.page_size || 10))
            });
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch employees');
            console.error('Error fetching employees:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new employee
     */
    const createEmployee = useCallback(async (employeeData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await employeesService.createEmployee(employeeData);

            // Add the new employee to the list (optimistic update)
            setEmployees(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create employee';
            setError(errorMessage);
            console.error('Error creating employee:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing employee
     */
    const updateEmployee = useCallback(async (id, employeeData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await employeesService.updateEmployee(id, employeeData);

            // Update the employee in the list (optimistic update)
            setEmployees(prev =>
                prev.map(employee =>
                    employee.id === id ? response.data : employee
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update employee';
            setError(errorMessage);
            console.error('Error updating employee:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete an employee
     */
    const deleteEmployee = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await employeesService.deleteEmployee(id);

            // Remove the employee from the list (optimistic update)
            setEmployees(prev => prev.filter(employee => employee.id !== id));

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete employee';
            setError(errorMessage);
            console.error('Error deleting employee:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        employees,
        loading,
        error,
        pagination,
        fetchEmployees,
        createEmployee,
        updateEmployee,
        deleteEmployee,
        setError
    };
};

/**
 * Custom hook for managing a single employee
 */
export const useEmployee = (id) => {
    const [employee, setEmployee] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchEmployee = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await employeesService.getEmployee(id);
            setEmployee(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch employee');
            console.error('Error fetching employee:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchEmployee();
    }, [fetchEmployee]);

    return {
        employee,
        loading,
        error,
        refetch: fetchEmployee
    };
};

/**
 * Custom hook for employee type options
 */
export const useEmployeeTypes = () => {
    return employeesService.getEmployeeTypes();
};

/**
 * Custom hook for employee status options
 */
export const useEmployeeStatuses = () => {
    return employeesService.getEmployeeStatuses();
};

/**
 * Custom hook for user role options
 */
export const useUserRoles = () => {
    return employeesService.getUserRoles();
};
