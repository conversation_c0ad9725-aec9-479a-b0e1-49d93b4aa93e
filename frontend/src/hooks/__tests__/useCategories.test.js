import { renderHook, act } from '@testing-library/react';
import { useCategories, useCategory, useParentCategories } from '../useCategories';
import { categoriesService } from '../../services/categoriesService';

// Mock the categoriesService
jest.mock('../../services/categoriesService');

describe('useCategories', () => {
    const mockCategories = [
        { id: 1, name: 'Electronics', description: 'Electronic devices' },
        { id: 2, name: 'Clothing', description: 'Apparel and accessories' }
    ];

    const mockPaginatedResponse = {
        data: {
            results: mockCategories,
            count: 2,
            next: null,
            previous: null
        }
    };

    beforeEach(() => {
        categoriesService.getCategories.mockResolvedValue(mockPaginatedResponse);
        categoriesService.createCategory.mockResolvedValue({ data: mockCategories[0] });
        categoriesService.updateCategory.mockResolvedValue({ data: mockCategories[0] });
        categoriesService.deleteCategory.mockResolvedValue({});
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('initializes with default state', () => {
        const { result } = renderHook(() => useCategories());

        expect(result.current.categories).toEqual([]);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe(null);
        expect(result.current.pagination).toEqual({
            count: 0,
            next: null,
            previous: null,
            page: 1,
            page_size: 10
        });
    });

    test('fetchCategories updates state correctly', async () => {
        const { result } = renderHook(() => useCategories());

        await act(async () => {
            await result.current.fetchCategories();
        });

        expect(result.current.categories).toEqual(mockCategories);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe(null);
        expect(result.current.pagination.count).toBe(2);
    });

    test('fetchCategories handles errors', async () => {
        const errorMessage = 'Network error';
        categoriesService.getCategories.mockRejectedValue({
            response: { data: { detail: errorMessage } }
        });

        const { result } = renderHook(() => useCategories());

        await act(async () => {
            await result.current.fetchCategories();
        });

        expect(result.current.error).toBe(errorMessage);
        expect(result.current.loading).toBe(false);
    });

    test('createCategory calls service and refreshes data', async () => {
        const { result } = renderHook(() => useCategories());
        const newCategory = { name: 'New Category', description: 'New description' };

        await act(async () => {
            await result.current.createCategory(newCategory);
        });

        expect(categoriesService.createCategory).toHaveBeenCalledWith(newCategory);
        expect(categoriesService.getCategories).toHaveBeenCalled();
    });

    test('updateCategory updates local state', async () => {
        const { result } = renderHook(() => useCategories());

        // First fetch categories
        await act(async () => {
            await result.current.fetchCategories();
        });

        const updatedCategory = { ...mockCategories[0], name: 'Updated Electronics' };
        categoriesService.updateCategory.mockResolvedValue({ data: updatedCategory });

        await act(async () => {
            await result.current.updateCategory(1, { name: 'Updated Electronics' });
        });

        expect(categoriesService.updateCategory).toHaveBeenCalledWith(1, { name: 'Updated Electronics' });
        expect(result.current.categories[0].name).toBe('Updated Electronics');
    });

    test('deleteCategory removes item from state', async () => {
        const { result } = renderHook(() => useCategories());

        // First fetch categories
        await act(async () => {
            await result.current.fetchCategories();
        });

        expect(result.current.categories).toHaveLength(2);

        await act(async () => {
            await result.current.deleteCategory(1);
        });

        expect(categoriesService.deleteCategory).toHaveBeenCalledWith(1);
        expect(categoriesService.getCategories).toHaveBeenCalled();
    });
});

describe('useCategory', () => {
    const mockCategory = { id: 1, name: 'Electronics', description: 'Electronic devices' };

    beforeEach(() => {
        categoriesService.getCategory.mockResolvedValue({ data: mockCategory });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('fetches category on mount', async () => {
        const { result } = renderHook(() => useCategory(1));

        expect(result.current.loading).toBe(true);

        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(categoriesService.getCategory).toHaveBeenCalledWith(1);
        expect(result.current.category).toEqual(mockCategory);
        expect(result.current.loading).toBe(false);
    });

    test('does not fetch if no id provided', () => {
        renderHook(() => useCategory(null));
        expect(categoriesService.getCategory).not.toHaveBeenCalled();
    });

    test('handles fetch errors', async () => {
        const errorMessage = 'Category not found';
        categoriesService.getCategory.mockRejectedValue({
            response: { data: { detail: errorMessage } }
        });

        const { result } = renderHook(() => useCategory(1));

        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(result.current.error).toBe(errorMessage);
        expect(result.current.loading).toBe(false);
    });
});

describe('useParentCategories', () => {
    const mockParentCategories = [
        { id: 1, name: 'Electronics' },
        { id: 2, name: 'Clothing' }
    ];

    beforeEach(() => {
        categoriesService.getParentCategories.mockResolvedValue({
            data: { results: mockParentCategories }
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('fetches parent categories on mount', async () => {
        const { result } = renderHook(() => useParentCategories());

        expect(result.current.loading).toBe(true);

        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(categoriesService.getParentCategories).toHaveBeenCalled();
        expect(result.current.parentCategories).toEqual(mockParentCategories);
        expect(result.current.loading).toBe(false);
    });

    test('handles fetch errors', async () => {
        const errorMessage = 'Failed to fetch parent categories';
        categoriesService.getParentCategories.mockRejectedValue({
            response: { data: { detail: errorMessage } }
        });

        const { result } = renderHook(() => useParentCategories());

        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 0));
        });

        expect(result.current.error).toBe(errorMessage);
        expect(result.current.loading).toBe(false);
    });
});
