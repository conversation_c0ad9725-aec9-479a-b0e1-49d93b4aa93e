import { useState, useEffect, useCallback } from 'react';
import attendanceService from '../services/attendanceService';

/**
 * Custom hook for managing attendance data and operations
 */
export const useAttendance = () => {
    const [attendanceRecords, setAttendanceRecords] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10,
        total_pages: 1
    });

    /**
     * Fetch attendance records with optional parameters
     */
    const fetchAttendanceRecords = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.getAttendanceRecords(params);
            setAttendanceRecords(response.data.results || []);

            // Update pagination info
            setPagination({
                count: response.data.count || 0,
                next: response.data.next,
                previous: response.data.previous,
                page: params.page || 1,
                page_size: params.page_size || 10,
                total_pages: Math.ceil((response.data.count || 0) / (params.page_size || 10))
            });
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch attendance records');
            console.error('Error fetching attendance records:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Record time-in for an employee
     */
    const recordTimeIn = useCallback(async (timeInData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.recordTimeIn(timeInData);

            // Add the new attendance record to the list (optimistic update)
            setAttendanceRecords(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to record time-in';
            setError(errorMessage);
            console.error('Error recording time-in:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Record time-out for an employee
     */
    const recordTimeOut = useCallback(async (timeOutData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.recordTimeOut(timeOutData);

            // Update the attendance record in the list (optimistic update)
            setAttendanceRecords(prev =>
                prev.map(record =>
                    record.id === response.data.id ? response.data : record
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to record time-out';
            setError(errorMessage);
            console.error('Error recording time-out:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Get attendance records for a specific employee
     */
    const fetchEmployeeAttendance = useCallback(async (employeeId, params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.getEmployeeAttendance(employeeId, params);
            setAttendanceRecords(response.data.results || []);

            // Update pagination info
            setPagination({
                count: response.data.count || 0,
                next: response.data.next,
                previous: response.data.previous,
                page: params.page || 1,
                page_size: params.page_size || 10,
                total_pages: Math.ceil((response.data.count || 0) / (params.page_size || 10))
            });
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch employee attendance');
            console.error('Error fetching employee attendance:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        attendanceRecords,
        loading,
        error,
        pagination,
        fetchAttendanceRecords,
        recordTimeIn,
        recordTimeOut,
        fetchEmployeeAttendance,
        setError
    };
};

/**
 * Custom hook for managing a single attendance record
 */
export const useAttendanceRecord = (id) => {
    const [attendanceRecord, setAttendanceRecord] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchAttendanceRecord = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.getAttendanceRecord(id);
            setAttendanceRecord(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch attendance record');
            console.error('Error fetching attendance record:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchAttendanceRecord();
    }, [fetchAttendanceRecord]);

    return {
        attendanceRecord,
        loading,
        error,
        refetch: fetchAttendanceRecord
    };
};

/**
 * Custom hook for attendance statistics
 */
export const useAttendanceStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchAttendanceStats = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.getAttendanceStats(params);
            setStats(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch attendance statistics');
            console.error('Error fetching attendance statistics:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        stats,
        loading,
        error,
        fetchAttendanceStats
    };
};

/**
 * Custom hook for attendance calendar data
 */
export const useAttendanceCalendar = () => {
    const [calendarData, setCalendarData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchAttendanceCalendar = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await attendanceService.getAttendanceCalendar(params);
            setCalendarData(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch attendance calendar');
            console.error('Error fetching attendance calendar:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        calendarData,
        loading,
        error,
        fetchAttendanceCalendar
    };
};
