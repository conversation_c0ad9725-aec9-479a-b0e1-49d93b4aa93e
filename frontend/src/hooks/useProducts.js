import { useState, useCallback, useEffect } from 'react';
import { productsService } from '../services/productsService';

/**
 * Custom hook for managing products CRUD operations
 */
export const useProducts = () => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch products with optional parameters
     */
    const fetchProducts = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await productsService.getProducts(params);

            setProducts(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch products');
            console.error('Error fetching products:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new product
     */
    const createProduct = useCallback(async (productData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await productsService.createProduct(productData);

            await fetchProducts({ page: pagination.page, page_size: pagination.page_size });

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create product';
            setError(errorMessage);
            console.error('Error creating product:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing product
     */
    const updateProduct = useCallback(async (id, productData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await productsService.updateProduct(id, productData);

            // Update the product in the local state
            setProducts(prevProducts =>
                prevProducts.map(product =>
                    product.id === id ? response.data : product
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update product';
            setError(errorMessage);
            console.error('Error updating product:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a product
     */
    const deleteProduct = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await productsService.deleteProduct(id);

            // Remove the product from local state
            setProducts(prevProducts =>
                prevProducts.filter(product => product.id !== id)
            );

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete product';
            setError(errorMessage);
            console.error('Error deleting product:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        products,
        loading,
        error,
        pagination,
        fetchProducts,
        createProduct,
        updateProduct,
        deleteProduct,
        setError
    };
};

/**
 * Custom hook for managing a single product
 */
export const useProduct = (id) => {
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchProduct = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await productsService.getProduct(id);
            setProduct(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch product');
            console.error('Error fetching product:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchProduct();
    }, [fetchProduct]);

    return {
        product,
        loading,
        error,
        refetch: fetchProduct
    };
};

/**
 * Custom hook for getting unit types
 */
export const useUnitTypes = () => {
    return productsService.getUnitTypes();
};
