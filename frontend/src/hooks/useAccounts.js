import { useState, useCallback, useEffect } from 'react';
import { accountsService } from '../services/accountsService';

/**
 * Custom hook for managing accounts CRUD operations
 */
export const useAccounts = () => {
    const [accounts, setAccounts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch accounts with optional parameters
     */
    const fetchAccounts = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.getAccounts(params);

            setAccounts(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch accounts');
            console.error('Error fetching accounts:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new account
     */
    const createAccount = useCallback(async (accountData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.createAccount(accountData);

            // Add the new account to the list
            setAccounts(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create account';
            setError(errorMessage);
            console.error('Error creating account:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing account
     */
    const updateAccount = useCallback(async (id, accountData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.updateAccount(id, accountData);

            // Update the account in the list
            setAccounts(prev =>
                prev.map(account =>
                    account.id === id ? response.data : account
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update account';
            setError(errorMessage);
            console.error('Error updating account:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete an account
     */
    const deleteAccount = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await accountsService.deleteAccount(id);

            // Remove the account from local state
            setAccounts(prevAccounts =>
                prevAccounts.filter(account => account.id !== id)
            );

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete account';
            setError(errorMessage);
            console.error('Error deleting account:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        accounts,
        loading,
        error,
        pagination,
        fetchAccounts,
        createAccount,
        updateAccount,
        deleteAccount,
        setError
    };
};

/**
 * Custom hook for managing a single account
 */
export const useAccount = (id) => {
    const [account, setAccount] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchAccount = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.getAccount(id);
            setAccount(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch account');
            console.error('Error fetching account:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchAccount();
    }, [fetchAccount]);

    return {
        account,
        loading,
        error,
        refetch: fetchAccount
    };
};

/**
 * Custom hook for managing account balances
 */
export const useAccountBalances = (accountId) => {
    const [balances, setBalances] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch balances for a specific account
     */
    const fetchAccountBalances = useCallback(async (params = {}) => {
        if (!accountId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.getAccountBalances(accountId, params);

            setBalances(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch account balances');
            console.error('Error fetching account balances:', err);
        } finally {
            setLoading(false);
        }
    }, [accountId]);

    useEffect(() => {
        if (accountId) {
            fetchAccountBalances();
        }
    }, [fetchAccountBalances]);

    return {
        balances,
        loading,
        error,
        pagination,
        fetchAccountBalances,
        setError
    };
};

/**
 * Custom hook for managing balance transactions
 */
export const useBalanceTransactions = (accountId, balanceId) => {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch transactions for a specific balance
     */
    const fetchBalanceTransactions = useCallback(async (params = {}) => {
        if (!accountId || !balanceId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.getBalanceTransactions(accountId, balanceId, params);

            setTransactions(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch balance transactions');
            console.error('Error fetching balance transactions:', err);
        } finally {
            setLoading(false);
        }
    }, [accountId, balanceId]);

    /**
     * Create a new transaction
     */
    const createTransaction = useCallback(async (transactionData) => {
        if (!accountId || !balanceId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await accountsService.createTransaction(accountId, balanceId, transactionData);

            // Add the new transaction to the list
            setTransactions(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create transaction';
            setError(errorMessage);
            console.error('Error creating transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [accountId, balanceId]);

    useEffect(() => {
        if (accountId && balanceId) {
            fetchBalanceTransactions();
        }
    }, [fetchBalanceTransactions]);

    return {
        transactions,
        loading,
        error,
        pagination,
        fetchBalanceTransactions,
        createTransaction,
        setError
    };
};
