import {useCallback, useEffect, useState} from 'react';
import salariesService from '../services/salariesService';

/**
 * Custom hook for managing salaries data and operations
 */
export const useSalaries = () => {
    const [salaries, setSalaries] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10,
        total_pages: 1
    });

    /**
     * Fetch salaries with optional parameters
     */
    const fetchSalaries = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.getSalaries(params);
            setSalaries(response.data.results || []);

            // Update pagination info
            setPagination({
                count: response.data.count || 0,
                next: response.data.next,
                previous: response.data.previous,
                page: params.page || 1,
                page_size: params.page_size || 10,
                total_pages: Math.ceil((response.data.count || 0) / (params.page_size || 10))
            });
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch salaries');
            console.error('Error fetching salaries:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Calculate salary for an employee
     */
    const calculateSalary = useCallback(async (salaryData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.calculateSalary(salaryData);

            // Add the new salary to the list (optimistic update)
            setSalaries(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                err.response?.data?.message ||
                'Failed to calculate salary';
            setError(errorMessage);
            console.error('Error calculating salary:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new salary record
     */
    const createSalary = useCallback(async (salaryData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.createSalary(salaryData);

            // Add the new salary to the list (optimistic update)
            setSalaries(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                err.response?.data?.message ||
                'Failed to create salary';
            setError(errorMessage);
            console.error('Error creating salary:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing salary record
     */
    const updateSalary = useCallback(async (id, salaryData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.updateSalary(id, salaryData);

            // Update the salary in the list (optimistic update)
            setSalaries(prev =>
                prev.map(salary =>
                    salary.id === id ? response.data : salary
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                err.response?.data?.message ||
                'Failed to update salary';
            setError(errorMessage);
            console.error('Error updating salary:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a salary record
     */
    const deleteSalary = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await salariesService.deleteSalary(id);

            // Remove the salary from the list (optimistic update)
            setSalaries(prev => prev.filter(salary => salary.id !== id));

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                err.response?.data?.message ||
                'Failed to delete salary';
            setError(errorMessage);
            console.error('Error deleting salary:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Mark salary as paid
     */
    const markAsPaid = useCallback(async (id, paymentData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.markAsPaid(id, paymentData);

            // Update the salary in the list (optimistic update)
            setSalaries(prev =>
                prev.map(salary =>
                    salary.id === id ? response.data : salary
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                err.response?.data?.message ||
                'Failed to mark salary as paid';
            setError(errorMessage);
            console.error('Error marking salary as paid:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Get salary history for a specific employee
     */
    const fetchEmployeeSalaryHistory = useCallback(async (employeeId, params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.getEmployeeSalaryHistory(employeeId, params);
            setSalaries(response.data.results || []);

            // Update pagination info
            setPagination({
                count: response.data.count || 0,
                next: response.data.next,
                previous: response.data.previous,
                page: params.page || 1,
                page_size: params.page_size || 10,
                total_pages: Math.ceil((response.data.count || 0) / (params.page_size || 10))
            });
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch employee salary history');
            console.error('Error fetching employee salary history:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        salaries,
        loading,
        error,
        pagination,
        fetchSalaries,
        calculateSalary,
        createSalary,
        updateSalary,
        deleteSalary,
        markAsPaid,
        fetchEmployeeSalaryHistory,
        setError
    };
};

/**
 * Custom hook for managing a single salary record
 */
export const useSalary = (id) => {
    const [salary, setSalary] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchSalary = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.getSalary(id);
            setSalary(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch salary');
            console.error('Error fetching salary:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchSalary();
    }, [fetchSalary]);

    return {
        salary,
        loading,
        error,
        refetch: fetchSalary
    };
};

/**
 * Custom hook for salary statistics
 */
export const useSalaryStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchSalaryStats = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.getSalaryStats(params);
            setStats(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch salary statistics');
            console.error('Error fetching salary statistics:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        stats,
        loading,
        error,
        fetchSalaryStats
    };
};

/**
 * Custom hook for pending salary calculations
 */
export const usePendingSalaries = () => {
    const [pendingSalaries, setPendingSalaries] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchPendingSalaries = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await salariesService.getPendingSalaries(params);
            setPendingSalaries(response.data.results || []);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch pending salaries');
            console.error('Error fetching pending salaries:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        pendingSalaries,
        loading,
        error,
        fetchPendingSalaries
    };
};
