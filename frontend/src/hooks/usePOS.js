import { useState, useCallback, useEffect } from 'react';
import { posService } from '../services/posService';

/**
 * Custom hook for managing POS terminals CRUD operations
 */
export const usePOSTerminals = () => {
    const [posTerminals, setPOSTerminals] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch POS terminals with optional parameters
     */
    const fetchPOSTerminals = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posService.getPOSTerminals(params);

            setPOSTerminals(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch POS terminals');
            console.error('Error fetching POS terminals:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new POS terminal
     */
    const createPOSTerminal = useCallback(async (posData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posService.createPOSTerminal(posData);

            // Add the new terminal to the list
            setPOSTerminals(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create POS terminal';
            setError(errorMessage);
            console.error('Error creating POS terminal:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing POS terminal
     */
    const updatePOSTerminal = useCallback(async (id, posData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posService.updatePOSTerminal(id, posData);

            // Update the terminal in the list
            setPOSTerminals(prev =>
                prev.map(terminal =>
                    terminal.id === id ? response.data : terminal
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update POS terminal';
            setError(errorMessage);
            console.error('Error updating POS terminal:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a POS terminal
     */
    const deletePOSTerminal = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await posService.deletePOSTerminal(id);

            // Remove the terminal from the list
            setPOSTerminals(prev => prev.filter(terminal => terminal.id !== id));

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete POS terminal';
            setError(errorMessage);
            console.error('Error deleting POS terminal:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Start a session for a POS terminal
     */
    const startSession = useCallback(async (posId, sessionData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posService.startSession(posId, sessionData);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to start session';
            setError(errorMessage);
            console.error('Error starting session:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        posTerminals,
        loading,
        error,
        pagination,
        fetchPOSTerminals,
        createPOSTerminal,
        updatePOSTerminal,
        deletePOSTerminal,
        startSession,
        setError
    };
};

/**
 * Custom hook for managing a single POS terminal
 */
export const usePOSTerminal = (id) => {
    const [posTerminal, setPOSTerminal] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchPOSTerminal = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posService.getPOSTerminal(id);
            setPOSTerminal(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch POS terminal');
            console.error('Error fetching POS terminal:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchPOSTerminal();
    }, [fetchPOSTerminal]);

    return {
        posTerminal,
        loading,
        error,
        refetch: fetchPOSTerminal
    };
};
