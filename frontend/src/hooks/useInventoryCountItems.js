import { useState, useCallback, useEffect } from 'react';
import { inventoryCountItemsService } from '../services/inventoryCountItemsService';

/**
 * Custom hook for managing inventory count items CRUD operations
 */
export const useInventoryCountItems = (inventoryCountId) => {
    const [inventoryCountItems, setInventoryCountItems] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 25
    });

    /**
     * Fetch inventory count items with optional parameters
     */
    const fetchInventoryCountItems = useCallback(async (params = {}) => {
        if (!inventoryCountId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountItemsService.getInventoryCountItems(inventoryCountId, params);

            setInventoryCountItems(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 25
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch inventory count items');
            console.error('Error fetching inventory count items:', err);
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId]);

    /**
     * Create a new inventory count item
     */
    const createInventoryCountItem = useCallback(async (itemData) => {
        if (!inventoryCountId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountItemsService.createInventoryCountItem(inventoryCountId, itemData);

            // Add the new item to the list
            setInventoryCountItems(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create inventory count item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId]);

    /**
     * Update an existing inventory count item
     */
    const updateInventoryCountItem = useCallback(async (itemId, itemData) => {
        if (!inventoryCountId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountItemsService.updateInventoryCountItem(inventoryCountId, itemId, itemData);

            // Update the item in the list
            setInventoryCountItems(prev =>
                prev.map(item => item.id === itemId ? response.data : item)
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update inventory count item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId]);

    /**
     * Bulk update multiple inventory count items
     */
    const bulkUpdateInventoryCountItems = useCallback(async (itemsData) => {
        if (!inventoryCountId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountItemsService.bulkUpdateInventoryCountItems(inventoryCountId, itemsData);

            // Update multiple items in the list
            const updatedItemsMap = new Map(response.data.map(item => [item.id, item]));
            setInventoryCountItems(prev =>
                prev.map(item => updatedItemsMap.get(item.id) || item)
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to bulk update inventory count items';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId]);

    /**
     * Delete an inventory count item
     */
    const deleteInventoryCountItem = useCallback(async (itemId) => {
        if (!inventoryCountId) return;

        try {
            setLoading(true);
            setError(null);

            await inventoryCountItemsService.deleteInventoryCountItem(inventoryCountId, itemId);

            // Remove the item from the list
            setInventoryCountItems(prev => prev.filter(item => item.id !== itemId));

        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete inventory count item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId]);

    // Auto-fetch items when inventoryCountId changes
    useEffect(() => {
        if (inventoryCountId) {
            fetchInventoryCountItems();
        }
    }, [inventoryCountId, fetchInventoryCountItems]);

    return {
        inventoryCountItems,
        loading,
        error,
        pagination,
        fetchInventoryCountItems,
        createInventoryCountItem,
        updateInventoryCountItem,
        bulkUpdateInventoryCountItems,
        deleteInventoryCountItem,
        setError
    };
};

/**
 * Custom hook for managing a single inventory count item
 */
export const useInventoryCountItem = (inventoryCountId, itemId) => {
    const [inventoryCountItem, setInventoryCountItem] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchInventoryCountItem = useCallback(async () => {
        if (!inventoryCountId || !itemId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountItemsService.getInventoryCountItem(inventoryCountId, itemId);
            setInventoryCountItem(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch inventory count item');
            console.error('Error fetching inventory count item:', err);
        } finally {
            setLoading(false);
        }
    }, [inventoryCountId, itemId]);

    useEffect(() => {
        fetchInventoryCountItem();
    }, [fetchInventoryCountItem]);

    return {
        inventoryCountItem,
        loading,
        error,
        refetch: fetchInventoryCountItem
    };
};
