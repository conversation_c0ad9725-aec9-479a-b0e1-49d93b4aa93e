import { useState, useCallback, useEffect } from 'react';
import { warehousesService } from '../services/warehousesService';

/**
 * Custom hook for managing warehouses CRUD operations
 */
export const useWarehouses = () => {
    const [warehouses, setWarehouses] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch warehouses with optional parameters
     */
    const fetchWarehouses = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await warehousesService.getWarehouses(params);

            setWarehouses(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch warehouses');
            console.error('Error fetching warehouses:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new warehouse
     */
    const createWarehouse = useCallback(async (warehouseData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await warehousesService.createWarehouse(warehouseData);

            // Add the new warehouse to the list
            setWarehouses(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create warehouse';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing warehouse
     */
    const updateWarehouse = useCallback(async (id, warehouseData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await warehousesService.updateWarehouse(id, warehouseData);

            // Update the warehouse in the list
            setWarehouses(prev =>
                prev.map(warehouse =>
                    warehouse.id === id ? response.data : warehouse
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update warehouse';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a warehouse
     */
    const deleteWarehouse = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await warehousesService.deleteWarehouse(id);

            // Remove the warehouse from the list
            setWarehouses(prev => prev.filter(warehouse => warehouse.id !== id));

        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete warehouse';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        warehouses,
        loading,
        error,
        pagination,
        fetchWarehouses,
        createWarehouse,
        updateWarehouse,
        deleteWarehouse,
        setError
    };
};

/**
 * Custom hook for managing a single warehouse
 */
export const useWarehouse = (id) => {
    const [warehouse, setWarehouse] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchWarehouse = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await warehousesService.getWarehouse(id);
            setWarehouse(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch warehouse');
            console.error('Error fetching warehouse:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchWarehouse();
    }, [fetchWarehouse]);

    return {
        warehouse,
        loading,
        error,
        refetch: fetchWarehouse
    };
};
