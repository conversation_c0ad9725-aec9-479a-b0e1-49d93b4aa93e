import { useState, useCallback, useEffect } from 'react';
import { transactionsService } from '../services/transactionsService';

/**
 * Custom hook for managing transactions CRUD operations
 */
export const useTransactions = () => {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch transactions with optional parameters
     */
    const fetchTransactions = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await transactionsService.getTransactions(params);

            setTransactions(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch transactions');
            console.error('Error fetching transactions:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new transaction
     */
    const createTransaction = useCallback(async (transactionData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await transactionsService.createTransaction(transactionData);

            // Add the new transaction to the list
            setTransactions(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create transaction';
            setError(errorMessage);
            console.error('Error creating transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing transaction
     */
    const updateTransaction = useCallback(async (id, transactionData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await transactionsService.updateTransaction(id, transactionData);

            // Update the transaction in the list
            setTransactions(prev =>
                prev.map(transaction =>
                    transaction.id === id ? response.data : transaction
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update transaction';
            setError(errorMessage);
            console.error('Error updating transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a transaction
     */
    const deleteTransaction = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await transactionsService.deleteTransaction(id);

            // Remove the transaction from local state
            setTransactions(prevTransactions =>
                prevTransactions.filter(transaction => transaction.id !== id)
            );

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete transaction';
            setError(errorMessage);
            console.error('Error deleting transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        transactions,
        loading,
        error,
        pagination,
        fetchTransactions,
        createTransaction,
        updateTransaction,
        deleteTransaction,
        setError
    };
};

/**
 * Custom hook for managing a single transaction
 */
export const useTransaction = (id) => {
    const [transaction, setTransaction] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchTransaction = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await transactionsService.getTransaction(id);
            setTransaction(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch transaction');
            console.error('Error fetching transaction:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchTransaction();
    }, [fetchTransaction]);

    return {
        transaction,
        loading,
        error,
        refetch: fetchTransaction
    };
};

/**
 * Custom hook for transaction summary/statistics
 */
export const useTransactionSummary = () => {
    const [summary, setSummary] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    /**
     * Fetch transaction summary with optional parameters
     */
    const fetchTransactionSummary = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await transactionsService.getTransactionSummary(params);
            setSummary(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch transaction summary');
            console.error('Error fetching transaction summary:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        summary,
        loading,
        error,
        fetchTransactionSummary,
        setError
    };
};
