import { useState, useCallback, useEffect } from 'react';
import { balancesService } from '../services/balancesService';

/**
 * Custom hook for managing balances CRUD operations
 */
export const useBalances = () => {
    const [balances, setBalances] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch balances with optional parameters
     */
    const fetchBalances = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await balancesService.getBalances(params);

            setBalances(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch balances');
            console.error('Error fetching balances:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new balance
     */
    const createBalance = useCallback(async (balanceData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await balancesService.createBalance(balanceData);

            // Add the new balance to the list
            setBalances(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create balance';
            setError(errorMessage);
            console.error('Error creating balance:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing balance
     */
    const updateBalance = useCallback(async (id, balanceData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await balancesService.updateBalance(id, balanceData);

            // Update the balance in the list
            setBalances(prev =>
                prev.map(balance =>
                    balance.id === id ? response.data : balance
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update balance';
            setError(errorMessage);
            console.error('Error updating balance:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete a balance
     */
    const deleteBalance = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await balancesService.deleteBalance(id);

            // Remove the balance from local state
            setBalances(prevBalances =>
                prevBalances.filter(balance => balance.id !== id)
            );

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete balance';
            setError(errorMessage);
            console.error('Error deleting balance:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Transfer amount between balances
     */
    const transferBalance = useCallback(async (transferData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await balancesService.transferBalance(transferData);

            // Refresh balances after transfer
            await fetchBalances();

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to transfer balance';
            setError(errorMessage);
            console.error('Error transferring balance:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [fetchBalances]);

    return {
        balances,
        loading,
        error,
        pagination,
        fetchBalances,
        createBalance,
        updateBalance,
        deleteBalance,
        transferBalance,
        setError
    };
};

/**
 * Custom hook for managing a single balance
 */
export const useBalance = (id) => {
    const [balance, setBalance] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchBalance = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await balancesService.getBalance(id);
            setBalance(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch balance');
            console.error('Error fetching balance:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchBalance();
    }, [fetchBalance]);

    return {
        balance,
        loading,
        error,
        refetch: fetchBalance
    };
};
