import { useState, useCallback, useEffect } from 'react';
import { stockItemsService } from '../services/stockItemsService';

/**
 * Custom hook for managing stock items CRUD operations
 * @param {number} warehouseId - The ID of the warehouse
 */
export const useStockItems = (warehouseId) => {
    const [stockItems, setStockItems] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch stock items with optional parameters
     */
    const fetchStockItems = useCallback(async (params = {}) => {
        if (!warehouseId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await stockItemsService.getStockItems(warehouseId, params);

            setStockItems(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch stock items');
            console.error('Error fetching stock items:', err);
        } finally {
            setLoading(false);
        }
    }, [warehouseId]);

    /**
     * Create a new stock item
     */
    const createStockItem = useCallback(async (stockItemData) => {
        if (!warehouseId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await stockItemsService.createStockItem(warehouseId, stockItemData);

            // Add the new stock item to the list
            setStockItems(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create stock item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [warehouseId]);

    /**
     * Update an existing stock item
     */
    const updateStockItem = useCallback(async (stockItemId, stockItemData) => {
        if (!warehouseId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await stockItemsService.updateStockItem(warehouseId, stockItemId, stockItemData);

            // Update the stock item in the list
            setStockItems(prev =>
                prev.map(item =>
                    item.id === stockItemId ? response.data : item
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update stock item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [warehouseId]);

    /**
     * Delete a stock item
     */
    const deleteStockItem = useCallback(async (stockItemId) => {
        if (!warehouseId) return;

        try {
            setLoading(true);
            setError(null);

            await stockItemsService.deleteStockItem(warehouseId, stockItemId);

            // Remove the stock item from the list
            setStockItems(prev => prev.filter(item => item.id !== stockItemId));

        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete stock item';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [warehouseId]);

    // Load stock items on mount and when warehouseId changes
    useEffect(() => {
        if (warehouseId) {
            fetchStockItems();
        }
    }, [warehouseId, fetchStockItems]);

    return {
        stockItems,
        loading,
        error,
        pagination,
        fetchStockItems,
        createStockItem,
        updateStockItem,
        deleteStockItem,
        setError
    };
};

/**
 * Custom hook for managing a single stock item
 */
export const useStockItem = (warehouseId, stockItemId) => {
    const [stockItem, setStockItem] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchStockItem = useCallback(async () => {
        if (!warehouseId || !stockItemId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await stockItemsService.getStockItem(warehouseId, stockItemId);
            setStockItem(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch stock item');
            console.error('Error fetching stock item:', err);
        } finally {
            setLoading(false);
        }
    }, [warehouseId, stockItemId]);

    useEffect(() => {
        fetchStockItem();
    }, [fetchStockItem]);

    return {
        stockItem,
        loading,
        error,
        refetch: fetchStockItem
    };
};
