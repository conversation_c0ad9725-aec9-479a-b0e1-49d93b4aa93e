import { useState, useCallback, useEffect } from 'react';
import { posSessionsService } from '../services/posSessionsService';

/**
 * Custom hook for managing POS sessions operations
 */
export const usePOSSessions = () => {
    const [sessions, setSessions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch POS sessions with optional parameters
     */
    const fetchSessions = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posSessionsService.getSessions(params);

            setSessions(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch POS sessions');
            console.error('Error fetching POS sessions:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Close a POS session
     */
    const closeSession = useCallback(async (id, sessionData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posSessionsService.closeSession(id, sessionData);

            // Update the session in the list
            setSessions(prev =>
                prev.map(session =>
                    session.id === id ? response.data : session
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to close session';
            setError(errorMessage);
            console.error('Error closing session:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Suspend a POS session
     */
    const suspendSession = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posSessionsService.suspendSession(id);

            // Update the session in the list
            setSessions(prev =>
                prev.map(session =>
                    session.id === id ? response.data : session
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to suspend session';
            setError(errorMessage);
            console.error('Error suspending session:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Resume a suspended POS session
     */
    const resumeSession = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            const response = await posSessionsService.resumeSession(id);

            // Update the session in the list
            setSessions(prev =>
                prev.map(session =>
                    session.id === id ? response.data : session
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to resume session';
            setError(errorMessage);
            console.error('Error resuming session:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        sessions,
        loading,
        error,
        pagination,
        fetchSessions,
        closeSession,
        suspendSession,
        resumeSession,
        setError
    };
};

/**
 * Custom hook for managing a single POS session
 */
export const usePOSSession = (id) => {
    const [session, setSession] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchSession = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posSessionsService.getSession(id);
            setSession(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch POS session');
            console.error('Error fetching POS session:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchSession();
    }, [fetchSession]);

    return {
        session,
        loading,
        error,
        refetch: fetchSession
    };
};
