import { useState, useCallback, useEffect } from 'react';
import { posSessionTransactionsService } from '../services/posSessionTransactionsService';

/**
 * Custom hook for managing POS session transactions operations
 */
export const usePOSSessionTransactions = (sessionId) => {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch transactions for a session with optional parameters
     */
    const fetchTransactions = useCallback(async (params = {}) => {
        if (!sessionId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posSessionTransactionsService.getTransactions(sessionId, params);

            setTransactions(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch transactions');
            console.error('Error fetching transactions:', err);
        } finally {
            setLoading(false);
        }
    }, [sessionId]);

    /**
     * Create a new transaction
     */
    const createTransaction = useCallback(async (transactionData) => {
        if (!sessionId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posSessionTransactionsService.createTransaction(sessionId, transactionData);

            // Add the new transaction to the list
            setTransactions(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create transaction';
            setError(errorMessage);
            console.error('Error creating transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [sessionId]);

    /**
     * Update an existing transaction
     */
    const updateTransaction = useCallback(async (transactionId, transactionData) => {
        if (!sessionId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posSessionTransactionsService.updateTransaction(sessionId, transactionId, transactionData);

            // Update the transaction in the list
            setTransactions(prev =>
                prev.map(transaction =>
                    transaction.id === transactionId ? response.data : transaction
                )
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update transaction';
            setError(errorMessage);
            console.error('Error updating transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [sessionId]);

    /**
     * Delete a transaction
     */
    const deleteTransaction = useCallback(async (transactionId) => {
        if (!sessionId) return;

        try {
            setLoading(true);
            setError(null);

            await posSessionTransactionsService.deleteTransaction(sessionId, transactionId);

            // Remove the transaction from the list
            setTransactions(prev => prev.filter(transaction => transaction.id !== transactionId));

            return true;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete transaction';
            setError(errorMessage);
            console.error('Error deleting transaction:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [sessionId]);

    // Auto-fetch transactions when sessionId changes
    useEffect(() => {
        if (sessionId) {
            fetchTransactions();
        }
    }, [sessionId, fetchTransactions]);

    return {
        transactions,
        loading,
        error,
        pagination,
        fetchTransactions,
        createTransaction,
        updateTransaction,
        deleteTransaction,
        setError
    };
};

/**
 * Custom hook for managing a single transaction
 */
export const usePOSSessionTransaction = (sessionId, transactionId) => {
    const [transaction, setTransaction] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchTransaction = useCallback(async () => {
        if (!sessionId || !transactionId) return;

        try {
            setLoading(true);
            setError(null);

            const response = await posSessionTransactionsService.getTransaction(sessionId, transactionId);
            setTransaction(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch transaction');
            console.error('Error fetching transaction:', err);
        } finally {
            setLoading(false);
        }
    }, [sessionId, transactionId]);

    useEffect(() => {
        fetchTransaction();
    }, [fetchTransaction]);

    return {
        transaction,
        loading,
        error,
        refetch: fetchTransaction
    };
};
