import { useState, useCallback, useEffect } from 'react';
import { inventoryCountsService } from '../services/inventoryCountsService';

/**
 * Custom hook for managing inventory counts CRUD operations
 */
export const useInventoryCounts = () => {
    const [inventoryCounts, setInventoryCounts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        count: 0,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    });

    /**
     * Fetch inventory counts with optional parameters
     */
    const fetchInventoryCounts = useCallback(async (params = {}) => {
        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.getInventoryCounts(params);

            setInventoryCounts(response.data.results || response.data);

            // Handle pagination if present
            if (response.data.count !== undefined) {
                setPagination({
                    count: response.data.count,
                    next: response.data.next,
                    previous: response.data.previous,
                    page: params.page || 1,
                    page_size: params.page_size || 10
                });
            }
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch inventory counts');
            console.error('Error fetching inventory counts:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Create a new inventory count
     */
    const createInventoryCount = useCallback(async (inventoryCountData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.createInventoryCount(inventoryCountData);

            // Add the new inventory count to the list
            setInventoryCounts(prev => [response.data, ...prev]);

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to create inventory count';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Update an existing inventory count
     */
    const updateInventoryCount = useCallback(async (id, inventoryCountData) => {
        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.updateInventoryCount(id, inventoryCountData);

            // Update the inventory count in the list
            setInventoryCounts(prev =>
                prev.map(count => count.id === id ? response.data : count)
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to update inventory count';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Delete an inventory count
     */
    const deleteInventoryCount = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            await inventoryCountsService.deleteInventoryCount(id);

            // Remove the inventory count from the list
            setInventoryCounts(prev => prev.filter(count => count.id !== id));

        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to delete inventory count';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Complete an inventory count
     */
    const completeInventoryCount = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.completeInventoryCount(id);

            // Update the inventory count status in the list
            setInventoryCounts(prev =>
                prev.map(count => count.id === id ? { ...count, status: 'completed' } : count)
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to complete inventory count';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Cancel an inventory count
     */
    const cancelInventoryCount = useCallback(async (id) => {
        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.cancelInventoryCount(id);

            // Update the inventory count status in the list
            setInventoryCounts(prev =>
                prev.map(count => count.id === id ? { ...count, status: 'cancelled' } : count)
            );

            return response.data;
        } catch (err) {
            const errorMessage = err.response?.data?.detail ||
                               err.response?.data?.message ||
                               'Failed to cancel inventory count';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        inventoryCounts,
        loading,
        error,
        pagination,
        fetchInventoryCounts,
        createInventoryCount,
        updateInventoryCount,
        deleteInventoryCount,
        completeInventoryCount,
        cancelInventoryCount,
        setError
    };
};

/**
 * Custom hook for managing a single inventory count
 */
export const useInventoryCount = (id) => {
    const [inventoryCount, setInventoryCount] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchInventoryCount = useCallback(async () => {
        if (!id) return;

        try {
            setLoading(true);
            setError(null);

            const response = await inventoryCountsService.getInventoryCount(id);
            setInventoryCount(response.data);
        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to fetch inventory count');
            console.error('Error fetching inventory count:', err);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchInventoryCount();
    }, [fetchInventoryCount]);

    return {
        inventoryCount,
        loading,
        error,
        refetch: fetchInventoryCount
    };
};
