import React from 'react';
import {BrowserRouter as Router, Navigate, Route, Routes} from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

import {AuthProvider, useAuth} from './contexts/AuthContext';

import Navigation from './components/layout/Navigation';
import Login from './components/auth/Login';
import Home from "./components/Home";

// Categories components
import CategoriesList from './components/categories/CategoriesList';
import CategoryCreate from './components/categories/CategoryCreate';
import CategoryEdit from './components/categories/CategoryEdit';
import CategoryView from './components/categories/CategoryView';

// Products components
import ProductsList from './components/products/ProductsList';
import ProductCreate from './components/products/ProductCreate';
import ProductEdit from './components/products/ProductEdit';
import ProductView from './components/products/ProductView';

// Warehouses components
import WarehousesList from './components/warehouses/WarehousesList';
import WarehouseCreate from './components/warehouses/WarehouseCreate';
import WarehouseEdit from './components/warehouses/WarehouseEdit';
import WarehouseView from './components/warehouses/WarehouseView';

// Inventory Counts components
import InventoryCountsList from './components/inventory-counts/InventoryCountsList';
import InventoryCountCreate from './components/inventory-counts/InventoryCountCreate';
import InventoryCountEdit from './components/inventory-counts/InventoryCountEdit';
import InventoryCountView from './components/inventory-counts/InventoryCountView';

// POS components
import POSTerminalsList from './components/pos/POSTerminalsList';
import POSTerminalCreate from './components/pos/POSTerminalCreate';
import POSTerminalEdit from './components/pos/POSTerminalEdit';
import POSTerminalView from './components/pos/POSTerminalView';
import POSSessionsList from './components/pos/POSSessionsList';
import POSSessionView from './components/pos/POSSessionView';
import TransactionsList from './components/pos/TransactionsList';

// Employees components
import EmployeesList from './components/employees/EmployeesList';
import EmployeeCreate from './components/employees/EmployeeCreate';
import EmployeeEdit from './components/employees/EmployeeEdit';
import EmployeeView from './components/employees/EmployeeView';
import AttendanceListPage from './components/employees/AttendanceListPage';
import SalaryListPage from './components/employees/SalaryListPage';
import TimeInPage from './components/employees/TimeInPage';
import TimeOutPage from './components/employees/TimeOutPage';

// Debug component
import AuthDebug from './components/auth/AuthDebug';

const ProtectedRoute = ({children, requiredRole}) => {
    const {currentUser, loading, permittedAdmin} = useAuth();

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!currentUser) {
        return <Navigate to="/login"/>;
    }
    if (!permittedAdmin) {
        return <Navigate to="/"/>;
    }

    return children;
};


function App() {
    return (
        <AuthProvider>
            <Router>
                <Navigation/>
                <div className="mt-4">
                    <Routes>
                        <Route path="/login" element={<Login/>}/>
                        <Route path="/auth-debug" element={<AuthDebug/>}/>

                        <Route path="/" element={<Home/>}/>

                        {/* Categories Routes */}
                        <Route
                            path="/categories"
                            element={
                                <ProtectedRoute>
                                    <CategoriesList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/create"
                            element={
                                <ProtectedRoute>
                                    <CategoryCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/:id"
                            element={
                                <ProtectedRoute>
                                    <CategoryView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/categories/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <CategoryEdit/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Products Routes */}
                        <Route
                            path="/products"
                            element={
                                <ProtectedRoute>
                                    <ProductsList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/products/create"
                            element={
                                <ProtectedRoute>
                                    <ProductCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/products/:id"
                            element={
                                <ProtectedRoute>
                                    <ProductView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/products/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <ProductEdit/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Warehouses Routes */}
                        <Route
                            path="/warehouses"
                            element={
                                <ProtectedRoute>
                                    <WarehousesList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/warehouses/create"
                            element={
                                <ProtectedRoute>
                                    <WarehouseCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/warehouses/:id"
                            element={
                                <ProtectedRoute>
                                    <WarehouseView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/warehouses/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <WarehouseEdit/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Inventory Counts Routes */}
                        <Route
                            path="/inventory-counts"
                            element={
                                <ProtectedRoute>
                                    <InventoryCountsList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/inventory-counts/create"
                            element={
                                <ProtectedRoute>
                                    <InventoryCountCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/inventory-counts/:id"
                            element={
                                <ProtectedRoute>
                                    <InventoryCountView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/inventory-counts/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <InventoryCountEdit/>
                                </ProtectedRoute>
                            }
                        />

                        {/* POS Routes */}
                        <Route
                            path="/pos/terminals"
                            element={
                                <ProtectedRoute>
                                    <POSTerminalsList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/terminals/create"
                            element={
                                <ProtectedRoute>
                                    <POSTerminalCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/terminals/:id"
                            element={
                                <ProtectedRoute>
                                    <POSTerminalView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/terminals/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <POSTerminalEdit/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/sessions"
                            element={
                                <ProtectedRoute>
                                    <POSSessionsList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/sessions/:id"
                            element={
                                <ProtectedRoute>
                                    <POSSessionView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/pos/sessions/:sessionId/transactions"
                            element={
                                <ProtectedRoute>
                                    <TransactionsList/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Employees Routes */}
                        <Route
                            path="/employees"
                            element={
                                <ProtectedRoute>
                                    <EmployeesList/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/employees/create"
                            element={
                                <ProtectedRoute>
                                    <EmployeeCreate/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/employees/:id"
                            element={
                                <ProtectedRoute>
                                    <EmployeeView/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/employees/:id/edit"
                            element={
                                <ProtectedRoute>
                                    <EmployeeEdit/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Attendance Routes */}
                        <Route
                            path="/attendance"
                            element={
                                <ProtectedRoute>
                                    <AttendanceListPage/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/attendance/time-in"
                            element={
                                <ProtectedRoute>
                                    <TimeInPage/>
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/attendance/time-out"
                            element={
                                <ProtectedRoute>
                                    <TimeOutPage/>
                                </ProtectedRoute>
                            }
                        />

                        {/* Salary Routes */}
                        <Route
                            path="/salaries"
                            element={
                                <ProtectedRoute>
                                    <SalaryListPage/>
                                </ProtectedRoute>
                            }
                        />

                        <Route path="*" element={<Navigate to="/"/>}/>
                    </Routes>
                </div>
            </Router>
        </AuthProvider>
    );
}

export default App;
