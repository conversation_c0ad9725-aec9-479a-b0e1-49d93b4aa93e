import { productsService } from '../productsService';
import api from '../api';

// Mock the api module
jest.mock('../api');

describe('productsService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getProducts', () => {
        test('calls API with correct parameters', async () => {
            const mockResponse = { data: { results: [] } };
            api.get.mockResolvedValue(mockResponse);

            const params = {
                page: 1,
                page_size: 10,
                search: 'test',
                ordering: 'name',
                category: '1',
                unit_type: 'piece'
            };

            await productsService.getProducts(params);

            expect(api.get).toHaveBeenCalledWith(
                '/products/?page=1&page_size=10&search=test&ordering=name&category=1&unit_type=piece'
            );
        });

        test('calls API without parameters', async () => {
            const mockResponse = { data: { results: [] } };
            api.get.mockResolvedValue(mockResponse);

            await productsService.getProducts();

            expect(api.get).toHaveBeenCalledWith('/products/');
        });

        test('handles partial parameters', async () => {
            const mockResponse = { data: { results: [] } };
            api.get.mockResolvedValue(mockResponse);

            await productsService.getProducts({ page: 2, search: 'test' });

            expect(api.get).toHaveBeenCalledWith('/products/?page=2&search=test');
        });
    });

    describe('getProduct', () => {
        test('calls API with correct product ID', async () => {
            const mockResponse = { data: { id: 1, name: 'Test Product' } };
            api.get.mockResolvedValue(mockResponse);

            await productsService.getProduct(1);

            expect(api.get).toHaveBeenCalledWith('/products/1/');
        });
    });

    describe('createProduct', () => {
        test('calls API with FormData for product creation', async () => {
            const mockResponse = { data: { id: 1, name: 'New Product' } };
            api.post.mockResolvedValue(mockResponse);

            const productData = {
                name: 'New Product',
                cost: 10,
                price: 15,
                description: 'Test description'
            };

            await productsService.createProduct(productData);

            expect(api.post).toHaveBeenCalledWith(
                '/products/',
                expect.any(FormData),
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
        });

        test('excludes null and empty values from FormData', async () => {
            const mockResponse = { data: { id: 1, name: 'New Product' } };
            api.post.mockResolvedValue(mockResponse);

            const productData = {
                name: 'New Product',
                cost: 10,
                price: 15,
                description: '',
                barcode: null,
                category_id: undefined
            };

            await productsService.createProduct(productData);

            // Verify FormData was created and passed
            expect(api.post).toHaveBeenCalledWith(
                '/products/',
                expect.any(FormData),
                expect.any(Object)
            );
        });
    });

    describe('updateProduct', () => {
        test('calls API with FormData for product update', async () => {
            const mockResponse = { data: { id: 1, name: 'Updated Product' } };
            api.put.mockResolvedValue(mockResponse);

            const productData = {
                name: 'Updated Product',
                cost: 12,
                price: 18
            };

            await productsService.updateProduct(1, productData);

            expect(api.put).toHaveBeenCalledWith(
                '/products/1/',
                expect.any(FormData),
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
        });
    });

    describe('deleteProduct', () => {
        test('calls API with correct product ID', async () => {
            api.delete.mockResolvedValue({});

            await productsService.deleteProduct(1);

            expect(api.delete).toHaveBeenCalledWith('/products/1/');
        });
    });

    describe('getUnitTypes', () => {
        test('returns correct unit types array', () => {
            const unitTypes = productsService.getUnitTypes();

            expect(unitTypes).toEqual([
                { value: 'piece', label: 'Piece' },
                { value: 'kg', label: 'Kilogram' },
                { value: 'g', label: 'Gram' },
                { value: 'l', label: 'Liter' },
                { value: 'ml', label: 'Milliliter' }
            ]);
        });

        test('returns array with correct structure', () => {
            const unitTypes = productsService.getUnitTypes();

            unitTypes.forEach(unit => {
                expect(unit).toHaveProperty('value');
                expect(unit).toHaveProperty('label');
                expect(typeof unit.value).toBe('string');
                expect(typeof unit.label).toBe('string');
            });
        });
    });

    describe('error handling', () => {
        test('getProducts handles API errors', async () => {
            const errorResponse = {
                response: {
                    data: { detail: 'Server error' },
                    status: 500
                }
            };
            api.get.mockRejectedValue(errorResponse);

            await expect(productsService.getProducts()).rejects.toEqual(errorResponse);
        });

        test('createProduct handles API errors', async () => {
            const errorResponse = {
                response: {
                    data: { name: ['This field is required.'] },
                    status: 400
                }
            };
            api.post.mockRejectedValue(errorResponse);

            await expect(productsService.createProduct({})).rejects.toEqual(errorResponse);
        });

        test('updateProduct handles API errors', async () => {
            const errorResponse = {
                response: {
                    data: { detail: 'Product not found' },
                    status: 404
                }
            };
            api.put.mockRejectedValue(errorResponse);

            await expect(productsService.updateProduct(999, {})).rejects.toEqual(errorResponse);
        });

        test('deleteProduct handles API errors', async () => {
            const errorResponse = {
                response: {
                    data: { detail: 'Product not found' },
                    status: 404
                }
            };
            api.delete.mockRejectedValue(errorResponse);

            await expect(productsService.deleteProduct(999)).rejects.toEqual(errorResponse);
        });
    });

    describe('FormData handling', () => {
        test('createProduct properly handles file uploads', async () => {
            const mockResponse = { data: { id: 1, name: 'New Product' } };
            api.post.mockResolvedValue(mockResponse);

            const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
            const productData = {
                name: 'New Product',
                cost: 10,
                price: 15,
                image: file
            };

            await productsService.createProduct(productData);

            // Verify the call was made with FormData
            const [url, formData, config] = api.post.mock.calls[0];

            expect(url).toBe('/products/');
            expect(formData).toBeInstanceOf(FormData);
            expect(config.headers['Content-Type']).toBe('multipart/form-data');
        });

        test('updateProduct properly handles file uploads', async () => {
            const mockResponse = { data: { id: 1, name: 'Updated Product' } };
            api.put.mockResolvedValue(mockResponse);

            const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
            const productData = {
                name: 'Updated Product',
                image: file
            };

            await productsService.updateProduct(1, productData);

            // Verify the call was made with FormData
            const [url, formData, config] = api.put.mock.calls[0];

            expect(url).toBe('/products/1/');
            expect(formData).toBeInstanceOf(FormData);
            expect(config.headers['Content-Type']).toBe('multipart/form-data');
        });
    });
});
