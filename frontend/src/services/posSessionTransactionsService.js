import api from './api';

/**
 * POS Session Transactions API service
 * Handles all API calls related to POS session transactions operations
 */
export const posSessionTransactionsService = {
    /**
     * Get all transactions for a specific session with optional pagination and filtering
     * @param {number} sessionId - POS session ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.transaction_type - Filter by transaction type
     * @param {string} params.created_after - Filter by created date (after)
     * @param {string} params.created_before - Filter by created date (before)
     * @returns {Promise} API response
     */
    getTransactions: (sessionId, params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/session/${sessionId}/transactions/?${queryString}` : `/session/${sessionId}/transactions/`;

        return api.get(url);
    },

    /**
     * Get a single transaction by ID
     * @param {number} sessionId - POS session ID
     * @param {number} transactionId - Transaction ID
     * @returns {Promise} API response
     */
    getTransaction: (sessionId, transactionId) => {
        return api.get(`/session/${sessionId}/transactions/${transactionId}/`);
    },

    /**
     * Create a new transaction for a session
     * @param {number} sessionId - POS session ID
     * @param {Object} transactionData - Transaction data
     * @param {string} transactionData.transaction_type - Transaction type
     * @param {number} transactionData.amount - Transaction amount
     * @param {string} transactionData.description - Transaction description (optional)
     * @returns {Promise} API response
     */
    createTransaction: (sessionId, transactionData) => {
        return api.post(`/session/${sessionId}/transactions/`, transactionData);
    },

    /**
     * Update an existing transaction
     * @param {number} sessionId - POS session ID
     * @param {number} transactionId - Transaction ID
     * @param {Object} transactionData - Updated transaction data
     * @returns {Promise} API response
     */
    updateTransaction: (sessionId, transactionId, transactionData) => {
        return api.put(`/session/${sessionId}/transactions/${transactionId}/`, transactionData);
    },

    /**
     * Delete a transaction
     * @param {number} sessionId - POS session ID
     * @param {number} transactionId - Transaction ID
     * @returns {Promise} API response
     */
    deleteTransaction: (sessionId, transactionId) => {
        return api.delete(`/session/${sessionId}/transactions/${transactionId}/`);
    }
};
