import api from './api';

/**
 * POS Sessions API service
 * Handles all API calls related to POS sessions operations
 */
export const posSessionsService = {
    /**
     * Get all POS sessions with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.status - Filter by status (open, closed, suspended)
     * @param {number} params.pos - Filter by POS terminal ID
     * @param {string} params.opened_at_after - Filter by opened date (after)
     * @param {string} params.opened_at_before - Filter by opened date (before)
     * @param {string} params.closed_at_after - Filter by closed date (after)
     * @param {string} params.closed_at_before - Filter by closed date (before)
     * @returns {Promise} API response
     */
    getSessions: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/sessions/?${queryString}` : '/sessions/';

        return api.get(url);
    },

    /**
     * Get a single POS session by ID
     * @param {number} id - POS session ID
     * @returns {Promise} API response
     */
    getSession: (id) => {
        return api.get(`/sessions/${id}/`);
    },

    /**
     * Close a POS session
     * @param {number} id - POS session ID
     * @param {Object} sessionData - Session closing data
     * @param {number} sessionData.closing_balance - Closing balance
     * @returns {Promise} API response
     */
    closeSession: (id, sessionData) => {
        return api.post(`/sessions/${id}/close/`, sessionData);
    },

    /**
     * Suspend a POS session
     * @param {number} id - POS session ID
     * @returns {Promise} API response
     */
    suspendSession: (id) => {
        return api.post(`/sessions/${id}/suspend/`);
    },

    /**
     * Resume a suspended POS session
     * @param {number} id - POS session ID
     * @returns {Promise} API response
     */
    resumeSession: (id) => {
        return api.post(`/sessions/${id}/resume/`);
    },

    /**
     * Get transactions for a specific session
     * @param {number} sessionId - POS session ID
     * @param {Object} params - Query parameters
     * @returns {Promise} API response
     */
    getSessionTransactions: (sessionId, params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/sessions/${sessionId}/transactions/?${queryString}` : `/sessions/${sessionId}/transactions/`;

        return api.get(url);
    }
};
