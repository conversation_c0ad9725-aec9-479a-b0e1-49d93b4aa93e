import api from './api';

/**
 * Categories API service
 * Handles all API calls related to categories CRUD operations
 */
export const categoriesService = {
    /**
     * Get all categories with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.parent_id - Filter by parent category
     * @returns {Promise} API response
     */
    getCategories: (params = {}) => {
        const queryParams = new URLSearchParams();

        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);

        // Add search parameter
        if (params.search) queryParams.append('search', params.search);

        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);

        // Add parent filter
        if (params.parent_id !== undefined) {
            queryParams.append('parent_id', params.parent_id);
        }

        const queryString = queryParams.toString();
        const url = `/categories/${queryString ? `?${queryString}` : ''}`;

        return api.get(url);
    },

    /**
     * Get a single category by ID
     * @param {number} id - Category ID
     * @returns {Promise} API response
     */
    getCategory: (id) => {
        return api.get(`/categories/${id}/`);
    },

    /**
     * Create a new category
     * @param {Object} categoryData - Category data
     * @param {string} categoryData.name - Category name
     * @param {string} categoryData.description - Category description
     * @param {number} categoryData.parent_id - Parent category ID (optional)
     * @returns {Promise} API response
     */
    createCategory: (categoryData) => {
        return api.post('/categories/', categoryData);
    },

    /**
     * Update an existing category
     * @param {number} id - Category ID
     * @param {Object} categoryData - Updated category data
     * @returns {Promise} API response
     */
    updateCategory: (id, categoryData) => {
        return api.put(`/categories/${id}/`, categoryData);
    },

    /**
     * Delete a category
     * @param {number} id - Category ID
     * @returns {Promise} API response
     */
    deleteCategory: (id) => {
        return api.delete(`/categories/${id}/`);
    },

    /**
     * Get parent categories (categories without a parent)
     * @returns {Promise} API response
     */
    getParentCategories: () => {
        return api.get('/categories/?parent_id=null');
    },

    /**
     * Get subcategories of a specific parent category
     * @param {number} parentId - Parent category ID
     * @returns {Promise} API response
     */
    getSubcategories: (parentId) => {
        return api.get(`/categories/?parent_id=${parentId}`);
    }
};

export default categoriesService;
