import api from './api';

/**
 * Balances API service
 * Handles all API calls related to balances CRUD operations
 */
export const balancesService = {
    /**
     * Get all balances with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {number} params.account_id - Filter by account ID
     * @param {number} params.pos_id - Filter by POS ID
     * @param {number} params.min_amount - Minimum amount filter
     * @param {number} params.max_amount - Maximum amount filter
     * @param {string} params.date_from - Start date filter
     * @param {string} params.date_to - End date filter
     * @returns {Promise} API response
     */
    getBalances: (params = {}) => {
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        
        // Add search parameter
        if (params.search) queryParams.append('search', params.search);
        
        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        // Add filter parameters
        if (params.account_id) queryParams.append('account_id', params.account_id);
        if (params.pos_id) queryParams.append('pos_id', params.pos_id);
        if (params.min_amount !== undefined) queryParams.append('min_amount', params.min_amount);
        if (params.max_amount !== undefined) queryParams.append('max_amount', params.max_amount);
        if (params.date_from) queryParams.append('date_from', params.date_from);
        if (params.date_to) queryParams.append('date_to', params.date_to);
        
        const queryString = queryParams.toString();
        return api.get(`/balances/${queryString ? `?${queryString}` : ''}`);
    },

    /**
     * Get a single balance by ID
     * @param {number} id - Balance ID
     * @returns {Promise} API response
     */
    getBalance: (id) => {
        return api.get(`/balances/${id}/`);
    },

    /**
     * Create a new balance
     * @param {Object} data - Balance data
     * @param {number} data.account_id - Account ID
     * @param {number} data.pos_id - POS ID
     * @param {number} data.amount - Balance amount
     * @param {string} data.notes - Balance notes (optional)
     * @returns {Promise} API response
     */
    createBalance: (data) => {
        return api.post('/balances/', data);
    },

    /**
     * Update an existing balance
     * @param {number} id - Balance ID
     * @param {Object} data - Updated balance data
     * @returns {Promise} API response
     */
    updateBalance: (id, data) => {
        return api.put(`/balances/${id}/`, data);
    },

    /**
     * Partially update an existing balance
     * @param {number} id - Balance ID
     * @param {Object} data - Partial balance data
     * @returns {Promise} API response
     */
    patchBalance: (id, data) => {
        return api.patch(`/balances/${id}/`, data);
    },

    /**
     * Delete a balance
     * @param {number} id - Balance ID
     * @returns {Promise} API response
     */
    deleteBalance: (id) => {
        return api.delete(`/balances/${id}/`);
    },

    /**
     * Transfer amount between balances
     * @param {Object} data - Transfer data
     * @param {number} data.from_balance_id - Source balance ID
     * @param {number} data.to_balance_id - Destination balance ID
     * @param {number} data.amount - Transfer amount
     * @param {string} data.description - Transfer description
     * @returns {Promise} API response
     */
    transferBalance: (data) => {
        return api.post('/balances/transfer/', data);
    }
};
