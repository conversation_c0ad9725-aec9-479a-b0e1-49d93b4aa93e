import api from './api';

/**
 * Accounts API service
 * Handles all API calls related to accounts CRUD operations
 */
export const accountsService = {
    /**
     * Get all accounts with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.account_type - Filter by account type
     * @param {number} params.min_balance - Minimum balance filter
     * @param {number} params.max_balance - Maximum balance filter
     * @returns {Promise} API response
     */
    getAccounts: (params = {}) => {
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        
        // Add search parameter
        if (params.search) queryParams.append('search', params.search);
        
        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        // Add filter parameters
        if (params.account_type) queryParams.append('account_type', params.account_type);
        if (params.min_balance !== undefined) queryParams.append('min_balance', params.min_balance);
        if (params.max_balance !== undefined) queryParams.append('max_balance', params.max_balance);
        
        const queryString = queryParams.toString();
        return api.get(`/accounts/${queryString ? `?${queryString}` : ''}`);
    },

    /**
     * Get a single account by ID
     * @param {number} id - Account ID
     * @returns {Promise} API response
     */
    getAccount: (id) => {
        return api.get(`/accounts/${id}/`);
    },

    /**
     * Create a new account
     * @param {Object} data - Account data
     * @param {string} data.name - Account name
     * @param {string} data.account_type - Account type
     * @param {string} data.content_type - Content type ID (optional)
     * @param {number} data.object_id - Object ID (optional)
     * @returns {Promise} API response
     */
    createAccount: (data) => {
        return api.post('/accounts/', data);
    },

    /**
     * Update an existing account
     * @param {number} id - Account ID
     * @param {Object} data - Updated account data
     * @returns {Promise} API response
     */
    updateAccount: (id, data) => {
        return api.put(`/accounts/${id}/`, data);
    },

    /**
     * Partially update an existing account
     * @param {number} id - Account ID
     * @param {Object} data - Partial account data
     * @returns {Promise} API response
     */
    patchAccount: (id, data) => {
        return api.patch(`/accounts/${id}/`, data);
    },

    /**
     * Delete an account
     * @param {number} id - Account ID
     * @returns {Promise} API response
     */
    deleteAccount: (id) => {
        return api.delete(`/accounts/${id}/`);
    },

    /**
     * Get balances for a specific account
     * @param {number} accountId - Account ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @returns {Promise} API response
     */
    getAccountBalances: (accountId, params = {}) => {
        const queryParams = new URLSearchParams();
        
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        if (params.search) queryParams.append('search', params.search);
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        const queryString = queryParams.toString();
        return api.get(`/accounts/${accountId}/balances/${queryString ? `?${queryString}` : ''}`);
    },

    /**
     * Get transactions for a specific account balance
     * @param {number} accountId - Account ID
     * @param {number} balanceId - Balance ID
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.type - Transaction type filter (debit/credit)
     * @param {string} params.date_from - Start date filter
     * @param {string} params.date_to - End date filter
     * @returns {Promise} API response
     */
    getBalanceTransactions: (accountId, balanceId, params = {}) => {
        const queryParams = new URLSearchParams();
        
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        if (params.search) queryParams.append('search', params.search);
        if (params.ordering) queryParams.append('ordering', params.ordering);
        if (params.type) queryParams.append('type', params.type);
        if (params.date_from) queryParams.append('date_from', params.date_from);
        if (params.date_to) queryParams.append('date_to', params.date_to);
        
        const queryString = queryParams.toString();
        return api.get(`/accounts/${accountId}/balance/${balanceId}/transactions/${queryString ? `?${queryString}` : ''}`);
    },

    /**
     * Create a new transaction for a specific account balance
     * @param {number} accountId - Account ID
     * @param {number} balanceId - Balance ID
     * @param {Object} data - Transaction data
     * @param {string} data.type - Transaction type (debit/credit)
     * @param {number} data.amount - Transaction amount
     * @param {string} data.description - Transaction description
     * @param {string} data.related_content_type - Related object content type (optional)
     * @param {number} data.related_object_id - Related object ID (optional)
     * @returns {Promise} API response
     */
    createTransaction: (accountId, balanceId, data) => {
        return api.post(`/accounts/${accountId}/balance/${balanceId}/create-transactions/`, data);
    }
};
