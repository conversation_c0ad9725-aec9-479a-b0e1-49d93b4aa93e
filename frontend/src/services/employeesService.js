import api from './api';

/**
 * Employees API service
 * Handles all API calls related to employees CRUD operations
 */
export const employeesService = {
    /**
     * Get all employees with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {string} params.status - Filter by status
     * @param {string} params.type - Filter by employee type
     * @param {number} params.pos - Filter by POS terminal ID
     * @returns {Promise} API response
     */
    getEmployees: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/employees/?${queryString}` : '/employees/';

        return api.get(url);
    },

    /**
     * Get a single employee by ID
     * @param {number} id - Employee ID
     * @returns {Promise} API response
     */
    getEmployee: (id) => {
        return api.get(`/employees/${id}/`);
    },

    /**
     * Create a new employee
     * @param {Object} employeeData - Employee data
     * @param {Object} employeeData.user - User data (first_name, last_name, email, phone_number, password, role)
     * @param {string} employeeData.address - Employee address
     * @param {string} employeeData.type - Employee type (full_time, part_time, daily)
     * @param {number} employeeData.hour_rate - Hourly rate
     * @param {number} employeeData.day_rate - Daily rate
     * @param {string} employeeData.hire_date - Hire date (YYYY-MM-DD)
     * @param {string} employeeData.status - Employee status (active, inactive, terminated)
     * @param {number} employeeData.pos - POS terminal ID (optional)
     * @param {File} employeeData.identification - Identification image file
     * @returns {Promise} API response
     */
    createEmployee: (employeeData) => {
        const formData = new FormData();

        // Handle nested user data
        if (employeeData.user) {
            Object.keys(employeeData.user).forEach(key => {
                formData.append(`user.${key}`, employeeData.user[key]);
            });
        }

        // Handle other employee fields
        Object.keys(employeeData).forEach(key => {
            if (key !== 'user' && employeeData[key] !== null && employeeData[key] !== undefined) {
                if (key === 'identification' && employeeData[key] instanceof File) {
                    formData.append(key, employeeData[key]);
                } else if (key !== 'identification') {
                    formData.append(key, employeeData[key]);
                }
            }
        });

        return api.post('/employees/', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Update an existing employee
     * @param {number} id - Employee ID
     * @param {Object} employeeData - Updated employee data
     * @returns {Promise} API response
     */
    updateEmployee: (id, employeeData) => {
        const formData = new FormData();

        // Handle nested user data
        if (employeeData.user) {
            Object.keys(employeeData.user).forEach(key => {
                formData.append(`user.${key}`, employeeData.user[key]);
            });
        }

        // Handle other employee fields
        Object.keys(employeeData).forEach(key => {
            if (key !== 'user' && employeeData[key] !== null && employeeData[key] !== undefined) {
                if (key === 'identification' && employeeData[key] instanceof File) {
                    formData.append(key, employeeData[key]);
                } else if (key !== 'identification') {
                    formData.append(key, employeeData[key]);
                }
            }
        });

        return api.patch(`/employees/${id}/`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Delete an employee
     * @param {number} id - Employee ID
     * @returns {Promise} API response
     */
    deleteEmployee: (id) => {
        return api.delete(`/employees/${id}/`);
    },

    /**
     * Get employee types choices
     * @returns {Array} Employee type options
     */
    getEmployeeTypes: () => {
        return [
            { value: 'full_time', label: 'Full Time' },
            { value: 'part_time', label: 'Part Time' },
            { value: 'daily', label: 'Daily Worker' }
        ];
    },

    /**
     * Get employee status choices
     * @returns {Array} Employee status options
     */
    getEmployeeStatuses: () => {
        return [
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'terminated', label: 'Terminated' }
        ];
    },

    /**
     * Get user role choices
     * @returns {Array} User role options
     */
    getUserRoles: () => {
        return [
            { value: 'admin', label: 'Admin' },
            { value: 'manager', label: 'Manager' },
            { value: 'employee', label: 'Employee' },
            { value: 'cashier', label: 'Cashier' }
        ];
    }
};

export default employeesService;
