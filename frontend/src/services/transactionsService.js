import api from './api';

/**
 * Transactions API service
 * Handles all API calls related to account transactions CRUD operations
 */
export const transactionsService = {
    /**
     * Get all transactions with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {number} params.balance_id - Filter by balance ID
     * @param {number} params.account_id - Filter by account ID
     * @param {string} params.type - Filter by transaction type (debit/credit)
     * @param {number} params.min_amount - Minimum amount filter
     * @param {number} params.max_amount - Maximum amount filter
     * @param {string} params.date_from - Start date filter
     * @param {string} params.date_to - End date filter
     * @returns {Promise} API response
     */
    getTransactions: (params = {}) => {
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);
        
        // Add search parameter
        if (params.search) queryParams.append('search', params.search);
        
        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);
        
        // Add filter parameters
        if (params.balance_id) queryParams.append('balance_id', params.balance_id);
        if (params.account_id) queryParams.append('account_id', params.account_id);
        if (params.type) queryParams.append('type', params.type);
        if (params.min_amount !== undefined) queryParams.append('min_amount', params.min_amount);
        if (params.max_amount !== undefined) queryParams.append('max_amount', params.max_amount);
        if (params.date_from) queryParams.append('date_from', params.date_from);
        if (params.date_to) queryParams.append('date_to', params.date_to);
        
        const queryString = queryParams.toString();
        return api.get(`/transactions/${queryString ? `?${queryString}` : ''}`);
    },

    /**
     * Get a single transaction by ID
     * @param {number} id - Transaction ID
     * @returns {Promise} API response
     */
    getTransaction: (id) => {
        return api.get(`/transactions/${id}/`);
    },

    /**
     * Create a new transaction
     * @param {Object} data - Transaction data
     * @param {number} data.balance_id - Balance ID
     * @param {string} data.type - Transaction type (debit/credit)
     * @param {number} data.amount - Transaction amount
     * @param {string} data.description - Transaction description
     * @param {string} data.related_content_type - Related object content type (optional)
     * @param {number} data.related_object_id - Related object ID (optional)
     * @returns {Promise} API response
     */
    createTransaction: (data) => {
        return api.post('/transactions/', data);
    },

    /**
     * Update an existing transaction
     * @param {number} id - Transaction ID
     * @param {Object} data - Updated transaction data
     * @returns {Promise} API response
     */
    updateTransaction: (id, data) => {
        return api.put(`/transactions/${id}/`, data);
    },

    /**
     * Partially update an existing transaction
     * @param {number} id - Transaction ID
     * @param {Object} data - Partial transaction data
     * @returns {Promise} API response
     */
    patchTransaction: (id, data) => {
        return api.patch(`/transactions/${id}/`, data);
    },

    /**
     * Delete a transaction
     * @param {number} id - Transaction ID
     * @returns {Promise} API response
     */
    deleteTransaction: (id) => {
        return api.delete(`/transactions/${id}/`);
    },

    /**
     * Get transaction summary/statistics
     * @param {Object} params - Query parameters
     * @param {number} params.account_id - Filter by account ID
     * @param {number} params.balance_id - Filter by balance ID
     * @param {string} params.date_from - Start date filter
     * @param {string} params.date_to - End date filter
     * @returns {Promise} API response
     */
    getTransactionSummary: (params = {}) => {
        const queryParams = new URLSearchParams();
        
        if (params.account_id) queryParams.append('account_id', params.account_id);
        if (params.balance_id) queryParams.append('balance_id', params.balance_id);
        if (params.date_from) queryParams.append('date_from', params.date_from);
        if (params.date_to) queryParams.append('date_to', params.date_to);
        
        const queryString = queryParams.toString();
        return api.get(`/transactions/summary/${queryString ? `?${queryString}` : ''}`);
    }
};
