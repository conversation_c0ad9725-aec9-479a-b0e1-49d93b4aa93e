import api from './api';

/**
 * Warehouses API service
 * Handles all API calls related to warehouses CRUD operations
 */
export const warehousesService = {
    /**
     * Get all warehouses with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @returns {Promise} API response
     */
    getWarehouses: (params = {}) => {
        const queryParams = new URLSearchParams();

        // Add pagination parameters
        if (params.page) queryParams.append('page', params.page);
        if (params.page_size) queryParams.append('page_size', params.page_size);

        // Add search parameter
        if (params.search) queryParams.append('search', params.search);

        // Add ordering parameter
        if (params.ordering) queryParams.append('ordering', params.ordering);

        const queryString = queryParams.toString();
        const url = `/warehouses/${queryString ? `?${queryString}` : ''}`;

        return api.get(url);
    },

    /**
     * Get a single warehouse by ID
     * @param {number} id - Warehouse ID
     * @returns {Promise} API response
     */
    getWarehouse: (id) => {
        return api.get(`/warehouses/${id}/`);
    },

    /**
     * Create a new warehouse
     * @param {Object} warehouseData - Warehouse data
     * @param {string} warehouseData.name - Warehouse name
     * @param {string} warehouseData.location - Warehouse location
     * @param {string} warehouseData.description - Warehouse description (optional)
     * @returns {Promise} API response
     */
    createWarehouse: (warehouseData) => {
        return api.post('/warehouses/', warehouseData);
    },

    /**
     * Update an existing warehouse
     * @param {number} id - Warehouse ID
     * @param {Object} warehouseData - Updated warehouse data
     * @returns {Promise} API response
     */
    updateWarehouse: (id, warehouseData) => {
        return api.put(`/warehouses/${id}/`, warehouseData);
    },

    /**
     * Delete a warehouse
     * @param {number} id - Warehouse ID
     * @returns {Promise} API response
     */
    deleteWarehouse: (id) => {
        return api.delete(`/warehouses/${id}/`);
    }
};

export default warehousesService;
