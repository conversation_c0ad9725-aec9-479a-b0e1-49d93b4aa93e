import api from './api';

/**
 * Attendance API service
 * Handles all API calls related to attendance operations
 */
export const attendanceService = {
    /**
     * Get all attendance records with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {number} params.employee - Filter by employee ID
     * @param {string} params.time_in__date - Filter by date (YYYY-MM-DD)
     * @param {string} params.time_in__gte - Filter by date range start
     * @param {string} params.time_in__lte - Filter by date range end
     * @returns {Promise} API response
     */
    getAttendanceRecords: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/attendance/?${queryString}` : '/attendance/';

        return api.get(url);
    },

    /**
     * Get a single attendance record by ID
     * @param {number} id - Attendance record ID
     * @returns {Promise} API response
     */
    getAttendanceRecord: (id) => {
        return api.get(`/attendance/${id}/`);
    },

    /**
     * Record time-in for an employee
     * @param {Object} timeInData - Time-in data
     * @param {string} timeInData.username - Employee username
     * @param {string} timeInData.password - Employee password
     * @param {File} timeInData.image - Time-in image
     * @param {string} timeInData.notes - Optional notes
     * @returns {Promise} API response
     */
    recordTimeIn: (timeInData) => {
        const formData = new FormData();

        Object.keys(timeInData).forEach(key => {
            if (timeInData[key] !== null && timeInData[key] !== undefined) {
                if (key === 'image' && timeInData[key] instanceof File) {
                    formData.append(key, timeInData[key]);
                } else {
                    formData.append(key, timeInData[key]);
                }
            }
        });

        return api.post('/attendance/time-in/', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Record time-out for an employee
     * @param {Object} timeOutData - Time-out data
     * @param {string} timeOutData.username - Employee username
     * @param {string} timeOutData.password - Employee password
     * @param {File} timeOutData.image - Time-out image
     * @param {string} timeOutData.notes - Optional notes
     * @returns {Promise} API response
     */
    recordTimeOut: (timeOutData) => {
        const formData = new FormData();

        Object.keys(timeOutData).forEach(key => {
            if (timeOutData[key] !== null && timeOutData[key] !== undefined) {
                if (key === 'image' && timeOutData[key] instanceof File) {
                    formData.append(key, timeOutData[key]);
                } else {
                    formData.append(key, timeOutData[key]);
                }
            }
        });

        return api.post('/attendance/time-out/', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * Get attendance records for a specific employee
     * @param {number} employeeId - Employee ID
     * @param {Object} params - Additional query parameters
     * @returns {Promise} API response
     */
    getEmployeeAttendance: (employeeId, params = {}) => {
        const queryParams = new URLSearchParams();
        queryParams.append('employee', employeeId);

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = `/attendance/?${queryString}`;

        return api.get(url);
    },

    /**
     * Get attendance statistics for reporting
     * @param {Object} params - Query parameters for statistics
     * @param {string} params.start_date - Start date for statistics (YYYY-MM-DD)
     * @param {string} params.end_date - End date for statistics (YYYY-MM-DD)
     * @param {number} params.employee - Filter by employee ID (optional)
     * @returns {Promise} API response
     */
    getAttendanceStats: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/attendance/stats/?${queryString}` : '/attendance/stats/';

        return api.get(url);
    },

    /**
     * Get attendance calendar data for a specific month
     * @param {Object} params - Calendar parameters
     * @param {number} params.year - Year (YYYY)
     * @param {number} params.month - Month (1-12)
     * @param {number} params.employee - Employee ID (optional)
     * @returns {Promise} API response
     */
    getAttendanceCalendar: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/attendance/calendar/?${queryString}` : '/attendance/calendar/';

        return api.get(url);
    }
};

export default attendanceService;
