import api from './api';

/**
 * Salaries API service
 * Handles all API calls related to salary operations
 */
export const salariesService = {
    /**
     * Get all salary records with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {number} params.employee - Filter by employee ID
     * @param {number} params.salary__gte - Filter by minimum salary
     * @param {number} params.salary__lte - Filter by maximum salary
     * @param {string} params.created__date - Filter by creation date (YYYY-MM-DD)
     * @param {string} params.created__gte - Filter by date range start
     * @param {string} params.created__lte - Filter by date range end
     * @param {string} params.employee__type - Filter by employee type
     * @param {string} params.employee__status - Filter by employee status
     * @param {number} params.employee__pos - Filter by POS terminal ID
     * @returns {Promise} API response
     */
    getSalaries: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/salaries/?${queryString}` : '/salaries/';

        return api.get(url);
    },

    /**
     * Get a single salary record by ID
     * @param {number} id - Salary record ID
     * @returns {Promise} API response
     */
    getSalary: (id) => {
        return api.get(`/salaries/${id}/`);
    },

    /**
     * Calculate salary for an employee based on attendance
     * @param {Object} salaryData - Salary calculation data
     * @param {number} salaryData.employee - Employee ID
     * @param {number} salaryData.attendance - Attendance record ID
     * @param {number} salaryData.salary - Calculated salary amount
     * @param {string} salaryData.notes - Optional notes
     * @returns {Promise} API response
     */
    calculateSalary: (salaryData) => {
        return api.post('/salaries/calculate/', salaryData);
    },

    /**
     * Create a new salary record
     * @param {Object} salaryData - Salary data
     * @param {number} salaryData.employee - Employee ID
     * @param {number} salaryData.attendance - Attendance record ID
     * @param {number} salaryData.salary - Salary amount
     * @param {string} salaryData.notes - Optional notes
     * @returns {Promise} API response
     */
    createSalary: (salaryData) => {
        return api.post('/salaries/', salaryData);
    },

    /**
     * Update an existing salary record
     * @param {number} id - Salary record ID
     * @param {Object} salaryData - Updated salary data
     * @returns {Promise} API response
     */
    updateSalary: (id, salaryData) => {
        return api.patch(`/salaries/${id}/`, salaryData);
    },

    /**
     * Delete a salary record
     * @param {number} id - Salary record ID
     * @returns {Promise} API response
     */
    deleteSalary: (id) => {
        return api.delete(`/salaries/${id}/`);
    },

    /**
     * Mark salary as paid
     * @param {number} id - Salary record ID
     * @param {Object} paymentData - Payment data
     * @param {string} paymentData.payment_date - Payment date (YYYY-MM-DD)
     * @param {string} paymentData.payment_method - Payment method
     * @param {string} paymentData.notes - Optional notes
     * @returns {Promise} API response
     */
    markAsPaid: (id, paymentData) => {
        return api.post(`/salaries/${id}/pay/`, paymentData);
    },

    /**
     * Get salary history for a specific employee
     * @param {number} employeeId - Employee ID
     * @param {Object} params - Additional query parameters
     * @returns {Promise} API response
     */
    getEmployeeSalaryHistory: (employeeId, params = {}) => {
        const queryParams = new URLSearchParams();
        queryParams.append('employee', employeeId);

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = `/salaries/?${queryString}`;

        return api.get(url);
    },

    /**
     * Get salary statistics and reports
     * @param {Object} params - Query parameters for statistics
     * @param {string} params.start_date - Start date for statistics (YYYY-MM-DD)
     * @param {string} params.end_date - End date for statistics (YYYY-MM-DD)
     * @param {number} params.employee - Filter by employee ID (optional)
     * @param {string} params.employee__type - Filter by employee type (optional)
     * @returns {Promise} API response
     */
    getSalaryStats: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/salaries/stats/?${queryString}` : '/salaries/stats/';

        return api.get(url);
    },

    /**
     * Generate payslip data for a salary record
     * @param {number} id - Salary record ID
     * @returns {Promise} API response
     */
    generatePayslip: (id) => {
        return api.get(`/salaries/${id}/payslip/`);
    },

    /**
     * Get pending salary calculations (attendance records without salary)
     * @param {Object} params - Query parameters
     * @returns {Promise} API response
     */
    getPendingSalaries: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/salaries/pending/?${queryString}` : '/salaries/pending/';

        return api.get(url);
    }
};

export default salariesService;
