import api from './api';

/**
 * POS Terminals API service
 * Handles all API calls related to POS terminals CRUD operations
 */
export const posService = {
    /**
     * Get all POS terminals with optional pagination, search, and filtering
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number
     * @param {number} params.page_size - Items per page
     * @param {string} params.search - Search term
     * @param {string} params.ordering - Ordering field
     * @param {number} params.warehouse - Filter by warehouse ID
     * @returns {Promise} API response
     */
    getPOSTerminals: (params = {}) => {
        const queryParams = new URLSearchParams();

        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        const queryString = queryParams.toString();
        const url = queryString ? `/pos/?${queryString}` : '/pos/';

        return api.get(url);
    },

    /**
     * Get a single POS terminal by ID
     * @param {number} id - POS terminal ID
     * @returns {Promise} API response
     */
    getPOSTerminal: (id) => {
        return api.get(`/pos/${id}/`);
    },

    /**
     * Create a new POS terminal
     * @param {Object} posData - POS terminal data
     * @param {string} posData.name - Terminal name
     * @param {string} posData.description - Terminal description (optional)
     * @param {number} posData.warehouse - Warehouse ID
     * @returns {Promise} API response
     */
    createPOSTerminal: (posData) => {
        return api.post('/pos/', posData);
    },

    /**
     * Update an existing POS terminal
     * @param {number} id - POS terminal ID
     * @param {Object} posData - Updated POS terminal data
     * @returns {Promise} API response
     */
    updatePOSTerminal: (id, posData) => {
        return api.put(`/pos/${id}/`, posData);
    },

    /**
     * Delete a POS terminal
     * @param {number} id - POS terminal ID
     * @returns {Promise} API response
     */
    deletePOSTerminal: (id) => {
        return api.delete(`/pos/${id}/`);
    },

    /**
     * Start a new session for a POS terminal
     * @param {number} posId - POS terminal ID
     * @param {Object} sessionData - Session data
     * @param {number} sessionData.opening_balance - Opening balance
     * @returns {Promise} API response
     */
    startSession: (posId, sessionData) => {
        return api.post(`/pos/${posId}/start_session/`, sessionData);
    }
};
