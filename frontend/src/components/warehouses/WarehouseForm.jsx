import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Form, Row, Spinner} from 'react-bootstrap';

/**
 * Reusable form component for creating and editing warehouses
 * @param {Object} props - Component props
 * @param {Object} props.initialData - Initial form data for edit mode
 * @param {Function} props.onSubmit - Form submission handler
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {boolean} props.isEdit - Whether this is edit mode
 */
const WarehouseForm = ({
    initialData = {},
    onSubmit,
    loading = false,
    error = null,
    isEdit = false
}) => {
    const [formData, setFormData] = useState({
        name: '',
        location: '',
        description: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                location: initialData.location || '',
                description: initialData.description || ''
            });
        }
    }, [initialData]);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.name.trim()) {
            errors.name = 'Warehouse name is required';
        } else if (formData.name.trim().length < 2) {
            errors.name = 'Warehouse name must be at least 2 characters long';
        } else if (formData.name.trim().length > 100) {
            errors.name = 'Warehouse name must not exceed 100 characters';
        }

        if (!formData.location.trim()) {
            errors.location = 'Warehouse location is required';
        } else if (formData.location.trim().length < 2) {
            errors.location = 'Warehouse location must be at least 2 characters long';
        } else if (formData.location.trim().length > 200) {
            errors.location = 'Warehouse location must not exceed 200 characters';
        }

        if (formData.description && formData.description.length > 1000) {
            errors.description = 'Description must not exceed 1000 characters';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            name: formData.name.trim(),
            location: formData.location.trim(),
            description: formData.description.trim() || null
        };

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Warehouse' : 'Create New Warehouse'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Warehouse Name <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.name}
                                    placeholder="Enter warehouse name"
                                    maxLength={100}
                                    aria-describedby="name-help"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.name}
                                </Form.Control.Feedback>
                                <Form.Text id="name-help" className="text-muted">
                                    {formData.name.length}/100 characters
                                </Form.Text>
                            </Form.Group>
                        </Col>

                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    Location <span className="text-danger">*</span>
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    name="location"
                                    value={formData.location}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.location}
                                    placeholder="Enter warehouse location"
                                    maxLength={200}
                                    aria-describedby="location-help"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.location}
                                </Form.Control.Feedback>
                                <Form.Text id="location-help" className="text-muted">
                                    {formData.location.length}/200 characters
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Form.Group className="mb-3">
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={4}
                            name="description"
                            value={formData.description}
                            onChange={handleChange}
                            isInvalid={!!validationErrors.description}
                            placeholder="Enter warehouse description (optional)"
                            maxLength={1000}
                            aria-describedby="description-help"
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.description}
                        </Form.Control.Feedback>
                        <Form.Text id="description-help" className="text-muted">
                            {formData.description.length}/1000 characters
                        </Form.Text>
                    </Form.Group>

                    <div className="d-flex gap-2">
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading}
                            className="me-2"
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Warehouse' : 'Create Warehouse'
                            )}
                        </Button>
                        <Button
                            variant="secondary"
                            type="button"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default WarehouseForm;
