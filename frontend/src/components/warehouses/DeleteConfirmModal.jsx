import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';

/**
 * Delete confirmation modal for warehouses
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to hide the modal
 * @param {Function} props.onConfirm - Function to confirm deletion
 * @param {string} props.warehouseName - Name of the warehouse to delete
 * @param {boolean} props.loading - Loading state during deletion
 */
const DeleteConfirmModal = ({
    show,
    onHide,
    onConfirm,
    warehouseName,
    loading = false
}) => {
    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Delete</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>
                    Are you sure you want to delete the warehouse{' '}
                    <strong>"{warehouseName}"</strong>?
                </p>
                <p className="text-muted">
                    This action cannot be undone. The warehouse will be permanently removed
                    from the system.
                </p>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="secondary"
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onConfirm}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Deleting...
                        </>
                    ) : (
                        'Delete Warehouse'
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
