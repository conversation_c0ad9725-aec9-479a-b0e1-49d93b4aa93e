import React, { useState, useEffect } from 'react';
import {
    Container,
    <PERSON>,
    Col,
    <PERSON><PERSON>,
    Spin<PERSON>,
    Card,
    Button,
    Table,
    Badge,
    Modal,
    Pagination,
    Form
} from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useWarehouse, useWarehouses } from '../../hooks/useWarehouses';
import { useStockItems } from '../../hooks/useStockItems';
import WarehouseForm from './WarehouseForm';
import StockItemForm from './StockItemForm';
import DeleteStockItemModal from './DeleteStockItemModal';

/**
 * Component for editing an existing warehouse
 * @returns {JSX.Element} The warehouse edit component
 */
const WarehouseEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { warehouse, loading: fetchLoading, error: fetchError } = useWarehouse(id);
    const { updateWarehouse } = useWarehouses();
    const {
        stockItems,
        loading: stockItemsLoading,
        error: stockItemsError,
        pagination,
        fetchStockItems,
        createStockItem,
        updateStockItem,
        deleteStockItem
    } = useStockItems(id);

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    // State for stock items filtering and pagination
    const [searchTerm, setSearchTerm] = useState('');
    const [ordering, setOrdering] = useState('product__name');

    // State for stock item form modal
    const [showStockItemModal, setShowStockItemModal] = useState(false);
    const [stockItemFormData, setStockItemFormData] = useState({});
    const [stockItemFormError, setStockItemFormError] = useState(null);
    const [stockItemFormLoading, setStockItemFormLoading] = useState(false);
    const [stockItemFormMode, setStockItemFormMode] = useState('add'); // 'add' or 'edit'

    // State for delete confirmation modal
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [stockItemToDelete, setStockItemToDelete] = useState(null);

    // Set error from fetch if it exists
    useEffect(() => {
        if (fetchError) {
            setError(fetchError);
        }
    }, [fetchError]);

    // Load stock items with pagination on component mount
    useEffect(() => {
        if (id) {
            fetchStockItems({
                page: 1,
                page_size: 10,
                ordering: 'product__name'
            });
        }
    }, [id, fetchStockItems]);

    // Handle opening the add stock item modal
    const handleAddStockItem = () => {
        setStockItemFormData({});
        setStockItemFormError(null);
        setStockItemFormMode('add');
        setShowStockItemModal(true);
    };

    // Handle opening the edit stock item modal
    const handleEditStockItem = (stockItem) => {
        setStockItemFormData(stockItem);
        setStockItemFormError(null);
        setStockItemFormMode('edit');
        setShowStockItemModal(true);
    };

    // Handle stock item form submission
    const handleStockItemSubmit = async (data) => {
        try {
            setStockItemFormLoading(true);
            setStockItemFormError(null);

            if (stockItemFormMode === 'add') {
                await createStockItem(data);
            } else {
                await updateStockItem(stockItemFormData.id, data);
            }

            setShowStockItemModal(false);

            // Refresh the stock items list with current pagination, search, and sorting
            fetchStockItems({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            });
        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;

                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field])
                            ? errorData[field]
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setStockItemFormError(errorMessages.join(', '));
                } else {
                    setStockItemFormError(errorData.detail || errorData.message || 'Failed to save stock item');
                }
            } else {
                setStockItemFormError('Failed to save stock item. Please try again.');
            }
        } finally {
            setStockItemFormLoading(false);
        }
    };

    // Handle delete click
    const handleDeleteClick = (stockItem) => {
        setStockItemToDelete(stockItem);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteStockItem(stockItemToDelete.id);
            setShowDeleteModal(false);
            setStockItemToDelete(null);

            // Refresh the current page
            fetchStockItems({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            });
        } catch (err) {
            // Error is handled by the hook
        }
    };

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchStockItems({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchStockItems({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchStockItems({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // Previous button
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={!pagination.previous}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = 1; page <= totalPages; page++) {
            if (
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 2 && page <= currentPage + 2)
            ) {
                items.push(
                    <Pagination.Item
                        key={page}
                        active={page === currentPage}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </Pagination.Item>
                );
            } else if (
                page === currentPage - 3 ||
                page === currentPage + 3
            ) {
                items.push(<Pagination.Ellipsis key={`ellipsis-${page}`} />);
            }
        }

        // Next button
        items.push(
            <Pagination.Next
                key="next"
                disabled={!pagination.next}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return items;
    };

    const getSortIcon = (field) => {
        if (ordering === field) return ' ↑';
        if (ordering === `-${field}`) return ' ↓';
        return '';
    };

    /**
     * Handle form submission
     * @param {Object} warehouseData - The updated warehouse data
     */
    const handleSubmit = async (warehouseData) => {
        try {
            setLoading(true);
            setError(null);

            await updateWarehouse(id, warehouseData);

            setSuccess(true);

            // Redirect to warehouses list after a short delay
            setTimeout(() => {
                navigate('/warehouses');
            }, 1500);

        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;

                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field])
                            ? errorData[field]
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setError(errorMessages.join(', '));
                } else {
                    setError(errorData.detail || errorData.message || 'Failed to update warehouse');
                }
            } else {
                setError('Failed to update warehouse. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    // Show loading spinner while fetching warehouse data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Warehouse</h2>
                    </Col>
                </Row>
                <Row>
                    <Col className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading warehouse...</span>
                        </Spinner>
                        <div className="mt-2">Loading warehouse data...</div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if warehouse not found or fetch failed
    if (fetchError || !warehouse) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Warehouse</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {fetchError || 'Warehouse not found'}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Warehouse</h2>
                    <p className="text-muted">
                        Update the warehouse information below. Changes will be saved immediately.
                    </p>
                </Col>
            </Row>

            {success && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success">
                            Warehouse updated successfully! Redirecting to warehouses list...
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row>
                <Col>
                    <WarehouseForm
                        initialData={warehouse}
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>

            {/* Stock Items Section */}
            <Row className="mt-4">
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Stock Items</h5>
                            <Button
                                variant="primary"
                                size="sm"
                                onClick={handleAddStockItem}
                            >
                                Add Stock Item
                            </Button>
                        </Card.Header>
                        <Card.Body>
                            {/* Search and Filters */}
                            <Row className="mb-3">
                                <Col md={8}>
                                    <Form onSubmit={handleSearch}>
                                        <Form.Group className="d-flex">
                                            <Form.Control
                                                type="text"
                                                placeholder="Search stock items by product name..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                aria-label="Search stock items"
                                            />
                                            <Button variant="outline-secondary" type="submit" className="ms-2">
                                                Search
                                            </Button>
                                        </Form.Group>
                                    </Form>
                                </Col>
                                <Col md={4}>
                                    <Form.Select
                                        value={ordering}
                                        onChange={(e) => handleSort(e.target.value)}
                                        aria-label="Sort stock items"
                                    >
                                        <option value="product__name">Sort by Product Name (A-Z)</option>
                                        <option value="-product__name">Sort by Product Name (Z-A)</option>
                                        <option value="quantity">Sort by Quantity (Low-High)</option>
                                        <option value="-quantity">Sort by Quantity (High-Low)</option>
                                        <option value="modified">Sort by Last Updated (Oldest)</option>
                                        <option value="-modified">Sort by Last Updated (Newest)</option>
                                    </Form.Select>
                                </Col>
                            </Row>

                            {stockItemsError && (
                                <Alert variant="danger">
                                    {stockItemsError}
                                </Alert>
                            )}

                            {stockItemsLoading ? (
                                <div className="text-center py-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading stock items...</span>
                                    </Spinner>
                                    <div className="mt-2">Loading stock items...</div>
                                </div>
                            ) : stockItems.length === 0 ? (
                                <div className="text-center py-4">
                                    <p className="text-muted">No stock items found for this warehouse.</p>
                                    <Button
                                        variant="primary"
                                        onClick={handleAddStockItem}
                                    >
                                        Add First Stock Item
                                    </Button>
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped hover>
                                        <thead>
                                            <tr>
                                                <th onClick={() => handleSort('product__name')}>
                                                    Product{getSortIcon('product__name')}
                                                </th>
                                                <th onClick={() => handleSort('quantity')}>
                                                    Quantity{getSortIcon('quantity')}
                                                </th>
                                                <th onClick={() => handleSort('min_stock')}>
                                                    Min Stock{getSortIcon('min_stock')}
                                                </th>
                                                <th>Status</th>
                                                <th onClick={() => handleSort('modified')}>
                                                    Last Updated{getSortIcon('modified')}
                                                </th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {stockItems.map(item => (
                                                <tr key={item.id}>
                                                    <td>{item.product_name}</td>
                                                    <td>{item.quantity} {item.unit_type}</td>
                                                    <td>{item.min_stock} {item.unit_type}</td>
                                                    <td>
                                                        {item.quantity <= 0 ? (
                                                            <Badge bg="danger">Out of Stock</Badge>
                                                        ) : item.quantity <= item.min_stock ? (
                                                            <Badge bg="warning">Low Stock</Badge>
                                                        ) : (
                                                            <Badge bg="success">In Stock</Badge>
                                                        )}
                                                    </td>
                                                    <td>{new Date(item.modified).toLocaleDateString()}</td>
                                                    <td>
                                                        <div className="d-flex gap-1">
                                                            <Button
                                                                variant="outline-warning"
                                                                size="sm"
                                                                onClick={() => handleEditStockItem(item)}
                                                            >
                                                                Edit
                                                            </Button>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => handleDeleteClick(item)}
                                                            >
                                                                Delete
                                                            </Button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </Table>

                                    {/* Pagination */}
                                    {pagination.count > pagination.page_size && (
                                        <div className="d-flex justify-content-between align-items-center mt-3">
                                            <div className="text-muted">
                                                Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                                {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                                {pagination.count} stock items
                                            </div>
                                            <Pagination className="mb-0">
                                                {generatePaginationItems()}
                                            </Pagination>
                                        </div>
                                    )}
                                </>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Stock Item Form Modal */}
            <Modal
                show={showStockItemModal}
                onHide={() => setShowStockItemModal(false)}
                size="lg"
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        {stockItemFormMode === 'add' ? 'Add Stock Item' : 'Edit Stock Item'}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <StockItemForm
                        warehouseId={id}
                        initialData={stockItemFormData}
                        onSubmit={handleStockItemSubmit}
                        loading={stockItemFormLoading}
                        error={stockItemFormError}
                        isEdit={stockItemFormMode === 'edit'}
                        onCancel={() => setShowStockItemModal(false)}
                    />
                </Modal.Body>
            </Modal>

            {/* Delete Confirmation Modal */}
            <DeleteStockItemModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                productName={stockItemToDelete?.product_name}
                loading={stockItemsLoading}
            />
        </Container>
    );
};

export default WarehouseEdit;
