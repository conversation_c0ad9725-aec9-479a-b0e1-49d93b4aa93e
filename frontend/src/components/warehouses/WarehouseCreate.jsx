import React, { useState } from 'react';
import { Container, Row, Col, Al<PERSON>, Card } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useWarehouses } from '../../hooks/useWarehouses';
import WarehouseForm from './WarehouseForm';

/**
 * Component for creating a new warehouse
 * @returns {JSX.Element} The warehouse create component
 */
const WarehouseCreate = () => {
    const navigate = useNavigate();
    const { createWarehouse } = useWarehouses();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    /**
     * Handle form submission
     * @param {Object} warehouseData - The warehouse data to create
     */
    const handleSubmit = async (warehouseData) => {
        try {
            setLoading(true);
            setError(null);

            await createWarehouse(warehouseData);

            setSuccess(true);

            // Redirect to warehouses list after a short delay
            setTimeout(() => {
                navigate('/warehouses');
            }, 1500);

        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;

                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field])
                            ? errorData[field]
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setError(errorMessages.join(', '));
                } else {
                    setError(errorData.detail || errorData.message || 'Failed to create warehouse');
                }
            } else {
                setError('Failed to create warehouse. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    if (success) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="success" className="text-center">
                            <h4>Warehouse Created Successfully!</h4>
                            <p>Redirecting to warehouses list...</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Warehouse</h2>
                    <p className="text-muted">
                        Add a new warehouse to your system. Fill in the required information below.
                    </p>
                </Col>
            </Row>

            <Row>
                <Col>
                    <WarehouseForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>

            <Row className="mt-4">
                <Col>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Stock Items</h5>
                        </Card.Header>
                        <Card.Body>
                            <p className="text-muted text-center">
                                You can add stock items after creating the warehouse.
                            </p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default WarehouseCreate;
