import React, { useState } from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useBalance, useBalances } from '../../hooks/useBalances';
import BalanceForm from './BalanceForm';

/**
 * Component for editing an existing balance
 * @returns {JSX.Element} The balance edit component
 */
const BalanceEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { balance, loading: fetchLoading, error: fetchError } = useBalance(id);
    const { updateBalance, loading: updateLoading } = useBalances();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} balanceData - The updated balance data
     */
    const handleSubmit = async (balanceData) => {
        try {
            setError('');
            const updatedBalance = await updateBalance(id, balanceData);
            
            // Navigate to the balance's view page
            navigate(`/balances/${updatedBalance.id}`, {
                state: { message: 'Balance updated successfully!' }
            });
        } catch (err) {
            console.error('Error updating balance:', err);
            setError(err.response?.data?.detail || 'Failed to update balance');
        }
    };

    // Show loading spinner while fetching balance data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Balance</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading balance...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if balance fetch failed
    if (fetchError) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Balance</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {fetchError}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if balance not found
    if (!balance) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Balance</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Balance not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Balance #{balance.id}</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <BalanceForm
                        initialData={balance}
                        onSubmit={handleSubmit}
                        loading={updateLoading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default BalanceEdit;
