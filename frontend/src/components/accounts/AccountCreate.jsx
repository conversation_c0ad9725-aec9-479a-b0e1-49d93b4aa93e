import React, { useState } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAccounts } from '../../hooks/useAccounts';
import AccountForm from './AccountForm';

/**
 * Component for creating a new account
 * @returns {JSX.Element} The account create component
 */
const AccountCreate = () => {
    const navigate = useNavigate();
    const { createAccount, loading } = useAccounts();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} accountData - The account data to create
     */
    const handleSubmit = async (accountData) => {
        try {
            setError('');
            const newAccount = await createAccount(accountData);
            
            // Navigate to the new account's view page
            navigate(`/accounts/${newAccount.id}`, {
                state: { message: 'Account created successfully!' }
            });
        } catch (err) {
            console.error('Error creating account:', err);
            setError(err.response?.data?.detail || 'Failed to create account');
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Account</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <AccountForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default AccountCreate;
