import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    Col,
    Container,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTransaction } from '../../hooks/useTransactions';
import { formatCurrency } from '../../utils';

const TransactionView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const location = useLocation();
    const { currentUser } = useAuth();
    const { transaction, loading, error } = useTransaction(id);
    const [successMessage, setSuccessMessage] = useState('');

    // Show success message from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    // Get transaction type badge variant
    const getTransactionTypeBadge = (type) => {
        return type === 'credit' ? 'success' : 'danger';
    };

    // Show loading spinner
    if (loading) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading transaction...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if transaction fetch failed
    if (error) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {error}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if transaction not found
    if (!transaction) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Transaction not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            {/* Success Message */}
            {successMessage && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Transaction #{transaction.id}</h2>
                            <div className="d-flex align-items-center gap-2">
                                <Badge bg={getTransactionTypeBadge(transaction.type)}>
                                    {transaction.type}
                                </Badge>
                                <span className="text-muted">
                                    Amount: <strong>{formatCurrency(transaction.amount)}</strong>
                                </span>
                            </div>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/transactions')}
                            >
                                Back to Transactions
                            </Button>
                            {currentUser && (
                                <Button
                                    variant="primary"
                                    onClick={() => navigate(`/transactions/${transaction.id}/edit`)}
                                >
                                    Edit Transaction
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Transaction Details */}
            <Row className="mb-4">
                <Col md={8}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Transaction Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Table borderless>
                                <tbody>
                                    <tr>
                                        <td><strong>ID:</strong></td>
                                        <td>{transaction.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>
                                            <Badge bg={getTransactionTypeBadge(transaction.type)}>
                                                {transaction.type}
                                            </Badge>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Amount:</strong></td>
                                        <td>{formatCurrency(transaction.amount)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Balance:</strong></td>
                                        <td>
                                            {transaction.balance ? (
                                                <Button
                                                    variant="link"
                                                    className="p-0"
                                                    onClick={() => navigate(`/balances/${transaction.balance.id}`)}
                                                >
                                                    Balance #{transaction.balance.id}
                                                </Button>
                                            ) : (
                                                'N/A'
                                            )}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Description:</strong></td>
                                        <td>{transaction.description || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Related Entity:</strong></td>
                                        <td>
                                            {transaction.related_content_type && transaction.related_object_id ? 
                                                `${transaction.related_content_type} #${transaction.related_object_id}` : 
                                                'None'
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{new Date(transaction.created_at).toLocaleString()}</td>
                                    </tr>
                                    {transaction.updated_at && (
                                        <tr>
                                            <td><strong>Last Updated:</strong></td>
                                            <td>{new Date(transaction.updated_at).toLocaleString()}</td>
                                        </tr>
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Quick Actions</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-grid gap-2">
                                {transaction.balance && (
                                    <Button
                                        variant="outline-primary"
                                        onClick={() => navigate(`/balances/${transaction.balance.id}`)}
                                    >
                                        View Balance
                                    </Button>
                                )}
                                {transaction.balance?.account && (
                                    <Button
                                        variant="outline-info"
                                        onClick={() => navigate(`/accounts/${transaction.balance.account.id}`)}
                                    >
                                        View Account
                                    </Button>
                                )}
                                {currentUser && (
                                    <Button
                                        variant="outline-success"
                                        onClick={() => navigate(`/transactions/create?balance_id=${transaction.balance?.id}`)}
                                    >
                                        Create Related Transaction
                                    </Button>
                                )}
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Transaction Impact */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Transaction Impact</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="row">
                                <div className="col-md-6">
                                    <h6>Balance Effect</h6>
                                    <p className="mb-0">
                                        This {transaction.type} transaction{' '}
                                        <strong>
                                            {transaction.type === 'credit' ? 'increased' : 'decreased'}
                                        </strong>{' '}
                                        the balance by <strong>{formatCurrency(transaction.amount)}</strong>.
                                    </p>
                                </div>
                                <div className="col-md-6">
                                    <h6>Transaction Summary</h6>
                                    <p className="mb-0">
                                        {transaction.type === 'credit' ? 
                                            'Money was added to the balance.' : 
                                            'Money was deducted from the balance.'
                                        }
                                    </p>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default TransactionView;
