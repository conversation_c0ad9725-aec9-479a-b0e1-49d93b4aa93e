import React, { useState, useEffect } from 'react';
import { Container, Row, Col, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useAccount, useAccounts } from '../../hooks/useAccounts';
import AccountForm from './AccountForm';

/**
 * Component for editing an existing account
 * @returns {JSX.Element} The account edit component
 */
const AccountEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { account, loading: fetchLoading, error: fetchError } = useAccount(id);
    const { updateAccount, loading: updateLoading } = useAccounts();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} accountData - The updated account data
     */
    const handleSubmit = async (accountData) => {
        try {
            setError('');
            const updatedAccount = await updateAccount(id, accountData);
            
            // Navigate to the account's view page
            navigate(`/accounts/${updatedAccount.id}`, {
                state: { message: 'Account updated successfully!' }
            });
        } catch (err) {
            console.error('Error updating account:', err);
            setError(err.response?.data?.detail || 'Failed to update account');
        }
    };

    // Show loading spinner while fetching account data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Account</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading account...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if account fetch failed
    if (fetchError) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Account</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {fetchError}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if account not found
    if (!account) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Account</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Account not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Account: {account.name}</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <AccountForm
                        initialData={account}
                        onSubmit={handleSubmit}
                        loading={updateLoading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default AccountEdit;
