import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    Al<PERSON>,
    Spin<PERSON>,
    Row,
    Col
} from 'react-bootstrap';
import { useSearchParams } from 'react-router-dom';

const BalanceForm = ({
    initialData = {},
    onSubmit,
    loading = false,
    error = null,
    isEdit = false
}) => {
    const [searchParams] = useSearchParams();
    const [formData, setFormData] = useState({
        account_id: searchParams.get('account_id') || '',
        pos_id: '',
        amount: '',
        notes: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                account_id: initialData.account_id || initialData.account?.id || searchParams.get('account_id') || '',
                pos_id: initialData.pos_id || initialData.pos?.id || '',
                amount: initialData.amount || '',
                notes: initialData.notes || ''
            });
        }
    }, [initialData, searchParams]);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        // Account ID validation
        if (!formData.account_id) {
            errors.account_id = 'Account is required';
        } else if (!Number.isInteger(Number(formData.account_id)) || Number(formData.account_id) <= 0) {
            errors.account_id = 'Account ID must be a positive integer';
        }

        // POS ID validation
        if (!formData.pos_id) {
            errors.pos_id = 'POS Terminal is required';
        } else if (!Number.isInteger(Number(formData.pos_id)) || Number(formData.pos_id) <= 0) {
            errors.pos_id = 'POS ID must be a positive integer';
        }

        // Amount validation
        if (!formData.amount) {
            errors.amount = 'Amount is required';
        } else if (isNaN(Number(formData.amount))) {
            errors.amount = 'Amount must be a valid number';
        } else if (Number(formData.amount) < 0) {
            errors.amount = 'Amount cannot be negative';
        }

        // Notes validation (optional but with length limit)
        if (formData.notes && formData.notes.length > 500) {
            errors.notes = 'Notes must be less than 500 characters';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            account_id: parseInt(formData.account_id),
            pos_id: parseInt(formData.pos_id),
            amount: parseFloat(formData.amount),
            notes: formData.notes.trim() || null
        };

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Balance' : 'Create New Balance'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Account ID *</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="account_id"
                                    value={formData.account_id}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.account_id}
                                    placeholder="Enter account ID"
                                    min="1"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.account_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    The account this balance belongs to
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>POS Terminal ID *</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="pos_id"
                                    value={formData.pos_id}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.pos_id}
                                    placeholder="Enter POS terminal ID"
                                    min="1"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.pos_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    The POS terminal associated with this balance
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Amount *</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    name="amount"
                                    value={formData.amount}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.amount}
                                    placeholder="Enter amount"
                                    min="0"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.amount}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Balance amount in EGP
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Notes</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    name="notes"
                                    value={formData.notes}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.notes}
                                    placeholder="Enter notes (optional)"
                                    maxLength={500}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.notes}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Optional notes about this balance ({formData.notes.length}/500 characters)
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <div className="d-flex justify-content-end gap-2">
                        <Button
                            variant="secondary"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Balance' : 'Create Balance'
                            )}
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default BalanceForm;
