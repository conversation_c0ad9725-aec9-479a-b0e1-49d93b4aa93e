import React, { useState } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useBalances } from '../../hooks/useBalances';
import BalanceForm from './BalanceForm';

/**
 * Component for creating a new balance
 * @returns {JSX.Element} The balance create component
 */
const BalanceCreate = () => {
    const navigate = useNavigate();
    const { createBalance, loading } = useBalances();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} balanceData - The balance data to create
     */
    const handleSubmit = async (balanceData) => {
        try {
            setError('');
            const newBalance = await createBalance(balanceData);
            
            // Navigate to the new balance's view page
            navigate(`/balances/${newBalance.id}`, {
                state: { message: 'Balance created successfully!' }
            });
        } catch (err) {
            console.error('Error creating balance:', err);
            setError(err.response?.data?.detail || 'Failed to create balance');
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Balance</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <BalanceForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default BalanceCreate;
