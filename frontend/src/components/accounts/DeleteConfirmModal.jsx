import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

/**
 * Reusable delete confirmation modal component
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to call when hiding the modal
 * @param {Function} props.onConfirm - Function to call when confirming deletion
 * @param {string} props.itemName - Name of the item being deleted
 * @param {string} props.itemType - Type of the item being deleted (e.g., 'account', 'balance', 'transaction')
 * @param {boolean} props.loading - Whether the deletion is in progress
 * @returns {JSX.Element} The delete confirmation modal
 */
const DeleteConfirmModal = ({
    show,
    onHide,
    onConfirm,
    itemName,
    itemType = 'item',
    loading = false
}) => {
    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Deletion</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Alert variant="warning" className="mb-3">
                    <Alert.Heading>Warning!</Alert.Heading>
                    This action cannot be undone.
                </Alert>
                <p>
                    Are you sure you want to delete the {itemType}{' '}
                    <strong>"{itemName}"</strong>?
                </p>
                {itemType === 'account' && (
                    <p className="text-muted">
                        <small>
                            Note: Deleting an account will also remove all associated balances and transactions.
                        </small>
                    </p>
                )}
                {itemType === 'balance' && (
                    <p className="text-muted">
                        <small>
                            Note: Deleting a balance will also remove all associated transactions.
                        </small>
                    </p>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="secondary"
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onConfirm}
                    disabled={loading}
                >
                    {loading ? 'Deleting...' : 'Delete'}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
