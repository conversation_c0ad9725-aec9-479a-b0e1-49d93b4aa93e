import React, { useState, useEffect } from 'react';
import {
    Container,
    Row,
    Col,
    Table,
    Button,
    Form,
    InputGroup,
    <PERSON><PERSON>,
    Spin<PERSON>,
    Badge,
    Card,
    Pagination
} from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAccounts } from '../../hooks/useAccounts';
import { useAuth } from '../../contexts/AuthContext';
import DeleteConfirmModal from './DeleteConfirmModal';
import { formatCurrency } from "../../utils";

const AccountsList = () => {
    const navigate = useNavigate();
    const { currentUser } = useAuth();
    const {
        accounts,
        loading,
        error,
        pagination,
        fetchAccounts,
        deleteAccount,
        setError
    } = useAccounts();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [accountTypeFilter, setAccountTypeFilter] = useState('');
    const [ordering, setOrdering] = useState('name');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [accountToDelete, setAccountToDelete] = useState(null);

    // Load accounts on component mount
    useEffect(() => {
        fetchAccounts({
            page: 1,
            page_size: 10,
            ordering: 'name'
        });
    }, [fetchAccounts]);

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchAccounts({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            account_type: accountTypeFilter,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchAccounts({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            account_type: accountTypeFilter,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchAccounts({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            account_type: accountTypeFilter,
            ordering
        });
    };

    // Handle delete confirmation
    const handleDeleteClick = (account) => {
        setAccountToDelete(account);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteAccount(accountToDelete.id);
            setShowDeleteModal(false);
            setAccountToDelete(null);

            // Refresh the current page
            fetchAccounts({
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                account_type: accountTypeFilter,
                ordering
            });
        } catch (err) {
            console.error('Error deleting account:', err);
        }
    };

    // Handle page size change
    const handlePageSizeChange = (newPageSize) => {
        fetchAccounts({
            page: 1,
            page_size: newPageSize,
            search: searchTerm,
            account_type: accountTypeFilter,
            ordering
        });
    };

    // Get account type badge variant
    const getAccountTypeBadge = (accountType) => {
        const variants = {
            'asset': 'success',
            'liability': 'danger',
            'equity': 'primary',
            'revenue': 'info',
            'expense': 'warning'
        };
        return variants[accountType] || 'secondary';
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // First page
        items.push(
            <Pagination.First
                key="first"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(1)}
            />
        );

        // Previous page
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers (show 5 pages around current)
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Next page
        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        // Last page
        items.push(
            <Pagination.Last
                key="last"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(totalPages)}
            />
        );

        return items;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Accounts</h2>
                        {currentUser && (
                            <Button
                                variant="primary"
                                onClick={() => navigate('/accounts/create')}
                            >
                                Add Account
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Section */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Form onSubmit={handleSearch}>
                                <Row>
                                    <Col md={6}>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder="Search accounts..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                            <Button variant="outline-secondary" type="submit">
                                                Search
                                            </Button>
                                        </InputGroup>
                                    </Col>
                                    <Col md={4}>
                                        <Form.Select
                                            value={accountTypeFilter}
                                            onChange={(e) => setAccountTypeFilter(e.target.value)}
                                        >
                                            <option value="">All Account Types</option>
                                            <option value="asset">Asset</option>
                                            <option value="liability">Liability</option>
                                            <option value="equity">Equity</option>
                                            <option value="revenue">Revenue</option>
                                            <option value="expense">Expense</option>
                                        </Form.Select>
                                    </Col>
                                    <Col md={2}>
                                        <Button variant="secondary" onClick={handleSearch} className="w-100">
                                            Filter
                                        </Button>
                                    </Col>
                                </Row>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError(null)}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Accounts Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Accounts List</h5>
                            <div className="d-flex align-items-center">
                                <span className="me-2">Show:</span>
                                <Form.Select
                                    size="sm"
                                    style={{ width: 'auto' }}
                                    value={pagination.page_size}
                                    onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                                >
                                    <option value={10}>10</option>
                                    <option value={25}>25</option>
                                    <option value={50}>50</option>
                                    <option value={100}>100</option>
                                </Form.Select>
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : (
                                <Table responsive hover>
                                    <thead>
                                        <tr>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('id')}
                                            >
                                                ID {ordering === 'id' ? '↑' : ordering === '-id' ? '↓' : ''}
                                            </th>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('name')}
                                            >
                                                Name {ordering === 'name' ? '↑' : ordering === '-name' ? '↓' : ''}
                                            </th>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('account_type')}
                                            >
                                                Type {ordering === 'account_type' ? '↑' : ordering === '-account_type' ? '↓' : ''}
                                            </th>
                                            <th>Balance</th>
                                            <th>Associated Entity</th>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('created_at')}
                                            >
                                                Created {ordering === 'created_at' ? '↑' : ordering === '-created_at' ? '↓' : ''}
                                            </th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {accounts.length === 0 ? (
                                            <tr>
                                                <td colSpan="7" className="text-center py-4">
                                                    No accounts found
                                                </td>
                                            </tr>
                                        ) : (
                                            accounts.map((account) => (
                                                <tr key={account.id}>
                                                    <td>{account.id}</td>
                                                    <td>
                                                        <strong>{account.name}</strong>
                                                    </td>
                                                    <td>
                                                        <Badge bg={getAccountTypeBadge(account.account_type)}>
                                                            {account.account_type}
                                                        </Badge>
                                                    </td>
                                                    <td>
                                                        {account.balance !== undefined ? 
                                                            formatCurrency(account.balance) : 
                                                            'N/A'
                                                        }
                                                    </td>
                                                    <td>
                                                        {account.content_object ? 
                                                            `${account.content_type} #${account.object_id}` : 
                                                            'None'
                                                        }
                                                    </td>
                                                    <td>
                                                        {new Date(account.created_at).toLocaleDateString()}
                                                    </td>
                                                    <td>
                                                        <div className="d-flex gap-2">
                                                            <Button
                                                                variant="outline-primary"
                                                                size="sm"
                                                                onClick={() => navigate(`/accounts/${account.id}`)}
                                                            >
                                                                View
                                                            </Button>
                                                            {currentUser && (
                                                                <>
                                                                    <Button
                                                                        variant="outline-secondary"
                                                                        size="sm"
                                                                        onClick={() => navigate(`/accounts/${account.id}/edit`)}
                                                                    >
                                                                        Edit
                                                                    </Button>
                                                                    <Button
                                                                        variant="outline-danger"
                                                                        size="sm"
                                                                        onClick={() => handleDeleteClick(account)}
                                                                    >
                                                                        Delete
                                                                    </Button>
                                                                </>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                        {pagination.count > 0 && (
                            <Card.Footer>
                                <div className="d-flex justify-content-between align-items-center">
                                    <span>
                                        Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                        {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                        {pagination.count} accounts
                                    </span>
                                    <Pagination className="mb-0">
                                        {generatePaginationItems()}
                                    </Pagination>
                                </div>
                            </Card.Footer>
                        )}
                    </Card>
                </Col>
            </Row>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                itemName={accountToDelete?.name}
                itemType="account"
            />
        </Container>
    );
};

export default AccountsList;
