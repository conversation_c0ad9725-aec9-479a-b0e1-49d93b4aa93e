import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    Col,
    Container,
    Row,
    Spinner,
    Table,
    Tabs,
    Tab
} from 'react-bootstrap';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useAccount, useAccountBalances } from '../../hooks/useAccounts';
import { formatCurrency } from '../../utils';

const AccountView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const location = useLocation();
    const { currentUser } = useAuth();
    const { account, loading: accountLoading, error: accountError } = useAccount(id);
    const { balances, loading: balancesLoading, error: balancesError } = useAccountBalances(id);
    const [successMessage, setSuccessMessage] = useState('');

    // Show success message from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    // Get account type badge variant
    const getAccountTypeBadge = (accountType) => {
        const variants = {
            'asset': 'success',
            'liability': 'danger',
            'equity': 'primary',
            'revenue': 'info',
            'expense': 'warning'
        };
        return variants[accountType] || 'secondary';
    };

    // Show loading spinner
    if (accountLoading) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading account...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if account fetch failed
    if (accountError) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {accountError}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if account not found
    if (!account) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Account not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            {/* Success Message */}
            {successMessage && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{account.name}</h2>
                            <Badge bg={getAccountTypeBadge(account.account_type)} className="me-2">
                                {account.account_type}
                            </Badge>
                            {account.content_object && (
                                <Badge bg="secondary">
                                    {account.content_type} #{account.object_id}
                                </Badge>
                            )}
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/accounts')}
                            >
                                Back to Accounts
                            </Button>
                            {currentUser && (
                                <Button
                                    variant="primary"
                                    onClick={() => navigate(`/accounts/${account.id}/edit`)}
                                >
                                    Edit Account
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Account Details */}
            <Row className="mb-4">
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Account Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Table borderless>
                                <tbody>
                                    <tr>
                                        <td><strong>ID:</strong></td>
                                        <td>{account.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{account.name}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>
                                            <Badge bg={getAccountTypeBadge(account.account_type)}>
                                                {account.account_type}
                                            </Badge>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Balance:</strong></td>
                                        <td>
                                            {account.balance !== undefined ? 
                                                formatCurrency(account.balance) : 
                                                'N/A'
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Associated Entity:</strong></td>
                                        <td>
                                            {account.content_object ? 
                                                `${account.content_type} #${account.object_id}` : 
                                                'None'
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{new Date(account.created_at).toLocaleString()}</td>
                                    </tr>
                                    {account.updated_at && (
                                        <tr>
                                            <td><strong>Last Updated:</strong></td>
                                            <td>{new Date(account.updated_at).toLocaleString()}</td>
                                        </tr>
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Quick Actions</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-grid gap-2">
                                <Button
                                    variant="outline-primary"
                                    onClick={() => navigate(`/balances?account_id=${account.id}`)}
                                >
                                    View All Balances
                                </Button>
                                <Button
                                    variant="outline-info"
                                    onClick={() => navigate(`/transactions?account_id=${account.id}`)}
                                >
                                    View All Transactions
                                </Button>
                                {currentUser && (
                                    <Button
                                        variant="outline-success"
                                        onClick={() => navigate(`/balances/create?account_id=${account.id}`)}
                                    >
                                        Create New Balance
                                    </Button>
                                )}
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Balances Section */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <div className="d-flex justify-content-between align-items-center">
                                <h5 className="mb-0">Account Balances</h5>
                                {currentUser && (
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={() => navigate(`/balances/create?account_id=${account.id}`)}
                                    >
                                        Add Balance
                                    </Button>
                                )}
                            </div>
                        </Card.Header>
                        <Card.Body>
                            {balancesLoading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading balances...</span>
                                    </Spinner>
                                </div>
                            ) : balancesError ? (
                                <Alert variant="danger">
                                    {balancesError}
                                </Alert>
                            ) : balances.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted">No balances found for this account.</p>
                                    {currentUser && (
                                        <Button
                                            variant="primary"
                                            onClick={() => navigate(`/balances/create?account_id=${account.id}`)}
                                        >
                                            Create First Balance
                                        </Button>
                                    )}
                                </div>
                            ) : (
                                <Table responsive hover>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>POS Terminal</th>
                                            <th>Amount</th>
                                            <th>Notes</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {balances.map((balance) => (
                                            <tr key={balance.id}>
                                                <td>{balance.id}</td>
                                                <td>
                                                    {balance.pos ? 
                                                        `${balance.pos.name} (#${balance.pos.id})` : 
                                                        'N/A'
                                                    }
                                                </td>
                                                <td>{formatCurrency(balance.amount)}</td>
                                                <td>{balance.notes || 'N/A'}</td>
                                                <td>{new Date(balance.created_at).toLocaleDateString()}</td>
                                                <td>
                                                    <div className="d-flex gap-2">
                                                        <Button
                                                            variant="outline-primary"
                                                            size="sm"
                                                            onClick={() => navigate(`/balances/${balance.id}`)}
                                                        >
                                                            View
                                                        </Button>
                                                        {currentUser && (
                                                            <Button
                                                                variant="outline-secondary"
                                                                size="sm"
                                                                onClick={() => navigate(`/balances/${balance.id}/edit`)}
                                                            >
                                                                Edit
                                                            </Button>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AccountView;
