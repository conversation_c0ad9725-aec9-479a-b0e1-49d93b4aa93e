import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    Spin<PERSON>,
    Row,
    Col
} from 'react-bootstrap';

const AccountForm = ({
    initialData = {},
    onSubmit,
    loading = false,
    error = null,
    isEdit = false
}) => {
    const [formData, setFormData] = useState({
        name: '',
        account_type: 'asset',
        content_type: '',
        object_id: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                account_type: initialData.account_type || 'asset',
                content_type: initialData.content_type || '',
                object_id: initialData.object_id || ''
            });
        }
    }, [initialData]);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        // Name validation
        if (!formData.name.trim()) {
            errors.name = 'Account name is required';
        } else if (formData.name.trim().length < 2) {
            errors.name = 'Account name must be at least 2 characters long';
        } else if (formData.name.trim().length > 100) {
            errors.name = 'Account name must be less than 100 characters';
        }

        // Account type validation
        if (!formData.account_type) {
            errors.account_type = 'Account type is required';
        }

        // Object ID validation (if content_type is provided)
        if (formData.content_type && !formData.object_id) {
            errors.object_id = 'Object ID is required when content type is specified';
        }

        // Object ID must be a positive integer if provided
        if (formData.object_id && (!Number.isInteger(Number(formData.object_id)) || Number(formData.object_id) <= 0)) {
            errors.object_id = 'Object ID must be a positive integer';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            name: formData.name.trim(),
            account_type: formData.account_type,
        };

        // Add content type and object ID if provided
        if (formData.content_type && formData.object_id) {
            submitData.content_type = formData.content_type;
            submitData.object_id = parseInt(formData.object_id);
        }

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Account' : 'Create New Account'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Account Name *</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.name}
                                    placeholder="Enter account name"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.name}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Account Type *</Form.Label>
                                <Form.Select
                                    name="account_type"
                                    value={formData.account_type}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.account_type}
                                >
                                    <option value="">Select account type</option>
                                    <option value="asset">Asset</option>
                                    <option value="liability">Liability</option>
                                    <option value="equity">Equity</option>
                                    <option value="revenue">Revenue</option>
                                    <option value="expense">Expense</option>
                                </Form.Select>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.account_type}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Content Type</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="content_type"
                                    value={formData.content_type}
                                    onChange={handleChange}
                                    placeholder="e.g., warehouse, employee"
                                />
                                <Form.Text className="text-muted">
                                    Optional: Link this account to a specific entity type
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Object ID</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="object_id"
                                    value={formData.object_id}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.object_id}
                                    placeholder="Enter object ID"
                                    min="1"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.object_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Required if content type is specified
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <div className="d-flex justify-content-end gap-2">
                        <Button
                            variant="secondary"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Account' : 'Create Account'
                            )}
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default AccountForm;
