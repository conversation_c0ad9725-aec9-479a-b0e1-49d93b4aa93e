import React, { useState } from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useTransaction, useTransactions } from '../../hooks/useTransactions';
import TransactionForm from './TransactionForm';

/**
 * Component for editing an existing transaction
 * @returns {JSX.Element} The transaction edit component
 */
const TransactionEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { transaction, loading: fetchLoading, error: fetchError } = useTransaction(id);
    const { updateTransaction, loading: updateLoading } = useTransactions();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} transactionData - The updated transaction data
     */
    const handleSubmit = async (transactionData) => {
        try {
            setError('');
            const updatedTransaction = await updateTransaction(id, transactionData);
            
            // Navigate to the transaction's view page
            navigate(`/transactions/${updatedTransaction.id}`, {
                state: { message: 'Transaction updated successfully!' }
            });
        } catch (err) {
            console.error('Error updating transaction:', err);
            setError(err.response?.data?.detail || 'Failed to update transaction');
        }
    };

    // Show loading spinner while fetching transaction data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Transaction</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading transaction...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if transaction fetch failed
    if (fetchError) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Transaction</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {fetchError}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if transaction not found
    if (!transaction) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Transaction</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Transaction not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Transaction #{transaction.id}</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <TransactionForm
                        initialData={transaction}
                        onSubmit={handleSubmit}
                        loading={updateLoading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default TransactionEdit;
