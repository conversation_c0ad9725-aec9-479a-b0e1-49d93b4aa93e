import React, { useState, useEffect } from 'react';
import {
    Container,
    Row,
    Col,
    Table,
    Button,
    Form,
    InputGroup,
    Al<PERSON>,
    Spinner,
    Card,
    Pagination
} from 'react-bootstrap';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useBalances } from '../../hooks/useBalances';
import { useAuth } from '../../contexts/AuthContext';
import DeleteConfirmModal from './DeleteConfirmModal';
import { formatCurrency } from "../../utils";

const BalancesList = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const { currentUser } = useAuth();
    const {
        balances,
        loading,
        error,
        pagination,
        fetchBalances,
        deleteBalance,
        setError
    } = useBalances();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [accountFilter, setAccountFilter] = useState(searchParams.get('account_id') || '');
    const [posFilter, setPosFilter] = useState('');
    const [ordering, setOrdering] = useState('-created_at');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [balanceToDelete, setBalanceToDelete] = useState(null);

    // Load balances on component mount
    useEffect(() => {
        const params = {
            page: 1,
            page_size: 10,
            ordering: '-created_at'
        };

        // Add account filter from URL params if present
        if (accountFilter) {
            params.account_id = accountFilter;
        }

        fetchBalances(params);
    }, [fetchBalances, accountFilter]);

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        const params = {
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        };

        if (accountFilter) params.account_id = accountFilter;
        if (posFilter) params.pos_id = posFilter;

        fetchBalances(params);
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        
        const params = {
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        };

        if (accountFilter) params.account_id = accountFilter;
        if (posFilter) params.pos_id = posFilter;

        fetchBalances(params);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        const params = {
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        };

        if (accountFilter) params.account_id = accountFilter;
        if (posFilter) params.pos_id = posFilter;

        fetchBalances(params);
    };

    // Handle delete confirmation
    const handleDeleteClick = (balance) => {
        setBalanceToDelete(balance);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        try {
            await deleteBalance(balanceToDelete.id);
            setShowDeleteModal(false);
            setBalanceToDelete(null);

            // Refresh the current page
            const params = {
                page: pagination.page,
                page_size: pagination.page_size,
                search: searchTerm,
                ordering
            };

            if (accountFilter) params.account_id = accountFilter;
            if (posFilter) params.pos_id = posFilter;

            fetchBalances(params);
        } catch (err) {
            console.error('Error deleting balance:', err);
        }
    };

    // Handle page size change
    const handlePageSizeChange = (newPageSize) => {
        const params = {
            page: 1,
            page_size: newPageSize,
            search: searchTerm,
            ordering
        };

        if (accountFilter) params.account_id = accountFilter;
        if (posFilter) params.pos_id = posFilter;

        fetchBalances(params);
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // First page
        items.push(
            <Pagination.First
                key="first"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(1)}
            />
        );

        // Previous page
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers (show 5 pages around current)
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Next page
        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        // Last page
        items.push(
            <Pagination.Last
                key="last"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(totalPages)}
            />
        );

        return items;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Balances</h2>
                        {currentUser && (
                            <Button
                                variant="primary"
                                onClick={() => navigate('/balances/create')}
                            >
                                Add Balance
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Section */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Form onSubmit={handleSearch}>
                                <Row>
                                    <Col md={4}>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder="Search balances..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                            <Button variant="outline-secondary" type="submit">
                                                Search
                                            </Button>
                                        </InputGroup>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Control
                                            type="number"
                                            placeholder="Account ID"
                                            value={accountFilter}
                                            onChange={(e) => setAccountFilter(e.target.value)}
                                        />
                                    </Col>
                                    <Col md={3}>
                                        <Form.Control
                                            type="number"
                                            placeholder="POS ID"
                                            value={posFilter}
                                            onChange={(e) => setPosFilter(e.target.value)}
                                        />
                                    </Col>
                                    <Col md={2}>
                                        <Button variant="secondary" onClick={handleSearch} className="w-100">
                                            Filter
                                        </Button>
                                    </Col>
                                </Row>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError(null)}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Balances Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Balances List</h5>
                            <div className="d-flex align-items-center">
                                <span className="me-2">Show:</span>
                                <Form.Select
                                    size="sm"
                                    style={{ width: 'auto' }}
                                    value={pagination.page_size}
                                    onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                                >
                                    <option value={10}>10</option>
                                    <option value={25}>25</option>
                                    <option value={50}>50</option>
                                    <option value={100}>100</option>
                                </Form.Select>
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : (
                                <Table responsive hover>
                                    <thead>
                                        <tr>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('id')}
                                            >
                                                ID {ordering === 'id' ? '↑' : ordering === '-id' ? '↓' : ''}
                                            </th>
                                            <th>Account</th>
                                            <th>POS Terminal</th>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('amount')}
                                            >
                                                Amount {ordering === 'amount' ? '↑' : ordering === '-amount' ? '↓' : ''}
                                            </th>
                                            <th>Notes</th>
                                            <th
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleSort('created_at')}
                                            >
                                                Created {ordering === 'created_at' ? '↑' : ordering === '-created_at' ? '↓' : ''}
                                            </th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {balances.length === 0 ? (
                                            <tr>
                                                <td colSpan="7" className="text-center py-4">
                                                    No balances found
                                                </td>
                                            </tr>
                                        ) : (
                                            balances.map((balance) => (
                                                <tr key={balance.id}>
                                                    <td>{balance.id}</td>
                                                    <td>
                                                        {balance.account ? 
                                                            `${balance.account.name} (#${balance.account.id})` : 
                                                            'N/A'
                                                        }
                                                    </td>
                                                    <td>
                                                        {balance.pos ? 
                                                            `${balance.pos.name} (#${balance.pos.id})` : 
                                                            'N/A'
                                                        }
                                                    </td>
                                                    <td>{formatCurrency(balance.amount)}</td>
                                                    <td>{balance.notes || 'N/A'}</td>
                                                    <td>
                                                        {new Date(balance.created_at).toLocaleDateString()}
                                                    </td>
                                                    <td>
                                                        <div className="d-flex gap-2">
                                                            <Button
                                                                variant="outline-primary"
                                                                size="sm"
                                                                onClick={() => navigate(`/balances/${balance.id}`)}
                                                            >
                                                                View
                                                            </Button>
                                                            {currentUser && (
                                                                <>
                                                                    <Button
                                                                        variant="outline-secondary"
                                                                        size="sm"
                                                                        onClick={() => navigate(`/balances/${balance.id}/edit`)}
                                                                    >
                                                                        Edit
                                                                    </Button>
                                                                    <Button
                                                                        variant="outline-danger"
                                                                        size="sm"
                                                                        onClick={() => handleDeleteClick(balance)}
                                                                    >
                                                                        Delete
                                                                    </Button>
                                                                </>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                        {pagination.count > 0 && (
                            <Card.Footer>
                                <div className="d-flex justify-content-between align-items-center">
                                    <span>
                                        Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                        {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                        {pagination.count} balances
                                    </span>
                                    <Pagination className="mb-0">
                                        {generatePaginationItems()}
                                    </Pagination>
                                </div>
                            </Card.Footer>
                        )}
                    </Card>
                </Col>
            </Row>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                itemName={`Balance #${balanceToDelete?.id}`}
                itemType="balance"
            />
        </Container>
    );
};

export default BalancesList;
