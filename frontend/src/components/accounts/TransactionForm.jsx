import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    Al<PERSON>,
    Spin<PERSON>,
    Row,
    Col
} from 'react-bootstrap';
import { useSearchParams } from 'react-router-dom';

const TransactionForm = ({
    initialData = {},
    onSubmit,
    loading = false,
    error = null,
    isEdit = false
}) => {
    const [searchParams] = useSearchParams();
    const [formData, setFormData] = useState({
        balance_id: searchParams.get('balance_id') || '',
        type: 'credit',
        amount: '',
        description: '',
        related_content_type: '',
        related_object_id: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Initialize form data
    useEffect(() => {
        if (initialData) {
            setFormData({
                balance_id: initialData.balance_id || initialData.balance?.id || searchParams.get('balance_id') || '',
                type: initialData.type || 'credit',
                amount: initialData.amount || '',
                description: initialData.description || '',
                related_content_type: initialData.related_content_type || '',
                related_object_id: initialData.related_object_id || ''
            });
        }
    }, [initialData, searchParams]);

    // Handle input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        // Balance ID validation
        if (!formData.balance_id) {
            errors.balance_id = 'Balance is required';
        } else if (!Number.isInteger(Number(formData.balance_id)) || Number(formData.balance_id) <= 0) {
            errors.balance_id = 'Balance ID must be a positive integer';
        }

        // Type validation
        if (!formData.type) {
            errors.type = 'Transaction type is required';
        } else if (!['credit', 'debit'].includes(formData.type)) {
            errors.type = 'Transaction type must be either credit or debit';
        }

        // Amount validation
        if (!formData.amount) {
            errors.amount = 'Amount is required';
        } else if (isNaN(Number(formData.amount))) {
            errors.amount = 'Amount must be a valid number';
        } else if (Number(formData.amount) <= 0) {
            errors.amount = 'Amount must be greater than zero';
        }

        // Description validation
        if (!formData.description.trim()) {
            errors.description = 'Description is required';
        } else if (formData.description.trim().length < 3) {
            errors.description = 'Description must be at least 3 characters long';
        } else if (formData.description.length > 500) {
            errors.description = 'Description must be less than 500 characters';
        }

        // Related object ID validation (if content_type is provided)
        if (formData.related_content_type && !formData.related_object_id) {
            errors.related_object_id = 'Related object ID is required when content type is specified';
        }

        // Related object ID must be a positive integer if provided
        if (formData.related_object_id && (!Number.isInteger(Number(formData.related_object_id)) || Number(formData.related_object_id) <= 0)) {
            errors.related_object_id = 'Related object ID must be a positive integer';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            balance_id: parseInt(formData.balance_id),
            type: formData.type,
            amount: parseFloat(formData.amount),
            description: formData.description.trim()
        };

        // Add related object data if provided
        if (formData.related_content_type && formData.related_object_id) {
            submitData.related_content_type = formData.related_content_type;
            submitData.related_object_id = parseInt(formData.related_object_id);
        }

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h4 className="mb-0">
                    {isEdit ? 'Edit Transaction' : 'Create New Transaction'}
                </h4>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Balance ID *</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="balance_id"
                                    value={formData.balance_id}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.balance_id}
                                    placeholder="Enter balance ID"
                                    min="1"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.balance_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    The balance this transaction belongs to
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Transaction Type *</Form.Label>
                                <Form.Select
                                    name="type"
                                    value={formData.type}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.type}
                                >
                                    <option value="">Select transaction type</option>
                                    <option value="credit">Credit (Money In)</option>
                                    <option value="debit">Debit (Money Out)</option>
                                </Form.Select>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.type}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Amount *</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    name="amount"
                                    value={formData.amount}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.amount}
                                    placeholder="Enter amount"
                                    min="0.01"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.amount}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Transaction amount in EGP
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Description *</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    name="description"
                                    value={formData.description}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.description}
                                    placeholder="Enter transaction description"
                                    maxLength={500}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.description}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Description of the transaction ({formData.description.length}/500 characters)
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Related Content Type</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="related_content_type"
                                    value={formData.related_content_type}
                                    onChange={handleChange}
                                    placeholder="e.g., sale, purchase, transfer"
                                />
                                <Form.Text className="text-muted">
                                    Optional: Type of related entity
                                </Form.Text>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group className="mb-3">
                                <Form.Label>Related Object ID</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="related_object_id"
                                    value={formData.related_object_id}
                                    onChange={handleChange}
                                    isInvalid={!!validationErrors.related_object_id}
                                    placeholder="Enter related object ID"
                                    min="1"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.related_object_id}
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                    Required if content type is specified
                                </Form.Text>
                            </Form.Group>
                        </Col>
                    </Row>

                    <div className="d-flex justify-content-end gap-2">
                        <Button
                            variant="secondary"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Transaction' : 'Create Transaction'
                            )}
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default TransactionForm;
