import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    Container,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useBalance } from '../../hooks/useBalances';
import { formatCurrency } from '../../utils';

const BalanceView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const location = useLocation();
    const { currentUser } = useAuth();
    const { balance, loading, error } = useBalance(id);
    const [successMessage, setSuccessMessage] = useState('');

    // Show success message from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    // Show loading spinner
    if (loading) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading balance...</span>
                            </Spinner>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if balance fetch failed
    if (error) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {error}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show message if balance not found
    if (!balance) {
        return (
            <Container fluid>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            Balance not found.
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            {/* Success Message */}
            {successMessage && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Balance #{balance.id}</h2>
                            <p className="text-muted mb-0">
                                Amount: <strong>{formatCurrency(balance.amount)}</strong>
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/balances')}
                            >
                                Back to Balances
                            </Button>
                            {currentUser && (
                                <Button
                                    variant="primary"
                                    onClick={() => navigate(`/balances/${balance.id}/edit`)}
                                >
                                    Edit Balance
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Balance Details */}
            <Row className="mb-4">
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Balance Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Table borderless>
                                <tbody>
                                    <tr>
                                        <td><strong>ID:</strong></td>
                                        <td>{balance.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Account:</strong></td>
                                        <td>
                                            {balance.account ? (
                                                <Button
                                                    variant="link"
                                                    className="p-0"
                                                    onClick={() => navigate(`/accounts/${balance.account.id}`)}
                                                >
                                                    {balance.account.name} (#{balance.account.id})
                                                </Button>
                                            ) : (
                                                'N/A'
                                            )}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>POS Terminal:</strong></td>
                                        <td>
                                            {balance.pos ? (
                                                <Button
                                                    variant="link"
                                                    className="p-0"
                                                    onClick={() => navigate(`/pos/terminals/${balance.pos.id}`)}
                                                >
                                                    {balance.pos.name} (#{balance.pos.id})
                                                </Button>
                                            ) : (
                                                'N/A'
                                            )}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Amount:</strong></td>
                                        <td>{formatCurrency(balance.amount)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Notes:</strong></td>
                                        <td>{balance.notes || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{new Date(balance.created_at).toLocaleString()}</td>
                                    </tr>
                                    {balance.updated_at && (
                                        <tr>
                                            <td><strong>Last Updated:</strong></td>
                                            <td>{new Date(balance.updated_at).toLocaleString()}</td>
                                        </tr>
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Quick Actions</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-grid gap-2">
                                <Button
                                    variant="outline-primary"
                                    onClick={() => navigate(`/transactions?balance_id=${balance.id}`)}
                                >
                                    View All Transactions
                                </Button>
                                {currentUser && (
                                    <>
                                        <Button
                                            variant="outline-success"
                                            onClick={() => navigate(`/transactions/create?balance_id=${balance.id}`)}
                                        >
                                            Create New Transaction
                                        </Button>
                                        <Button
                                            variant="outline-info"
                                            onClick={() => navigate(`/balances/transfer?from_balance_id=${balance.id}`)}
                                        >
                                            Transfer Balance
                                        </Button>
                                    </>
                                )}
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Recent Transactions Section */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <div className="d-flex justify-content-between align-items-center">
                                <h5 className="mb-0">Recent Transactions</h5>
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    onClick={() => navigate(`/transactions?balance_id=${balance.id}`)}
                                >
                                    View All
                                </Button>
                            </div>
                        </Card.Header>
                        <Card.Body>
                            <div className="text-center p-4">
                                <p className="text-muted">
                                    Transaction history will be displayed here once the transaction components are implemented.
                                </p>
                                <Button
                                    variant="primary"
                                    onClick={() => navigate(`/transactions?balance_id=${balance.id}`)}
                                >
                                    View Transactions
                                </Button>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default BalanceView;
