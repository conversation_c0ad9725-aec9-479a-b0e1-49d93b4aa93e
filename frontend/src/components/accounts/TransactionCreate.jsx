import React, { useState } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useTransactions } from '../../hooks/useTransactions';
import TransactionForm from './TransactionForm';

/**
 * Component for creating a new transaction
 * @returns {JSX.Element} The transaction create component
 */
const TransactionCreate = () => {
    const navigate = useNavigate();
    const { createTransaction, loading } = useTransactions();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} transactionData - The transaction data to create
     */
    const handleSubmit = async (transactionData) => {
        try {
            setError('');
            const newTransaction = await createTransaction(transactionData);
            
            // Navigate to the new transaction's view page
            navigate(`/transactions/${newTransaction.id}`, {
                state: { message: 'Transaction created successfully!' }
            });
        } catch (err) {
            console.error('Error creating transaction:', err);
            setError(err.response?.data?.detail || 'Failed to create transaction');
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Transaction</h2>
                </Col>
            </Row>
            <Row>
                <Col lg={8} xl={6}>
                    <TransactionForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default TransactionCreate;
