# Accounts Module Implementation

This directory contains the complete implementation of the accounts functionality for the ERP frontend application, including accounts, balances, and transactions management.

## Overview

The accounts system allows users to manage financial accounts, track balances between accounts and POS terminals, and record all financial transactions with comprehensive CRUD operations. The implementation follows React best practices and uses React Bootstrap for styling.

## Features

### Account Management
- ✅ **List View**: Display all accounts with search and filtering by type, balance range
- ✅ **Create Account**: Add new accounts with type and optional entity association
- ✅ **Edit Account**: Update account details and associations
- ✅ **View Account**: Display account details with balances and quick actions
- ✅ **Delete Account**: Remove accounts with confirmation modal
- ✅ **Account Types**: Support for asset, liability, equity, revenue, expense accounts

### Balance Management
- ✅ **List View**: Display all balances with filtering by account/POS
- ✅ **Create Balance**: Add new balances linking accounts to POS terminals
- ✅ **Edit Balance**: Update balance amounts and notes
- ✅ **View Balance**: Display balance details with transaction history
- ✅ **Delete Balance**: Remove balances with confirmation
- ✅ **Balance Tracking**: Track amounts between accounts and POS terminals

### Transaction Management
- ✅ **List View**: Display all transactions with advanced filtering
- ✅ **Create Transaction**: Add new credit/debit transactions
- ✅ **Edit Transaction**: Update transaction details
- ✅ **View Transaction**: Display transaction details and impact
- ✅ **Delete Transaction**: Remove transactions with confirmation
- ✅ **Transaction Types**: Support for credit (money in) and debit (money out)

## Component Structure

```
accounts/
├── README.md                    # This documentation
├── AccountsList.jsx            # Main accounts list with table, search, pagination
├── AccountForm.jsx             # Reusable form component for create/edit
├── AccountCreate.jsx           # Create account page
├── AccountEdit.jsx             # Edit account page
├── AccountView.jsx             # View account details and manage balances
├── BalancesList.jsx            # Balances list with advanced filtering
├── BalanceForm.jsx             # Reusable form component for balance create/edit
├── BalanceCreate.jsx           # Create balance page
├── BalanceEdit.jsx             # Edit balance page
├── BalanceView.jsx             # View balance details with transaction history
├── TransactionsList.jsx        # Transactions list with advanced filtering
├── TransactionForm.jsx         # Reusable form component for transaction create/edit
├── TransactionCreate.jsx       # Create transaction page
├── TransactionEdit.jsx         # Edit transaction page
├── TransactionView.jsx         # View transaction details and impact
└── DeleteConfirmModal.jsx      # Delete confirmation modal
```

## API Integration

### Service Layer
- **accountsService.js**: Handles accounts CRUD operations and related data
- **balancesService.js**: Manages balances operations and transfers
- **transactionsService.js**: Handles transaction operations and summaries

### Custom Hooks
- **useAccounts.js**: Manages accounts data and operations
- **useBalances.js**: Manages balances data and operations
- **useTransactions.js**: Manages transactions data and operations

## Component Patterns

### Data Management
- **Data Fetching**: Uses custom hooks for data fetching and state management
- **State Management**: React hooks for local state, no global state management
- **Updates**: Optimistic updates with error handling and rollback

### Search & Filtering

| Component        | Search Fields | Filter Options                                              |
|------------------|---------------|-------------------------------------------------------------|
| AccountsList     | Text search   | Account Type, Balance Range                                |
| BalancesList     | Text search   | Account ID, POS ID, Amount Range, Date Range              |
| TransactionsList | Text search   | Balance ID, Account ID, Transaction Type, Amount, Date    |

### Sorting & Pagination

| Component        | Sortable Columns                                                                                                     | Pagination                                                                          |
|------------------|----------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------|
| AccountsList     | ID, Name, Type, Created                                                                                             | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |
| BalancesList     | ID, Amount, Created                                                                                                 | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |
| TransactionsList | ID, Type, Amount, Created                                                                                           | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |

### Form Handling
- **Validation**: Client-side validation with custom validation logic
- **Error Handling**: Field-specific error messages, API error extraction and display
- **Libraries**: Uses React's controlled components
- **Submission**: Async submission with loading states and success feedback

### UI Components

- **React-Bootstrap Components**:
    - Layout: Container, Row, Col, Card
    - Forms: Form, Form.Control, Form.Select
    - Feedback: Alert, Badge, Spinner
    - Navigation: Button, Pagination
    - Data Display: Table
    - Modals: Modal

- **Custom Components**:
    - DeleteConfirmModal: Reusable confirmation dialog
    - Various form components for different entity types

## API Endpoints Used

### Accounts
- `GET /api/accounts/` - List accounts
- `POST /api/accounts/` - Create account
- `GET /api/accounts/{id}/` - Get account details
- `PUT /api/accounts/{id}/` - Update account
- `DELETE /api/accounts/{id}/` - Delete account
- `GET /api/accounts/{id}/balances/` - Get account balances
- `GET /api/accounts/{id}/balance/{balance_pk}/transactions/` - Get balance transactions
- `POST /api/accounts/{id}/balance/{balance_pk}/create-transactions/` - Create transaction

### Balances
- `GET /api/balances/` - List balances
- `POST /api/balances/` - Create balance
- `GET /api/balances/{id}/` - Get balance details
- `PUT /api/balances/{id}/` - Update balance
- `DELETE /api/balances/{id}/` - Delete balance
- `POST /api/balances/transfer/` - Transfer between balances

### Transactions
- `GET /api/transactions/` - List transactions
- `POST /api/transactions/` - Create transaction
- `GET /api/transactions/{id}/` - Get transaction details
- `PUT /api/transactions/{id}/` - Update transaction
- `DELETE /api/transactions/{id}/` - Delete transaction
- `GET /api/transactions/summary/` - Get transaction summary

## Error Handling

### Error Display
- Alert components for global errors
- Form.Control.Feedback for field-specific errors
- Dismissible alerts with close buttons

### Error Management
- Try/catch blocks for async operations
- Error state in hooks
- Error extraction from API responses

## Navigation Integration

### Routes
- `/accounts` - Accounts list
- `/accounts/create` - Create account
- `/accounts/:id` - View account
- `/accounts/:id/edit` - Edit account
- `/balances` - Balances list
- `/balances/create` - Create balance
- `/balances/:id` - View balance
- `/balances/:id/edit` - Edit balance
- `/transactions` - Transactions list
- `/transactions/create` - Create transaction
- `/transactions/:id` - View transaction
- `/transactions/:id/edit` - Edit transaction

### Navigation Menu
- **Accounts** dropdown menu with links to all major sections
- Organized by functionality: Accounts, Balances, Transactions
- Quick access to create operations

## Usage Examples

### Creating an Account
1. Navigate to "Accounts" → "Add Account"
2. Fill in account name and select type
3. Optionally link to an entity (content type + object ID)
4. Submit to create the account

### Creating a Balance
1. Go to "Balances" → "Add Balance" or from account view
2. Select account and POS terminal
3. Enter initial balance amount
4. Add optional notes
5. Submit to create the balance

### Recording a Transaction
1. Navigate to "Transactions" → "Add Transaction"
2. Select balance and transaction type (credit/debit)
3. Enter amount and description
4. Optionally link to related entity
5. Submit to record the transaction

## Future Enhancements

- Balance transfer wizard with validation
- Transaction bulk operations
- Advanced reporting and analytics
- Chart visualizations for account balances
- Export functionality for financial reports
- Audit trail and transaction history
- Integration with external accounting systems
- Multi-currency support
- Automated transaction rules
- Financial period closing functionality

## Testing

The implementation includes comprehensive error handling and validation. To test:

1. **CRUD Operations**: Test create, read, update, delete for all entities
2. **Validation**: Test form validation with invalid data
3. **Error Handling**: Test API error scenarios
4. **Navigation**: Test all routes and navigation links
5. **Filtering**: Test search and filter functionality
6. **Pagination**: Test pagination with different page sizes
7. **Relationships**: Test account-balance-transaction relationships

## Dependencies

- React 18+
- React Router DOM
- React Bootstrap
- Custom hooks and services
- Existing authentication context
- Utility functions (formatCurrency, etc.)
