import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CategoriesList from '../CategoriesList';
import { useCategories } from '../../../hooks/useCategories';

// Mock the useCategories hook
jest.mock('../../../hooks/useCategories');

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockNavigate,
}));

const renderWithRouter = (component) => {
    return render(
        <BrowserRouter>
            {component}
        </BrowserRouter>
    );
};

describe('CategoriesList', () => {
    const mockCategories = [
        {
            id: 1,
            name: 'Electronics',
            description: 'Electronic devices and accessories',
            parent_id: null,
            created: '2023-01-01T00:00:00Z'
        },
        {
            id: 2,
            name: 'Computers',
            description: 'Computer hardware and software',
            parent_id: 1,
            created: '2023-01-02T00:00:00Z'
        }
    ];

    const mockPagination = {
        count: 2,
        next: null,
        previous: null,
        page: 1,
        page_size: 10
    };

    const defaultMockHook = {
        categories: mockCategories,
        loading: false,
        error: null,
        pagination: mockPagination,
        fetchCategories: jest.fn(),
        deleteCategory: jest.fn(),
        setError: jest.fn()
    };

    beforeEach(() => {
        useCategories.mockReturnValue(defaultMockHook);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders categories list correctly', () => {
        renderWithRouter(<CategoriesList />);

        expect(screen.getByText('Categories')).toBeInTheDocument();
        expect(screen.getByText('Electronics')).toBeInTheDocument();
        expect(screen.getByText('Computers')).toBeInTheDocument();
        expect(screen.getByText('Add Category')).toBeInTheDocument();
    });

    test('displays loading state', () => {
        useCategories.mockReturnValue({
            ...defaultMockHook,
            loading: true
        });

        renderWithRouter(<CategoriesList />);

        expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    test('displays error state', () => {
        const errorMessage = 'Failed to fetch categories';
        useCategories.mockReturnValue({
            ...defaultMockHook,
            error: errorMessage
        });

        renderWithRouter(<CategoriesList />);

        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    test('displays empty state when no categories', () => {
        useCategories.mockReturnValue({
            ...defaultMockHook,
            categories: []
        });

        renderWithRouter(<CategoriesList />);

        expect(screen.getByText('No categories found')).toBeInTheDocument();
    });

    test('handles search functionality', async () => {
        renderWithRouter(<CategoriesList />);

        const searchInput = screen.getByPlaceholderText('Search categories by name or description...');
        const searchButton = screen.getByText('Search');

        fireEvent.change(searchInput, { target: { value: 'Electronics' } });
        fireEvent.click(searchButton);

        await waitFor(() => {
            expect(defaultMockHook.fetchCategories).toHaveBeenCalledWith({
                page: 1,
                page_size: 10,
                search: 'Electronics',
                ordering: 'name'
            });
        });
    });

    test('handles sorting functionality', async () => {
        renderWithRouter(<CategoriesList />);

        const sortSelect = screen.getByDisplayValue('Sort by Name (A-Z)');

        fireEvent.change(sortSelect, { target: { value: '-created' } });

        await waitFor(() => {
            expect(defaultMockHook.fetchCategories).toHaveBeenCalledWith({
                page: 1,
                page_size: 10,
                search: '',
                ordering: '-created'
            });
        });
    });

    test('opens delete confirmation modal', () => {
        renderWithRouter(<CategoriesList />);

        const deleteButtons = screen.getAllByText('Delete');
        fireEvent.click(deleteButtons[0]);

        expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
        expect(screen.getByText(/Are you sure you want to delete the category/)).toBeInTheDocument();
    });

    test('displays correct category information', () => {
        renderWithRouter(<CategoriesList />);

        // Check for root category badge
        expect(screen.getByText('Root Category')).toBeInTheDocument();

        // Check for subcategory badge
        expect(screen.getByText('Has Parent')).toBeInTheDocument();

        // Check for formatted dates
        expect(screen.getByText('1/1/2023')).toBeInTheDocument();
        expect(screen.getByText('1/2/2023')).toBeInTheDocument();
    });

    test('renders action buttons correctly', () => {
        renderWithRouter(<CategoriesList />);

        // Should have View, Edit, and Delete buttons for each category
        expect(screen.getAllByText('View')).toHaveLength(2);
        expect(screen.getAllByText('Edit')).toHaveLength(2);
        expect(screen.getAllByText('Delete')).toHaveLength(2);
    });
});
