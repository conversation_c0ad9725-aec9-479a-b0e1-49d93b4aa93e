import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
    Container,
    Row,
    Col,
    Card,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Spin<PERSON>,
    <PERSON>ge,
    Table
} from 'react-bootstrap';
import { useCategory } from '../../hooks/useCategories';

const CategoryView = () => {
    const { id } = useParams();
    const { category, loading, error } = useCategory(id);

    // Show loading spinner
    if (loading) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8} className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </Spinner>
                        <p className="mt-2">Loading category details...</p>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if category not found
    if (error) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="danger">
                            <h4>Error Loading Category</h4>
                            <p>{error}</p>
                            <Button variant="outline-primary" as={Link} to="/categories">
                                Back to Categories
                            </Button>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show category not found if no data
    if (!category) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="warning">
                            <h4>Category Not Found</h4>
                            <p>The requested category could not be found.</p>
                            <Button variant="outline-primary" as={Link} to="/categories">
                                Back to Categories
                            </Button>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container>
            <Row className="justify-content-center">
                <Col md={10}>
                    {/* Header */}
                    <div className="d-flex justify-content-between align-items-center mb-4">
                        <h2>Category Details</h2>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-warning"
                                as={Link}
                                to={`/categories/${category.id}/edit`}
                            >
                                Edit Category
                            </Button>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/categories"
                            >
                                Back to List
                            </Button>
                        </div>
                    </div>

                    {/* Category Information Card */}
                    <Card className="mb-4">
                        <Card.Header>
                            <h4 className="mb-0">
                                {category.name}
                                {category.parent_id ? (
                                    <Badge bg="secondary" className="ms-2">Subcategory</Badge>
                                ) : (
                                    <Badge bg="primary" className="ms-2">Root Category</Badge>
                                )}
                            </h4>
                        </Card.Header>
                        <Card.Body>
                            <Table borderless>
                                <tbody>
                                    <tr>
                                        <td width="200"><strong>Category ID:</strong></td>
                                        <td>{category.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{category.name}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Description:</strong></td>
                                        <td>
                                            {category.description ? (
                                                <div style={{ whiteSpace: 'pre-wrap' }}>
                                                    {category.description}
                                                </div>
                                            ) : (
                                                <span className="text-muted">No description provided</span>
                                            )}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Parent Category:</strong></td>
                                        <td>
                                            {category.parent_id ? (
                                                <Badge bg="info">Has Parent (ID: {category.parent_id})</Badge>
                                            ) : (
                                                <Badge bg="primary">Root Category</Badge>
                                            )}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>
                                            {new Date(category.created).toLocaleString()}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Modified:</strong></td>
                                        <td>
                                            {new Date(category.modified).toLocaleString()}
                                        </td>
                                    </tr>
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>

                    {/* Category Hierarchy Information */}
                    <Card className="mb-4">
                        <Card.Header>
                            <h5 className="mb-0">Category Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <h6>Category Type</h6>
                                    <p>
                                        {category.parent_id ? (
                                            <>
                                                <Badge bg="secondary" className="me-2">Subcategory</Badge>
                                                This is a subcategory under another category.
                                            </>
                                        ) : (
                                            <>
                                                <Badge bg="primary" className="me-2">Root Category</Badge>
                                                This is a top-level category.
                                            </>
                                        )}
                                    </p>
                                </Col>
                                <Col md={6}>
                                    <h6>Usage</h6>
                                    <p className="text-muted">
                                        This category can be used to organize products in your inventory.
                                        {category.parent_id && ' As a subcategory, it provides more specific categorization.'}
                                    </p>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    {/* Actions Card */}
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Actions</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-flex gap-2 flex-wrap">
                                <Button
                                    variant="warning"
                                    as={Link}
                                    to={`/categories/${category.id}/edit`}
                                >
                                    Edit Category
                                </Button>
                                <Button
                                    variant="success"
                                    as={Link}
                                    to="/categories/create"
                                >
                                    Create New Category
                                </Button>
                                <Button
                                    variant="info"
                                    as={Link}
                                    to="/categories"
                                >
                                    View All Categories
                                </Button>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CategoryView;
