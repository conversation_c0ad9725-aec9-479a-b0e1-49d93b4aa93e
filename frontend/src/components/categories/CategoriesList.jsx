import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    But<PERSON>,
    Form,
    Row,
    Col,
    Pa<PERSON><PERSON>,
    Spin<PERSON>,
    Al<PERSON>,
    Card,
    InputGroup,
    Badge, Container
} from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useCategories } from '../../hooks/useCategories';
import DeleteConfirmModal from './DeleteConfirmModal';

const CategoriesList = () => {
    const navigate = useNavigate();
    const {
        categories,
        loading,
        error,
        pagination,
        fetchCategories,
        deleteCategory,
        setError
    } = useCategories();

    // Local state for filters and search
    const [searchTerm, setSearchTerm] = useState('');
    const [ordering, setOrdering] = useState('name');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState(null);

    // Load categories on component mount
    useEffect(() => {
        fetchCategories({
            page: 1,
            page_size: 10,
            ordering: 'name'
        });
    }, [fetchCategories]);

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        fetchCategories({
            page: 1,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = ordering === field ? `-${field}` : field;
        setOrdering(newOrdering);
        fetchCategories({
            page: pagination.page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering: newOrdering
        });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchCategories({
            page,
            page_size: pagination.page_size,
            search: searchTerm,
            ordering
        });
    };

    // Handle delete confirmation
    const handleDeleteClick = (category) => {
        setCategoryToDelete(category);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            await deleteCategory(categoryToDelete.id);
            setShowDeleteModal(false);
            setCategoryToDelete(null);
        } catch (error) {
            // Error is handled by the hook
        }
    };

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pagination.page_size);
        const currentPage = pagination.page;

        // Previous button
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={!pagination.previous}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = 1; page <= totalPages; page++) {
            if (
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 2 && page <= currentPage + 2)
            ) {
                items.push(
                    <Pagination.Item
                        key={page}
                        active={page === currentPage}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </Pagination.Item>
                );
            } else if (
                page === currentPage - 3 ||
                page === currentPage + 3
            ) {
                items.push(<Pagination.Ellipsis key={`ellipsis-${page}`} />);
            }
        }

        // Next button
        items.push(
            <Pagination.Next
                key="next"
                disabled={!pagination.next}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return items;
    };

    const getSortIcon = (field) => {
        if (ordering === field) return ' ↑';
        if (ordering === `-${field}`) return ' ↓';
        return '';
    };

    return (
        <Container fluid>

            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Categories</h2>
                        <Button
                            variant="primary"
                            onClick={() => navigate('/categories/create')}
                        >
                            Add New Product
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Search and Filters */}
            <Row className="mb-3">
                <Col md={8}>
                    <Form onSubmit={handleSearch}>
                        <InputGroup>
                            <Form.Control
                                type="text"
                                placeholder="Search categories by name or description..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            <Button variant="outline-secondary" type="submit">
                                Search
                            </Button>
                        </InputGroup>
                    </Form>
                </Col>
                <Col md={4}>
                    <Form.Select value={ordering} onChange={(e) => handleSort(e.target.value)}>
                        <option value="name">Sort by Name (A-Z)</option>
                        <option value="-name">Sort by Name (Z-A)</option>
                        <option value="created">Sort by Date (Oldest)</option>
                        <option value="-created">Sort by Date (Newest)</option>
                    </Form.Select>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Loading Spinner */}
            {loading && (
                <div className="text-center py-4">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            )}

            <Card>
                <Card.Body>
                    {/* Categories Table */}
                    {!loading && (
                        <>
                            <Table responsive striped hover>
                                <thead className="listing-table-header">
                                    <tr>
                                        <th
                                            onClick={() => handleSort('name')}
                                        >
                                            Name{getSortIcon('name')}
                                        </th>
                                        <th>Description</th>
                                        <th>Parent Category</th>
                                        <th
                                            onClick={() => handleSort('created')}
                                        >
                                            Created{getSortIcon('created')}
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {categories.length === 0 ? (
                                        <tr>
                                            <td colSpan="5" className="text-center py-4">
                                                No categories found
                                            </td>
                                        </tr>
                                    ) : (
                                        categories.map((category) => (
                                            <tr key={category.id}>
                                                <td>
                                                    <strong>{category.name}</strong>
                                                </td>
                                                <td>
                                                    {category.description ? (
                                                        category.description.length > 100
                                                            ? `${category.description.substring(0, 100)}...`
                                                            : category.description
                                                    ) : (
                                                        <span className="text-muted">No description</span>
                                                    )}
                                                </td>
                                                <td>
                                                    {category.parent_id ? (
                                                        <Badge bg="secondary">Has Parent</Badge>
                                                    ) : (
                                                        <Badge bg="primary">Root Category</Badge>
                                                    )}
                                                </td>
                                                <td>
                                                    {new Date(category.created).toLocaleDateString()}
                                                </td>
                                                <td>
                                                    <div className="d-flex gap-2">
                                                        <Button
                                                            variant="outline-info"
                                                            size="sm"
                                                            as={Link}
                                                            to={`/categories/${category.id}`}
                                                        >
                                                            View
                                                        </Button>
                                                        <Button
                                                            variant="outline-warning"
                                                            size="sm"
                                                            as={Link}
                                                            to={`/categories/${category.id}/edit`}
                                                        >
                                                            Edit
                                                        </Button>
                                                        <Button
                                                            variant="outline-danger"
                                                            size="sm"
                                                            onClick={() => handleDeleteClick(category)}
                                                        >
                                                            Delete
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {pagination.count > pagination.page_size && (
                                <Row className="mt-3">
                                    <Col className="d-flex justify-content-between align-items-center">
                                        <div>
                                            Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                            {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                            {pagination.count} entries
                                        </div>
                                        <Pagination className="mb-0">
                                            {generatePaginationItems()}
                                        </Pagination>
                                    </Col>
                                </Row>
                            )}
                        </>
                    )}
                </Card.Body>
            </Card>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                category={categoryToDelete}
                loading={loading}
            />
        </Container>
    );
};

export default CategoriesList;
