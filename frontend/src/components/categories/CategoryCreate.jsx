import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Alert } from 'react-bootstrap';
import CategoryForm from './CategoryForm';
import { useCategories } from '../../hooks/useCategories';

const CategoryCreate = () => {
    const navigate = useNavigate();
    const { createCategory } = useCategories();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    const handleSubmit = async (categoryData) => {
        try {
            setLoading(true);
            setError(null);

            await createCategory(categoryData);

            setSuccess(true);

            // Redirect to categories list after a short delay
            setTimeout(() => {
                navigate('/categories');
            }, 1500);

        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to create category');
        } finally {
            setLoading(false);
        }
    };

    if (success) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="success" className="text-center">
                            <h4>Category Created Successfully!</h4>
                            <p>Redirecting to categories list...</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container>
            <Row className="justify-content-center">
                <Col md={8}>
                    <CategoryForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default CategoryCreate;
