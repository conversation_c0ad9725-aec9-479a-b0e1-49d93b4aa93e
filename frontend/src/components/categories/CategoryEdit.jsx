import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Alert, Spinner } from 'react-bootstrap';
import CategoryForm from './CategoryForm';
import { useCategory, useCategories } from '../../hooks/useCategories';

const CategoryEdit = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { category, loading: fetchLoading, error: fetchError } = useCategory(id);
    const { updateCategory } = useCategories();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    const handleSubmit = async (categoryData) => {
        try {
            setLoading(true);
            setError(null);

            await updateCategory(id, categoryData);

            setSuccess(true);

            // Redirect to categories list after a short delay
            setTimeout(() => {
                navigate('/categories');
            }, 1500);

        } catch (err) {
            setError(err.response?.data?.detail || 'Failed to update category');
        } finally {
            setLoading(false);
        }
    };

    // Show loading spinner while fetching category data
    if (fetchLoading) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8} className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </Spinner>
                        <p className="mt-2">Loading category data...</p>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if category not found or fetch failed
    if (fetchError) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="danger">
                            <h4>Error Loading Category</h4>
                            <p>{fetchError}</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show success message
    if (success) {
        return (
            <Container>
                <Row className="justify-content-center">
                    <Col md={8}>
                        <Alert variant="success" className="text-center">
                            <h4>Category Updated Successfully!</h4>
                            <p>Redirecting to categories list...</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container>
            <Row className="justify-content-center">
                <Col md={8}>
                    <CategoryForm
                        initialData={category}
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default CategoryEdit;
