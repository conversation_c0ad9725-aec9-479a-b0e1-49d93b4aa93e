import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';

const DeleteConfirmModal = ({ show, onHide, onConfirm, category, loading }) => {
    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Delete</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>
                    Are you sure you want to delete the category{' '}
                    <strong>"{category?.name}"</strong>?
                </p>
                <p className="text-muted">
                    This action cannot be undone. If this category has subcategories
                    or associated products, the deletion will fail.
                </p>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={onHide} disabled={loading}>
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onConfirm}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Deleting...
                        </>
                    ) : (
                        'Delete'
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
