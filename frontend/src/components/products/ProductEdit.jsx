import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useProduct, useProducts } from '../../hooks/useProducts';
import ProductForm from './ProductForm';

const ProductEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { product, loading: fetchLoading, error: fetchError } = useProduct(id);
    const { updateProduct } = useProducts();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    // Set error from fetch if it exists
    useEffect(() => {
        if (fetchError) {
            setError(fetchError);
        }
    }, [fetchError]);

    const handleSubmit = async (productData) => {
        try {
            setLoading(true);
            setError(null);

            await updateProduct(id, productData);

            setSuccess(true);

            // Redirect to products list after a short delay
            setTimeout(() => {
                navigate('/products');
            }, 1500);

        } catch (err) {
            // Handle validation errors from the backend
            if (err.response?.data) {
                const errorData = err.response.data;

                // If it's a validation error with field-specific messages
                if (typeof errorData === 'object' && !errorData.detail) {
                    const errorMessages = [];
                    Object.keys(errorData).forEach(field => {
                        const fieldErrors = Array.isArray(errorData[field])
                            ? errorData[field]
                            : [errorData[field]];
                        fieldErrors.forEach(error => {
                            errorMessages.push(`${field}: ${error}`);
                        });
                    });
                    setError(errorMessages.join(', '));
                } else {
                    setError(errorData.detail || errorData.message || 'Failed to update product');
                }
            } else {
                setError('Failed to update product. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    // Show loading spinner while fetching product data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Product</h2>
                    </Col>
                </Row>
                <Row>
                    <Col className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading product...</span>
                        </Spinner>
                        <div className="mt-2">Loading product data...</div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if product not found or fetch failed
    if (fetchError || !product) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit Product</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {fetchError || 'Product not found'}
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Product</h2>
                    <p className="text-muted">
                        Update the product information below. Changes will be saved immediately.
                    </p>
                </Col>
            </Row>

            {success && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success">
                            Product updated successfully! Redirecting to products list...
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row>
                <Col>
                    <ProductForm
                        initialData={product}
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default ProductEdit;
