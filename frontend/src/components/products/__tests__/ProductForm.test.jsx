import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProductForm from '../ProductForm';
import { useParentCategories } from '../../../hooks/useCategories';

// Mock the hooks
jest.mock('../../../hooks/useCategories');

const mockCategories = [
    { id: 1, name: 'Electronics' },
    { id: 2, name: 'Clothing' },
    { id: 3, name: 'Books' }
];

describe('ProductForm', () => {
    const mockOnSubmit = jest.fn();

    beforeEach(() => {
        useParentCategories.mockReturnValue({
            parentCategories: mockCategories,
            loading: false
        });
        mockOnSubmit.mockClear();
    });

    test('renders form fields correctly', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        expect(screen.getByLabelText(/product name/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/cost price/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/selling price/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/barcode/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/unit type/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/product image/i)).toBeInTheDocument();
    });

    test('displays create mode title', () => {
        render(<ProductForm onSubmit={mockOnSubmit} isEdit={false} />);

        expect(screen.getByText('Create New Product')).toBeInTheDocument();
        expect(screen.getByText('Create Product')).toBeInTheDocument();
    });

    test('displays edit mode title', () => {
        render(<ProductForm onSubmit={mockOnSubmit} isEdit={true} />);

        expect(screen.getByText('Edit Product')).toBeInTheDocument();
        expect(screen.getByText('Update Product')).toBeInTheDocument();
    });

    test('pre-fills form with initial data in edit mode', () => {
        const initialData = {
            name: 'Test Product',
            description: 'Test description',
            cost: 10.00,
            price: 15.00,
            barcode: '123456789',
            unit_type: 'kg',
            category_id: 1
        };

        render(
            <ProductForm
                onSubmit={mockOnSubmit}
                initialData={initialData}
                isEdit={true}
            />
        );

        expect(screen.getByDisplayValue('Test Product')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();
        expect(screen.getByDisplayValue('10')).toBeInTheDocument();
        expect(screen.getByDisplayValue('15')).toBeInTheDocument();
        expect(screen.getByDisplayValue('123456789')).toBeInTheDocument();
        expect(screen.getByDisplayValue('kg')).toBeInTheDocument();
    });

    test('validates required fields', async () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        const submitButton = screen.getByText('Create Product');
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText('Product name is required')).toBeInTheDocument();
            expect(screen.getByText('Cost price is required')).toBeInTheDocument();
            expect(screen.getByText('Selling price is required')).toBeInTheDocument();
        });

        expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    test('validates price must be greater than or equal to cost', async () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        fireEvent.change(screen.getByLabelText(/product name/i), {
            target: { value: 'Test Product' }
        });
        fireEvent.change(screen.getByLabelText(/cost price/i), {
            target: { value: '20' }
        });
        fireEvent.change(screen.getByLabelText(/selling price/i), {
            target: { value: '15' }
        });

        const submitButton = screen.getByText('Create Product');
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText('Selling price cannot be less than cost price')).toBeInTheDocument();
        });

        expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    test('calculates profit margin correctly', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        fireEvent.change(screen.getByLabelText(/cost price/i), {
            target: { value: '10' }
        });
        fireEvent.change(screen.getByLabelText(/selling price/i), {
            target: { value: '15' }
        });

        // Profit margin should be (15-10)/10 * 100 = 50%
        expect(screen.getByDisplayValue('50.00%')).toBeInTheDocument();
    });

    test('submits form with correct data', async () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        fireEvent.change(screen.getByLabelText(/product name/i), {
            target: { value: 'Test Product' }
        });
        fireEvent.change(screen.getByLabelText(/description/i), {
            target: { value: 'Test description' }
        });
        fireEvent.change(screen.getByLabelText(/cost price/i), {
            target: { value: '10.50' }
        });
        fireEvent.change(screen.getByLabelText(/selling price/i), {
            target: { value: '15.75' }
        });
        fireEvent.change(screen.getByLabelText(/barcode/i), {
            target: { value: '123456789' }
        });
        fireEvent.change(screen.getByLabelText(/unit type/i), {
            target: { value: 'kg' }
        });
        fireEvent.change(screen.getByLabelText(/category/i), {
            target: { value: '1' }
        });

        const submitButton = screen.getByText('Create Product');
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(mockOnSubmit).toHaveBeenCalledWith({
                name: 'Test Product',
                description: 'Test description',
                cost: 10.50,
                price: 15.75,
                barcode: '123456789',
                unit_type: 'kg',
                category_id: 1
            });
        });
    });

    test('handles image upload', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        const imageInput = screen.getByLabelText(/product image/i);

        fireEvent.change(imageInput, { target: { files: [file] } });

        expect(imageInput.files[0]).toBe(file);
    });

    test('displays loading state', () => {
        render(<ProductForm onSubmit={mockOnSubmit} loading={true} />);

        expect(screen.getByText('Creating...')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /creating/i })).toBeDisabled();
    });

    test('displays error message', () => {
        const errorMessage = 'Failed to create product';
        render(<ProductForm onSubmit={mockOnSubmit} error={errorMessage} />);

        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    test('populates category dropdown', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        const categorySelect = screen.getByLabelText(/category/i);

        expect(screen.getByText('Electronics')).toBeInTheDocument();
        expect(screen.getByText('Clothing')).toBeInTheDocument();
        expect(screen.getByText('Books')).toBeInTheDocument();
    });

    test('populates unit type dropdown', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        const unitSelect = screen.getByLabelText(/unit type/i);

        expect(screen.getByText('Piece')).toBeInTheDocument();
        expect(screen.getByText('Kilogram')).toBeInTheDocument();
        expect(screen.getByText('Gram')).toBeInTheDocument();
        expect(screen.getByText('Liter')).toBeInTheDocument();
        expect(screen.getByText('Milliliter')).toBeInTheDocument();
    });

    test('validates description length', async () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        const longDescription = 'a'.repeat(1001);

        fireEvent.change(screen.getByLabelText(/description/i), {
            target: { value: longDescription }
        });

        const submitButton = screen.getByText('Create Product');
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText('Description must be less than 1000 characters')).toBeInTheDocument();
        });
    });

    test('shows character count for description', () => {
        render(<ProductForm onSubmit={mockOnSubmit} />);

        fireEvent.change(screen.getByLabelText(/description/i), {
            target: { value: 'Test description' }
        });

        expect(screen.getByText('16/1000 characters')).toBeInTheDocument();
    });

    test('handles cancel button click', () => {
        // Mock window.history.back
        const mockBack = jest.fn();
        Object.defineProperty(window, 'history', {
            value: { back: mockBack },
            writable: true
        });

        render(<ProductForm onSubmit={mockOnSubmit} />);

        const cancelButton = screen.getByText('Cancel');
        fireEvent.click(cancelButton);

        expect(mockBack).toHaveBeenCalled();
    });
});
