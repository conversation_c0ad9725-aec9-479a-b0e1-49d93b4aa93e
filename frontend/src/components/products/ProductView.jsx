import React from 'react';
import {
    Con<PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>ge,
    <PERSON><PERSON>,
    Spin<PERSON>,
    Image
} from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useProduct } from '../../hooks/useProducts';
import { useAuth } from '../../contexts/AuthContext';
import {formatCurrency} from "../../utils";

const ProductView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { currentUser } = useAuth();
    const { product, loading, error } = useProduct(id);

    // Check if user can perform admin actions
    const canPerformAdminActions = currentUser &&
        (currentUser.role === 'admin' || currentUser.role === 'manager');

    // Format unit type
    const formatUnitType = (unitType) => {
        const unitTypes = {
            'piece': 'Piece',
            'kg': 'Kilogram',
            'g': 'Gram',
            'l': 'Liter',
            'ml': 'Milliliter'
        };
        return unitTypes[unitType] || unitType;
    };

    // Calculate profit margin
    const calculateProfitMargin = (cost, price) => {
        if (cost === 0) return 0;
        return ((price - cost) / cost * 100).toFixed(2);
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Show loading spinner
    if (loading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Product Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading product...</span>
                        </Spinner>
                        <div className="mt-2">Loading product details...</div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if product not found
    if (error || !product) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Product Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            {error || 'Product not found'}
                        </Alert>
                        <Button variant="secondary" onClick={() => navigate('/products')}>
                            Back to Products
                        </Button>
                    </Col>
                </Row>
            </Container>
        );
    }

    const profitMargin = calculateProfitMargin(product.cost, product.price);

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{product.name}</h2>
                            <p className="text-muted mb-0">Product Details</p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="secondary"
                                onClick={() => navigate('/products')}
                            >
                                Back to Products
                            </Button>
                            {canPerformAdminActions && (
                                <Button
                                    variant="primary"
                                    onClick={() => navigate(`/products/${product.id}/edit`)}
                                >
                                    Edit Product
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col lg={8}>
                    <Card className="mb-4">
                        <Card.Header>
                            <h5 className="mb-0">Basic Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Product Name:</strong>
                                        <div>{product.name}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Category:</strong>
                                        <div>
                                            {product.category?.name || (
                                                <span className="text-muted">No category assigned</span>
                                            )}
                                        </div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Unit Type:</strong>
                                        <div>{formatUnitType(product.unit_type)}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Barcode:</strong>
                                        <div>
                                            {product.barcode || (
                                                <span className="text-muted">No barcode</span>
                                            )}
                                        </div>
                                    </div>
                                </Col>

                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Cost Price:</strong>
                                        <div className="h5 text-info">{formatCurrency(product.cost)}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Selling Price:</strong>
                                        <div className="h5 text-success">{formatCurrency(product.price)}</div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Profit Margin:</strong>
                                        <div>
                                            <Badge
                                                bg={profitMargin > 20 ? 'success' : profitMargin > 10 ? 'warning' : 'danger'}
                                                className="fs-6"
                                            >
                                                {profitMargin}%
                                            </Badge>
                                        </div>
                                    </div>

                                    <div className="mb-3">
                                        <strong>Profit Amount:</strong>
                                        <div className="h6 text-primary">
                                            {formatCurrency(product.price - product.cost)}
                                        </div>
                                    </div>
                                </Col>
                            </Row>

                            {product.description && (
                                <div className="mt-3">
                                    <strong>Description:</strong>
                                    <div className="mt-2 p-3 bg-light rounded">
                                        {product.description}
                                    </div>
                                </div>
                            )}
                        </Card.Body>
                    </Card>

                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Metadata</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Created:</strong>
                                        <div className="text-muted">
                                            {formatDate(product.created)}
                                        </div>
                                    </div>
                                </Col>
                                <Col md={6}>
                                    <div className="mb-3">
                                        <strong>Last Modified:</strong>
                                        <div className="text-muted">
                                            {formatDate(product.modified)}
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>

                <Col lg={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Product Image</h5>
                        </Card.Header>
                        <Card.Body>
                            {product.image ? (
                                <div className="text-center">
                                    <Image
                                        src={product.image}
                                        alt={product.name}
                                        fluid
                                        rounded
                                        className="mb-3"
                                        style={{ maxHeight: '300px' }}
                                    />
                                    <div className="text-muted small">
                                        Click to view full size
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-5">
                                    <div className="text-muted">
                                        <i className="bi bi-image" style={{ fontSize: '3rem' }}></i>
                                        <div className="mt-2">No image available</div>
                                    </div>
                                </div>
                            )}
                        </Card.Body>
                    </Card>

                    {/* Quick Stats Card */}
                    <Card className="mt-3">
                        <Card.Header>
                            <h5 className="mb-0">Quick Stats</h5>
                        </Card.Header>
                        <Card.Body>
                            <div className="d-flex justify-content-between mb-2">
                                <span>Profit per unit:</span>
                                <strong className="text-success">
                                    {formatCurrency(product.price - product.cost)}
                                </strong>
                            </div>
                            <div className="d-flex justify-content-between mb-2">
                                <span>Markup:</span>
                                <strong>{profitMargin}%</strong>
                            </div>
                            <div className="d-flex justify-content-between">
                                <span>Unit type:</span>
                                <strong>{formatUnitType(product.unit_type)}</strong>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default ProductView;
