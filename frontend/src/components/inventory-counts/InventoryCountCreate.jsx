import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useInventoryCounts } from '../../hooks/useInventoryCounts';
import InventoryCountForm from './InventoryCountForm';

/**
 * Component for creating a new inventory count
 * @returns {JSX.Element} The inventory count create component
 */
const InventoryCountCreate = () => {
    const navigate = useNavigate();
    const { createInventoryCount } = useInventoryCounts();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    /**
     * Handle form submission
     * @param {Object} formData - The form data
     */
    const handleSubmit = async (formData) => {
        try {
            setLoading(true);
            setError(null);
            setSuccess(false);

            const newInventoryCount = await createInventoryCount(formData);

            setSuccess(true);

            // Redirect to the new inventory count view after a short delay
            setTimeout(() => {
                navigate(`/inventory-counts/${newInventoryCount.id}`);
            }, 1500);

        } catch (err) {
            console.error('Error creating inventory count:', err);

            // Extract error message
            let errorMessage = 'Failed to create inventory count';

            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    errorMessage = err.response.data;
                } else if (err.response.data.detail) {
                    errorMessage = err.response.data.detail;
                } else if (err.response.data.message) {
                    errorMessage = err.response.data.message;
                } else if (err.response.data.warehouse) {
                    errorMessage = `Warehouse: ${err.response.data.warehouse[0]}`;
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field][0]}`);
                        }
                    });
                    if (fieldErrors.length > 0) {
                        errorMessage = fieldErrors.join(', ');
                    }
                }
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New Inventory Count</h2>
                    <p className="text-muted">
                        Create a new inventory count to track physical stock levels in a warehouse.
                    </p>
                </Col>
            </Row>

            <Row>
                <Col lg={8}>
                    {success && (
                        <Alert variant="success" className="mb-3">
                            <Alert.Heading>Success!</Alert.Heading>
                            <p>
                                Inventory count created successfully. You will be redirected to the
                                inventory count details page shortly.
                            </p>
                        </Alert>
                    )}

                    <InventoryCountForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
                <Col lg={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">About Inventory Counts</h5>
                        </Card.Header>
                        <Card.Body>
                            <h6>What happens when you create an inventory count?</h6>
                            <ul className="small">
                                <li>A new inventory count record is created in "Draft" status</li>
                                <li>Count items are automatically generated for all stock items in the warehouse</li>
                                <li>System quantities are captured at the time of creation</li>
                                <li>You can then record actual quantities found during physical counting</li>
                            </ul>

                            <h6 className="mt-3">Workflow</h6>
                            <ol className="small">
                                <li><strong>Draft:</strong> Initial state, can be edited or deleted</li>
                                <li><strong>In Progress:</strong> Counting is active, quantities can be recorded</li>
                                <li><strong>Completed:</strong> Count is finalized, stock levels are updated</li>
                                <li><strong>Cancelled:</strong> Count is cancelled, no changes to stock</li>
                            </ol>

                            <h6 className="mt-3">Requirements</h6>
                            <ul className="small">
                                <li>Only one active count per warehouse at a time</li>
                                <li>Admin privileges required to create counts</li>
                                <li>Warehouse must have existing stock items</li>
                            </ul>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default InventoryCountCreate;
