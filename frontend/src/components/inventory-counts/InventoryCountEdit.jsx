import React, { useState } from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import { useInventoryCount, useInventoryCounts } from '../../hooks/useInventoryCounts';
import InventoryCountForm from './InventoryCountForm';

/**
 * Component for editing an existing inventory count
 * @returns {JSX.Element} The inventory count edit component
 */
const InventoryCountEdit = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { inventoryCount, loading: fetchLoading, error: fetchError } = useInventoryCount(id);
    const { updateInventoryCount } = useInventoryCounts();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    /**
     * Handle form submission
     * @param {Object} formData - The form data
     */
    const handleSubmit = async (formData) => {
        try {
            setLoading(true);
            setError(null);
            setSuccess(false);

            await updateInventoryCount(id, formData);

            setSuccess(true);

            // Redirect to the inventory count view after a short delay
            setTimeout(() => {
                navigate(`/inventory-counts/${id}`);
            }, 1500);

        } catch (err) {
            console.error('Error updating inventory count:', err);

            // Extract error message
            let errorMessage = 'Failed to update inventory count';

            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    errorMessage = err.response.data;
                } else if (err.response.data.detail) {
                    errorMessage = err.response.data.detail;
                } else if (err.response.data.message) {
                    errorMessage = err.response.data.message;
                } else if (err.response.data.warehouse) {
                    errorMessage = `Warehouse: ${err.response.data.warehouse[0]}`;
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field][0]}`);
                        }
                    });
                    if (fieldErrors.length > 0) {
                        errorMessage = fieldErrors.join(', ');
                    }
                }
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    // Show loading state while fetching inventory count
    if (fetchLoading) {
        return (
            <Container fluid>
                <div className="text-center py-5">
                    <div className="spinner-border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                </div>
            </Container>
        );
    }

    // Show error if failed to fetch inventory count
    if (fetchError) {
        return (
            <Container fluid>
                <Alert variant="danger">
                    <Alert.Heading>Error</Alert.Heading>
                    <p>{fetchError}</p>
                    <button
                        className="btn btn-outline-danger"
                        onClick={() => navigate('/inventory-counts')}
                    >
                        Back to Inventory Counts
                    </button>
                </Alert>
            </Container>
        );
    }

    // Show not found if inventory count doesn't exist
    if (!inventoryCount) {
        return (
            <Container fluid>
                <Alert variant="warning">
                    <Alert.Heading>Not Found</Alert.Heading>
                    <p>The requested inventory count was not found.</p>
                    <button
                        className="btn btn-outline-warning"
                        onClick={() => navigate('/inventory-counts')}
                    >
                        Back to Inventory Counts
                    </button>
                </Alert>
            </Container>
        );
    }

    // Check if editing is allowed
    const canEdit = ['draft', 'in_progress'].includes(inventoryCount.status);

    if (!canEdit) {
        return (
            <Container fluid>
                <Alert variant="warning">
                    <Alert.Heading>Cannot Edit</Alert.Heading>
                    <p>
                        This inventory count cannot be edited because it is in "{inventoryCount.status_display}" status.
                        Only inventory counts in "Draft" or "In Progress" status can be edited.
                    </p>
                    <button
                        className="btn btn-outline-warning"
                        onClick={() => navigate(`/inventory-counts/${id}`)}
                    >
                        View Inventory Count
                    </button>
                </Alert>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit Inventory Count #{inventoryCount.id}</h2>
                    <p className="text-muted">
                        Update the details of this inventory count. Note that the warehouse cannot be changed once created.
                    </p>
                </Col>
            </Row>

            <Row>
                <Col lg={8}>
                    {success && (
                        <Alert variant="success" className="mb-3">
                            <Alert.Heading>Success!</Alert.Heading>
                            <p>
                                Inventory count updated successfully. You will be redirected to the
                                inventory count details page shortly.
                            </p>
                        </Alert>
                    )}

                    <InventoryCountForm
                        initialData={inventoryCount}
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
                <Col lg={4}>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Current Status</h5>
                        </Card.Header>
                        <Card.Body>
                            <dl className="row">
                                <dt className="col-sm-5">Status:</dt>
                                <dd className="col-sm-7">
                                    <span className={`badge bg-${
                                        inventoryCount.status === 'draft' ? 'secondary' :
                                        inventoryCount.status === 'in_progress' ? 'primary' :
                                        inventoryCount.status === 'completed' ? 'success' : 'danger'
                                    }`}>
                                        {inventoryCount.status_display}
                                    </span>
                                </dd>

                                <dt className="col-sm-5">Warehouse:</dt>
                                <dd className="col-sm-7">{inventoryCount.warehouse_name}</dd>

                                <dt className="col-sm-5">Items:</dt>
                                <dd className="col-sm-7">{inventoryCount.item_count || 0}</dd>

                                <dt className="col-sm-5">Created:</dt>
                                <dd className="col-sm-7">
                                    {new Date(inventoryCount.created).toLocaleDateString()}
                                </dd>

                                {inventoryCount.started_at && (
                                    <>
                                        <dt className="col-sm-5">Started:</dt>
                                        <dd className="col-sm-7">
                                            {new Date(inventoryCount.started_at).toLocaleDateString()}
                                        </dd>
                                    </>
                                )}
                            </dl>
                        </Card.Body>
                    </Card>

                    <Card className="mt-3">
                        <Card.Header>
                            <h6 className="mb-0">Edit Limitations</h6>
                        </Card.Header>
                        <Card.Body>
                            <ul className="small mb-0">
                                <li>Warehouse cannot be changed once created</li>
                                <li>Only notes can be updated</li>
                                <li>To modify items, use the main view page</li>
                                <li>Status changes through workflow actions</li>
                            </ul>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default InventoryCountEdit;
