import React, { useState } from 'react';
import { <PERSON>, Button, Alert, Form, Row, Col } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';

const AuthDebug = () => {
    const { currentUser, loading, error, login, logout, refreshUser } = useAuth();
    const [loginForm, setLoginForm] = useState({ username: '', password: '' });
    const [loginLoading, setLoginLoading] = useState(false);
    const [loginError, setLoginError] = useState(null);

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoginLoading(true);
        setLoginError(null);

        try {
            await login(loginForm.username, loginForm.password);
        } catch (err) {
            setLoginError(err.response?.data?.detail || 'Login failed');
        } finally {
            setLoginLoading(false);
        }
    };

    const handleRefresh = async () => {
        try {
            await refreshUser();
        } catch (err) {
            console.error('Refresh failed:', err);
        }
    };

    const getTokenInfo = () => {
        const accessToken = localStorage.getItem('access');
        const refreshToken = localStorage.getItem('refresh');

        if (!accessToken) return null;

        try {
            const payload = JSON.parse(atob(accessToken.split('.')[1]));
            return {
                accessToken: accessToken.substring(0, 20) + '...',
                refreshToken: refreshToken ? refreshToken.substring(0, 20) + '...' : 'None',
                exp: new Date(payload.exp * 1000).toLocaleString(),
                iat: new Date(payload.iat * 1000).toLocaleString(),
                userId: payload.user_id,
                isExpired: payload.exp < (Date.now() / 1000)
            };
        } catch (err) {
            return { error: 'Invalid token format' };
        }
    };

    const tokenInfo = getTokenInfo();

    return (
        <div className="container mt-4">
            <h2>Authentication Debug Panel</h2>

            <Row>
                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Header>
                            <h5>Current User State</h5>
                        </Card.Header>
                        <Card.Body>
                            {loading && <Alert variant="info">Loading...</Alert>}
                            {error && <Alert variant="danger">{error}</Alert>}

                            {currentUser ? (
                                <div>
                                    <p><strong>Logged in as:</strong> {currentUser.email || currentUser.username}</p>
                                    <p><strong>Role:</strong> {currentUser.role?.name || 'Unknown'}</p>
                                    <p><strong>User ID:</strong> {currentUser.id}</p>
                                    <p><strong>First Name:</strong> {currentUser.first_name || 'N/A'}</p>
                                    <p><strong>Last Name:</strong> {currentUser.last_name || 'N/A'}</p>
                                    <div className="d-flex gap-2">
                                        <Button variant="warning" onClick={handleRefresh}>
                                            Refresh User Data
                                        </Button>
                                        <Button variant="danger" onClick={logout}>
                                            Logout
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <p>Not logged in</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Header>
                            <h5>Token Information</h5>
                        </Card.Header>
                        <Card.Body>
                            {tokenInfo ? (
                                tokenInfo.error ? (
                                    <Alert variant="danger">{tokenInfo.error}</Alert>
                                ) : (
                                    <div>
                                        <p><strong>Access Token:</strong> {tokenInfo.accessToken}</p>
                                        <p><strong>Refresh Token:</strong> {tokenInfo.refreshToken}</p>
                                        <p><strong>Issued At:</strong> {tokenInfo.iat}</p>
                                        <p><strong>Expires At:</strong> {tokenInfo.exp}</p>
                                        <p><strong>User ID:</strong> {tokenInfo.userId}</p>
                                        <p><strong>Status:</strong>
                                            <span className={tokenInfo.isExpired ? 'text-danger' : 'text-success'}>
                                                {tokenInfo.isExpired ? ' Expired' : ' Valid'}
                                            </span>
                                        </p>
                                    </div>
                                )
                            ) : (
                                <p>No tokens found</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {!currentUser && (
                <Card>
                    <Card.Header>
                        <h5>Login Form</h5>
                    </Card.Header>
                    <Card.Body>
                        {loginError && <Alert variant="danger">{loginError}</Alert>}

                        <Form onSubmit={handleLogin}>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Username/Email</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={loginForm.username}
                                            onChange={(e) => setLoginForm({
                                                ...loginForm,
                                                username: e.target.value
                                            })}
                                            placeholder="Enter username or email"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Password</Form.Label>
                                        <Form.Control
                                            type="password"
                                            value={loginForm.password}
                                            onChange={(e) => setLoginForm({
                                                ...loginForm,
                                                password: e.target.value
                                            })}
                                            placeholder="Enter password"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Button
                                type="submit"
                                variant="primary"
                                disabled={loginLoading}
                            >
                                {loginLoading ? 'Logging in...' : 'Login'}
                            </Button>
                        </Form>
                    </Card.Body>
                </Card>
            )}

            <Card className="mt-3">
                <Card.Header>
                    <h5>Debug Actions</h5>
                </Card.Header>
                <Card.Body>
                    <div className="d-flex gap-2">
                        <Button
                            variant="outline-secondary"
                            onClick={() => {
                                localStorage.clear();
                                window.location.reload();
                            }}
                        >
                            Clear All Storage
                        </Button>
                        <Button
                            variant="outline-info"
                            onClick={() => {
                                console.log('Current User:', currentUser);
                                console.log('Access Token:', localStorage.getItem('access'));
                                console.log('Refresh Token:', localStorage.getItem('refresh'));
                            }}
                        >
                            Log to Console
                        </Button>
                    </div>
                </Card.Body>
            </Card>
        </div>
    );
};

export default AuthDebug;
