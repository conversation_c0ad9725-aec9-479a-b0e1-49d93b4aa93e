import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate, useParams, useLocation} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {usePOSTerminal} from '../../hooks/usePOS';
import {usePOSSessions} from '../../hooks/usePOSSessions';
import {useWarehouses} from '../../hooks/useWarehouses';
import StartSessionModal from './StartSessionModal';

/**
 * Component for viewing POS terminal details and managing sessions
 * @returns {JSX.Element} The POS terminal view component
 */
const POSTerminalView = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const {id} = useParams();
    const {permittedAdmin} = useAuth();
    const {posTerminal, loading, error, refetch} = usePOSTerminal(id);
    const {sessions, fetchSessions} = usePOSSessions();
    const {warehouses, fetchWarehouses} = useWarehouses();

    const [showStartSessionModal, setShowStartSessionModal] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');

    // Load data on component mount
    useEffect(() => {
        fetchWarehouses();
        fetchSessions({pos: id, page_size: 10});
    }, [id]);

    // Show success message from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    const getWarehouseName = (warehouseId) => {
        const warehouse = warehouses.find(w => w.id === warehouseId);
        return warehouse ? warehouse.name : 'Unknown';
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            'open': {variant: 'success', text: 'Open'},
            'closed': {variant: 'secondary', text: 'Closed'},
            'suspended': {variant: 'warning', text: 'Suspended'}
        };

        const config = statusConfig[status] || {variant: 'secondary', text: status};
        return <Badge bg={config.variant}>{config.text}</Badge>;
    };

    const handleStartSession = () => {
        setShowStartSessionModal(true);
    };

    const handleSessionStarted = () => {
        setShowStartSessionModal(false);
        setSuccessMessage('Session started successfully!');
        refetch(); // Refresh terminal data
        fetchSessions({pos: id, page_size: 10}); // Refresh sessions
    };

    // Show loading state
    if (loading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Terminal Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                            <p className="mt-2">Loading terminal details...</p>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if failed to fetch terminal data
    if (error) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Terminal Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            <Alert.Heading>Error Loading Terminal</Alert.Heading>
                            <p>{error}</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <Button
                                    variant="outline-danger"
                                    onClick={() => navigate('/pos/terminals')}
                                >
                                    Back to Terminals List
                                </Button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show not found if terminal doesn't exist
    if (!posTerminal) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Terminal Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Terminal Not Found</Alert.Heading>
                            <p>The requested POS terminal could not be found.</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <Button
                                    variant="outline-warning"
                                    onClick={() => navigate('/pos/terminals')}
                                >
                                    Back to Terminals List
                                </Button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Find active session
    const activeSession = sessions.find(session =>
        session.status === 'open' || session.status === 'suspended'
    );

    return (
        <Container fluid>
            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{posTerminal.name}</h2>
                            <p className="text-muted mb-0">POS Terminal Details</p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/pos/terminals')}
                            >
                                Back to List
                            </Button>
                            {permittedAdmin() && (
                                <Button
                                    variant="outline-primary"
                                    onClick={() => navigate(`/pos/terminals/${id}/edit`)}
                                >
                                    Edit Terminal
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Success Message */}
            {successMessage && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row>
                {/* Terminal Information */}
                <Col lg={6} className="mb-4">
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Terminal Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row className="mb-3">
                                <Col sm={4}><strong>ID:</strong></Col>
                                <Col sm={8}>{posTerminal.id}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Name:</strong></Col>
                                <Col sm={8}>{posTerminal.name}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Warehouse:</strong></Col>
                                <Col sm={8}>{getWarehouseName(posTerminal.warehouse)}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Description:</strong></Col>
                                <Col sm={8}>{posTerminal.description || 'No description'}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Created:</strong></Col>
                                <Col sm={8}>{new Date(posTerminal.created).toLocaleString()}</Col>
                            </Row>
                            <Row>
                                <Col sm={4}><strong>Last Modified:</strong></Col>
                                <Col sm={8}>{new Date(posTerminal.modified).toLocaleString()}</Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>

                {/* Session Management */}
                <Col lg={6} className="mb-4">
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Session Management</h5>
                            {!activeSession && (
                                <Button
                                    variant="success"
                                    size="sm"
                                    onClick={handleStartSession}
                                >
                                    Start Session
                                </Button>
                            )}
                        </Card.Header>
                        <Card.Body>
                            {activeSession ? (
                                <div>
                                    <div className="d-flex justify-content-between align-items-center mb-3">
                                        <h6>Active Session</h6>
                                        {getStatusBadge(activeSession.status)}
                                    </div>
                                    <Row className="mb-2">
                                        <Col sm={4}><strong>Session ID:</strong></Col>
                                        <Col sm={8}>{activeSession.id}</Col>
                                    </Row>
                                    <Row className="mb-2">
                                        <Col sm={4}><strong>User:</strong></Col>
                                        <Col sm={8}>{activeSession.user_name}</Col>
                                    </Row>
                                    <Row className="mb-2">
                                        <Col sm={4}><strong>Opened:</strong></Col>
                                        <Col sm={8}>{new Date(activeSession.opened_at).toLocaleString()}</Col>
                                    </Row>
                                    <Row className="mb-3">
                                        <Col sm={4}><strong>Opening Balance:</strong></Col>
                                        <Col sm={8}>${activeSession.opening_balance}</Col>
                                    </Row>
                                    <div className="d-flex gap-2">
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={() => navigate(`/pos/sessions/${activeSession.id}`)}
                                        >
                                            View Session
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center text-muted">
                                    <p>No active session</p>
                                    <p className="small">Start a new session to begin operations</p>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Recent Sessions */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Recent Sessions</h5>
                            <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => navigate(`/pos/sessions?pos=${id}`)}
                            >
                                View All Sessions
                            </Button>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {sessions.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="mb-0">No sessions found for this terminal.</p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                    <tr>
                                        <th>Session ID</th>
                                        <th>User</th>
                                        <th>Status</th>
                                        <th>Opened</th>
                                        <th>Closed</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {sessions.slice(0, 5).map((session) => (
                                        <tr key={session.id}>
                                            <td>{session.id}</td>
                                            <td>{session.user_name}</td>
                                            <td>{getStatusBadge(session.status)}</td>
                                            <td>{new Date(session.opened_at).toLocaleDateString()}</td>
                                            <td>
                                                {session.closed_at
                                                    ? new Date(session.closed_at).toLocaleDateString()
                                                    : '-'
                                                }
                                            </td>
                                            <td>
                                                <Button
                                                    variant="outline-primary"
                                                    size="sm"
                                                    onClick={() => navigate(`/pos/sessions/${session.id}`)}
                                                >
                                                    View
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Start Session Modal */}
            <StartSessionModal
                show={showStartSessionModal}
                onHide={() => setShowStartSessionModal(false)}
                onSessionStarted={handleSessionStarted}
                posTerminal={posTerminal}
            />
        </Container>
    );
};

export default POSTerminalView;
