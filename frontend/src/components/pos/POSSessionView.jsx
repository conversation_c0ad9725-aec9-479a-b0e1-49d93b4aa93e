import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    <PERSON>,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate, useParams, useLocation} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {usePOSSession, usePOSSessions} from '../../hooks/usePOSSessions';
import {usePOSSessionTransactions} from '../../hooks/usePOSSessionTransactions';
import SessionStatusBadge from './SessionStatusBadge';
import TransactionTypeBadge from './TransactionTypeBadge';
import CloseSessionModal from './CloseSessionModal';

/**
 * Component for viewing POS session details and transactions
 * @returns {JSX.Element} The POS session view component
 */
const POSSessionView = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const {id} = useParams();
    const {currentUser, permittedAdmin} = useAuth();
    const {session, loading, error, refetch} = usePOSSession(id);
    const {closeSession, suspendSession, resumeSession} = usePOSSessions();
    const {transactions, fetchTransactions} = usePOSSessionTransactions(id);

    const [showCloseModal, setShowCloseModal] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [actionLoading, setActionLoading] = useState(false);

    // Load transactions when session is loaded
    useEffect(() => {
        if (id) {
            fetchTransactions({page_size: 20});
        }
    }, [id]);

    // Show success message from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    const handleSuspendSession = async () => {
        try {
            setActionLoading(true);
            await suspendSession(id);
            setSuccessMessage('Session suspended successfully!');
            refetch();
        } catch (error) {
            console.error('Error suspending session:', error);
        } finally {
            setActionLoading(false);
        }
    };

    const handleResumeSession = async () => {
        try {
            setActionLoading(true);
            await resumeSession(id);
            setSuccessMessage('Session resumed successfully!');
            refetch();
        } catch (error) {
            console.error('Error resuming session:', error);
        } finally {
            setActionLoading(false);
        }
    };

    const handleCloseSession = async (closingBalance) => {
        try {
            await closeSession(id, {closing_balance: closingBalance});
            setShowCloseModal(false);
            setSuccessMessage('Session closed successfully!');
            refetch();
        } catch (error) {
            console.error('Error closing session:', error);
            throw error;
        }
    };

    // Show loading state
    if (loading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Session Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                            <p className="mt-2">Loading session details...</p>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if failed to fetch session data
    if (error) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Session Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            <Alert.Heading>Error Loading Session</Alert.Heading>
                            <p>{error}</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <Button
                                    variant="outline-danger"
                                    onClick={() => navigate('/pos/sessions')}
                                >
                                    Back to Sessions List
                                </Button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show not found if session doesn't exist
    if (!session) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>POS Session Details</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Session Not Found</Alert.Heading>
                            <p>The requested POS session could not be found.</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <Button
                                    variant="outline-warning"
                                    onClick={() => navigate('/pos/sessions')}
                                >
                                    Back to Sessions List
                                </Button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    const canManageSession = permittedAdmin() || session.user === currentUser?.id;

    return (
        <Container fluid>
            {/* Header */}
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Session #{session.id}</h2>
                            <p className="text-muted mb-0">
                                {session.pos_name} - {session.user_name}
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-secondary"
                                onClick={() => navigate('/pos/sessions')}
                            >
                                Back to Sessions
                            </Button>
                            {canManageSession && session.status === 'open' && (
                                <>
                                    <Button
                                        variant="warning"
                                        onClick={handleSuspendSession}
                                        disabled={actionLoading}
                                    >
                                        Suspend
                                    </Button>
                                    <Button
                                        variant="danger"
                                        onClick={() => setShowCloseModal(true)}
                                        disabled={actionLoading}
                                    >
                                        Close Session
                                    </Button>
                                </>
                            )}
                            {canManageSession && session.status === 'suspended' && (
                                <Button
                                    variant="success"
                                    onClick={handleResumeSession}
                                    disabled={actionLoading}
                                >
                                    Resume
                                </Button>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Success Message */}
            {successMessage && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row>
                {/* Session Information */}
                <Col lg={6} className="mb-4">
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Session Information</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Session ID:</strong></Col>
                                <Col sm={8}>{session.id}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>POS Terminal:</strong></Col>
                                <Col sm={8}>{session.pos_name}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>User:</strong></Col>
                                <Col sm={8}>{session.user_name}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Status:</strong></Col>
                                <Col sm={8}><SessionStatusBadge status={session.status} /></Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={4}><strong>Opened:</strong></Col>
                                <Col sm={8}>{new Date(session.opened_at).toLocaleString()}</Col>
                            </Row>
                            {session.closed_at && (
                                <Row className="mb-3">
                                    <Col sm={4}><strong>Closed:</strong></Col>
                                    <Col sm={8}>{new Date(session.closed_at).toLocaleString()}</Col>
                                </Row>
                            )}
                            {session.notes && (
                                <Row>
                                    <Col sm={4}><strong>Notes:</strong></Col>
                                    <Col sm={8}>{session.notes}</Col>
                                </Row>
                            )}
                        </Card.Body>
                    </Card>
                </Col>

                {/* Financial Summary */}
                <Col lg={6} className="mb-4">
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Financial Summary</h5>
                        </Card.Header>
                        <Card.Body>
                            <Row className="mb-3">
                                <Col sm={6}><strong>Opening Balance:</strong></Col>
                                <Col sm={6}>${session.opening_balance}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={6}><strong>Total Sales:</strong></Col>
                                <Col sm={6} className="text-success">${session.total_sales}</Col>
                            </Row>
                            <Row className="mb-3">
                                <Col sm={6}><strong>Total Expenses:</strong></Col>
                                <Col sm={6} className="text-danger">${session.total_expenses}</Col>
                            </Row>
                            {session.closing_balance !== null && (
                                <Row className="mb-3">
                                    <Col sm={6}><strong>Closing Balance:</strong></Col>
                                    <Col sm={6}>${session.closing_balance}</Col>
                                </Row>
                            )}
                            {session.calculated_balance !== null && (
                                <Row className="mb-3">
                                    <Col sm={6}><strong>Calculated Balance:</strong></Col>
                                    <Col sm={6}>${session.calculated_balance}</Col>
                                </Row>
                            )}
                            {session.difference !== null && (
                                <Row className="mb-3">
                                    <Col sm={6}><strong>Difference:</strong></Col>
                                    <Col sm={6} className={session.difference >= 0 ? 'text-success' : 'text-danger'}>
                                        ${session.difference}
                                    </Col>
                                </Row>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Transactions */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <h5 className="mb-0">Transactions</h5>
                            <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => navigate(`/pos/sessions/${id}/transactions`)}
                            >
                                View All Transactions
                            </Button>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {transactions.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="mb-0">No transactions found for this session.</p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Description</th>
                                        <th>Created</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {transactions.slice(0, 10).map((transaction) => (
                                        <tr key={transaction.id}>
                                            <td>{transaction.id}</td>
                                            <td>
                                                <TransactionTypeBadge type={transaction.transaction_type} />
                                            </td>
                                            <td>${transaction.amount}</td>
                                            <td>{transaction.description || '-'}</td>
                                            <td>{new Date(transaction.created).toLocaleString()}</td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Close Session Modal */}
            <CloseSessionModal
                show={showCloseModal}
                onHide={() => setShowCloseModal(false)}
                onConfirm={handleCloseSession}
                session={session}
            />
        </Container>
    );
};

export default POSSessionView;
