import React, {useState, useEffect} from 'react';
import {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner} from 'react-bootstrap';
import {useNavigate, useParams} from 'react-router-dom';
import {usePOSTerminal, usePOSTerminals} from '../../hooks/usePOS';
import POSTerminalForm from './POSTerminalForm';

/**
 * Component for editing an existing POS terminal
 * @returns {JSX.Element} The POS terminal edit component
 */
const POSTerminalEdit = () => {
    const navigate = useNavigate();
    const {id} = useParams();
    const {posTerminal, loading: fetchLoading, error: fetchError} = usePOSTerminal(id);
    const {updatePOSTerminal, loading: updateLoading} = usePOSTerminals();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} formData - The form data
     */
    const handleSubmit = async (formData) => {
        try {
            setError('');
            const updatedTerminal = await updatePOSTerminal(id, formData);

            // Navigate to the terminal's view page
            navigate(`/pos/terminals/${updatedTerminal.id}`, {
                state: {message: 'POS terminal updated successfully!'}
            });
        } catch (err) {
            console.error('Error updating POS terminal:', err);

            // Extract error message from response
            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    setError(err.response.data);
                } else if (err.response.data.detail) {
                    setError(err.response.data.detail);
                } else if (err.response.data.message) {
                    setError(err.response.data.message);
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field].join(', ')}`);
                        } else {
                            fieldErrors.push(`${field}: ${err.response.data[field]}`);
                        }
                    });
                    setError(fieldErrors.join('; '));
                }
            } else {
                setError('Failed to update POS terminal. Please try again.');
            }
        }
    };

    // Show loading state while fetching terminal data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit POS Terminal</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                            <p className="mt-2">Loading terminal data...</p>
                        </div>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if failed to fetch terminal data
    if (fetchError) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit POS Terminal</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            <Alert.Heading>Error Loading Terminal</Alert.Heading>
                            <p>{fetchError}</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <button
                                    className="btn btn-outline-danger"
                                    onClick={() => navigate('/pos/terminals')}
                                >
                                    Back to Terminals List
                                </button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show not found if terminal doesn't exist
    if (!posTerminal) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <h2>Edit POS Terminal</h2>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Terminal Not Found</Alert.Heading>
                            <p>The requested POS terminal could not be found.</p>
                            <hr />
                            <div className="d-flex justify-content-end">
                                <button
                                    className="btn btn-outline-warning"
                                    onClick={() => navigate('/pos/terminals')}
                                >
                                    Back to Terminals List
                                </button>
                            </div>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Edit POS Terminal</h2>
                    <p className="text-muted">
                        Update the details of the POS terminal: <strong>{posTerminal.name}</strong>
                    </p>
                </Col>
            </Row>

            <Row>
                <Col lg={8} xl={6}>
                    <POSTerminalForm
                        initialData={posTerminal}
                        onSubmit={handleSubmit}
                        loading={updateLoading}
                        error={error}
                        isEdit={true}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default POSTerminalEdit;
