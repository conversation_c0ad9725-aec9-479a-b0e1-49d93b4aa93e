import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {usePOSTerminals} from '../../hooks/usePOS';
import {useWarehouses} from '../../hooks/useWarehouses';
import DeleteConfirmModal from './DeleteConfirmModal';

/**
 * Component for displaying a list of POS terminals with search, sort, and pagination
 * @returns {JSX.Element} The POS terminals list component
 */
const POSTerminalsList = () => {
    const navigate = useNavigate();
    const {currentUser, permittedAdmin} = useAuth();
    const {
        posTerminals,
        loading,
        error,
        pagination,
        fetchPOSTerminals,
        deletePOSTerminal,
        setError
    } = usePOSTerminals();
    const {warehouses, fetchWarehouses} = useWarehouses();

    // Search and filter states
    const [searchTerm, setSearchTerm] = useState('');
    const [warehouseFilter, setWarehouseFilter] = useState('');
    const [sortField, setSortField] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);

    // Modal states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [terminalToDelete, setTerminalToDelete] = useState(null);

    // Load initial data
    useEffect(() => {
        fetchWarehouses();
        handleSearch();
    }, []);

    // Handle search and filtering
    const handleSearch = () => {
        const params = {
            page: currentPage,
            page_size: pageSize,
            ordering: sortDirection === 'desc' ? `-${sortField}` : sortField,
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        if (warehouseFilter) {
            params.warehouse = warehouseFilter;
        }

        fetchPOSTerminals(params);
    };

    // Handle search on Enter key press
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            setCurrentPage(1);
            handleSearch();
        }
    };

    // Handle sort
    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
        setCurrentPage(1);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    // Handle page size change
    const handlePageSizeChange = (e) => {
        setPageSize(parseInt(e.target.value));
        setCurrentPage(1);
    };

    // Handle delete
    const handleDeleteClick = (terminal) => {
        setTerminalToDelete(terminal);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            await deletePOSTerminal(terminalToDelete.id);
            setShowDeleteModal(false);
            setTerminalToDelete(null);
            handleSearch(); // Refresh the list
        } catch (error) {
            console.error('Error deleting POS terminal:', error);
        }
    };

    // Update search when dependencies change
    useEffect(() => {
        handleSearch();
    }, [currentPage, pageSize, sortField, sortDirection]);

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pageSize);
        const maxVisiblePages = 5;

        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page
        if (startPage > 1) {
            items.push(
                <Pagination.Item key={1} onClick={() => handlePageChange(1)}>
                    1
                </Pagination.Item>
            );
            if (startPage > 2) {
                items.push(<Pagination.Ellipsis key="start-ellipsis"/>);
            }
        }

        // Visible pages
        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                items.push(<Pagination.Ellipsis key="end-ellipsis"/>);
            }
            items.push(
                <Pagination.Item key={totalPages} onClick={() => handlePageChange(totalPages)}>
                    {totalPages}
                </Pagination.Item>
            );
        }

        return items;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Terminals</h2>
                        {permittedAdmin() && (
                            <Button
                                variant="primary"
                                onClick={() => navigate('/pos/terminals/create')}
                            >
                                Add POS Terminal
                            </Button>
                        )}
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Card */}
            <Card className="mb-4">
                <Card.Body>
                    <Row>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search by name or description..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Warehouse</Form.Label>
                                <Form.Select
                                    value={warehouseFilter}
                                    onChange={(e) => setWarehouseFilter(e.target.value)}
                                >
                                    <option value="">All Warehouses</option>
                                    {warehouses.map(warehouse => (
                                        <option key={warehouse.id} value={warehouse.id}>
                                            {warehouse.name}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2} className="d-flex align-items-end">
                            <Button variant="outline-primary" onClick={handleSearch}>
                                Search
                            </Button>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Results Card */}
            <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                    <span>
                        {loading ? 'Loading...' : `${pagination.count} POS Terminal(s) found`}
                    </span>
                    <div className="d-flex align-items-center">
                        <span className="me-2">Show:</span>
                        <Form.Select
                            size="sm"
                            value={pageSize}
                            onChange={handlePageSizeChange}
                            style={{width: 'auto'}}
                        >
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : posTerminals.length === 0 ? (
                        <div className="text-center p-4">
                            <p className="mb-0">No POS terminals found.</p>
                        </div>
                    ) : (
                        <Table responsive hover className="mb-0">
                            <thead className="table-light">
                            <tr>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('id')}
                                >
                                    ID {sortField === 'id' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('name')}
                                >
                                    Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th>Description</th>
                                <th>Warehouse</th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('created')}
                                >
                                    Created {sortField === 'created' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            {posTerminals.map((terminal) => (
                                <tr key={terminal.id}>
                                    <td>{terminal.id}</td>
                                    <td>
                                        <strong>{terminal.name}</strong>
                                    </td>
                                    <td>{terminal.description || '-'}</td>
                                    <td>
                                        {warehouses.find(w => w.id === terminal.warehouse)?.name || 'Unknown'}
                                    </td>
                                    <td>
                                        {new Date(terminal.created).toLocaleDateString()}
                                    </td>
                                    <td>
                                        <div className="d-flex gap-2">
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                onClick={() => navigate(`/pos/terminals/${terminal.id}`)}
                                            >
                                                View
                                            </Button>
                                            {permittedAdmin() && (
                                                <>
                                                    <Button
                                                        variant="outline-secondary"
                                                        size="sm"
                                                        onClick={() => navigate(`/pos/terminals/${terminal.id}/edit`)}
                                                    >
                                                        Edit
                                                    </Button>
                                                    <Button
                                                        variant="outline-danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteClick(terminal)}
                                                    >
                                                        Delete
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>

                {/* Pagination */}
                {pagination.count > pageSize && (
                    <Card.Footer>
                        <div className="d-flex justify-content-between align-items-center">
                            <span>
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, pagination.count)} of {pagination.count} entries
                            </span>
                            <Pagination className="mb-0">
                                <Pagination.First
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(1)}
                                />
                                <Pagination.Prev
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(currentPage - 1)}
                                />
                                {generatePaginationItems()}
                                <Pagination.Next
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(currentPage + 1)}
                                />
                                <Pagination.Last
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(Math.ceil(pagination.count / pageSize))}
                                />
                            </Pagination>
                        </div>
                    </Card.Footer>
                )}
            </Card>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                terminal={terminalToDelete}
                loading={loading}
            />
        </Container>
    );
};

export default POSTerminalsList;
