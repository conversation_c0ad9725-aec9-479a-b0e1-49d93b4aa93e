import React, {useState} from 'react';
import {Container, Row, Col} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {usePOSTerminals} from '../../hooks/usePOS';
import POSTerminalForm from './POSTerminalForm';

/**
 * Component for creating a new POS terminal
 * @returns {JSX.Element} The POS terminal create component
 */
const POSTerminalCreate = () => {
    const navigate = useNavigate();
    const {createPOSTerminal, loading} = usePOSTerminals();
    const [error, setError] = useState('');

    /**
     * Handle form submission
     * @param {Object} formData - The form data
     */
    const handleSubmit = async (formData) => {
        try {
            setError('');
            const newTerminal = await createPOSTerminal(formData);

            // Navigate to the new terminal's view page
            navigate(`/pos/terminals/${newTerminal.id}`, {
                state: {message: 'POS terminal created successfully!'}
            });
        } catch (err) {
            console.error('Error creating POS terminal:', err);

            // Extract error message from response
            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    setError(err.response.data);
                } else if (err.response.data.detail) {
                    setError(err.response.data.detail);
                } else if (err.response.data.message) {
                    setError(err.response.data.message);
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field].join(', ')}`);
                        } else {
                            fieldErrors.push(`${field}: ${err.response.data[field]}`);
                        }
                    });
                    setError(fieldErrors.join('; '));
                }
            } else {
                setError('Failed to create POS terminal. Please try again.');
            }
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <h2>Create New POS Terminal</h2>
                    <p className="text-muted">
                        Add a new point of sale terminal to your system.
                    </p>
                </Col>
            </Row>

            <Row>
                <Col lg={8} xl={6}>
                    <POSTerminalForm
                        onSubmit={handleSubmit}
                        loading={loading}
                        error={error}
                        isEdit={false}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export default POSTerminalCreate;
