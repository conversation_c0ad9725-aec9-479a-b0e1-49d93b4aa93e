import React from 'react';
import {Badge} from 'react-bootstrap';

/**
 * Component for displaying transaction type with appropriate styling
 * @param {Object} props - Component props
 * @param {string} props.type - The transaction type
 * @returns {JSX.Element} The transaction type badge component
 */
const TransactionTypeBadge = ({type}) => {
    const getTypeConfig = (type) => {
        const typeConfigs = {
            'sale': {
                variant: 'success',
                text: 'Sale',
                icon: '💰'
            },
            'refund': {
                variant: 'warning',
                text: 'Refund',
                icon: '↩️'
            },
            'purchase': {
                variant: 'info',
                text: 'Purchase',
                icon: '🛒'
            },
            'refund_purchase': {
                variant: 'secondary',
                text: 'Refund Purchase',
                icon: '🔄'
            },
            'cash_in': {
                variant: 'success',
                text: 'Cash In',
                icon: '💵'
            },
            'cash_out': {
                variant: 'danger',
                text: 'Cash Out',
                icon: '💸'
            },
            'expense': {
                variant: 'danger',
                text: 'Expense',
                icon: '📄'
            },
            'salary': {
                variant: 'primary',
                text: 'Salary',
                icon: '👤'
            },
            'other': {
                variant: 'secondary',
                text: 'Other',
                icon: '📝'
            }
        };

        return typeConfigs[type] || {
            variant: 'secondary',
            text: type || 'Unknown',
            icon: '❓'
        };
    };

    const config = getTypeConfig(type);

    return (
        <Badge bg={config.variant} className="d-inline-flex align-items-center">
            <span className="me-1">{config.icon}</span>
            {config.text}
        </Badge>
    );
};

export default TransactionTypeBadge;
