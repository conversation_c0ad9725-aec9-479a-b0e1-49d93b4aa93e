import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Spinner} from 'react-bootstrap';

/**
 * Confirmation modal for deleting a POS terminal
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to hide the modal
 * @param {Function} props.onConfirm - Function to confirm deletion
 * @param {Object} props.terminal - The POS terminal to be deleted
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} The delete confirmation modal component
 */
const DeleteConfirmModal = ({
                                show,
                                onHide,
                                onConfirm,
                                terminal,
                                loading = false
                            }) => {
    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Deletion</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {terminal && (
                    <div>
                        <p>Are you sure you want to delete this POS terminal?</p>
                        <div className="bg-light p-3 rounded">
                            <strong>Terminal Details:</strong>
                            <br/>
                            <strong>ID:</strong> {terminal.id}
                            <br/>
                            <strong>Name:</strong> {terminal.name}
                            <br/>
                            {terminal.description && (
                                <>
                                    <strong>Description:</strong> {terminal.description}
                                    <br/>
                                </>
                            )}
                            <strong>Created:</strong> {new Date(terminal.created).toLocaleDateString()}
                        </div>
                        <div className="mt-3">
                            <small className="text-danger">
                                <strong>Warning:</strong> This action cannot be undone. The POS terminal will be permanently deleted.
                            </small>
                        </div>
                    </div>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="secondary"
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onConfirm}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Deleting...
                        </>
                    ) : (
                        'Delete Terminal'
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
