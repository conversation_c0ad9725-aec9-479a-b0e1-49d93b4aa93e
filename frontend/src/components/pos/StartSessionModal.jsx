import React, {useState} from 'react';
import {<PERSON><PERSON>, But<PERSON>, Form, Modal, Spinner} from 'react-bootstrap';
import {usePOSTerminals} from '../../hooks/usePOS';

/**
 * <PERSON><PERSON> for starting a new POS session
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to hide the modal
 * @param {Function} props.onSessionStarted - Function called when session is started
 * @param {Object} props.posTerminal - The POS terminal object
 * @returns {JSX.Element} The start session modal component
 */
const StartSessionModal = ({
                               show,
                               onHide,
                               onSessionStarted,
                               posTerminal
                           }) => {
    const {startSession, loading} = usePOSTerminals();
    const [openingBalance, setOpeningBalance] = useState('');
    const [error, setError] = useState('');
    const [validationError, setValidationError] = useState('');

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setOpeningBalance(value);

        // Clear validation error when user starts typing
        if (validationError) {
            setValidationError('');
        }
        if (error) {
            setError('');
        }
    };

    // Validate form
    const validateForm = () => {
        if (!openingBalance.trim()) {
            setValidationError('Opening balance is required');
            return false;
        }

        const balance = parseFloat(openingBalance);
        if (isNaN(balance)) {
            setValidationError('Opening balance must be a valid number');
            return false;
        }

        if (balance < 0) {
            setValidationError('Opening balance cannot be negative');
            return false;
        }

        return true;
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            setError('');
            await startSession(posTerminal.id, {
                opening_balance: parseFloat(openingBalance)
            });

            // Reset form
            setOpeningBalance('');
            setValidationError('');

            // Call success callback
            onSessionStarted();
        } catch (err) {
            console.error('Error starting session:', err);

            // Extract error message from response
            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    setError(err.response.data);
                } else if (err.response.data.detail) {
                    setError(err.response.data.detail);
                } else if (err.response.data.message) {
                    setError(err.response.data.message);
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field].join(', ')}`);
                        } else {
                            fieldErrors.push(`${field}: ${err.response.data[field]}`);
                        }
                    });
                    setError(fieldErrors.join('; '));
                }
            } else {
                setError('Failed to start session. Please try again.');
            }
        }
    };

    // Handle modal close
    const handleClose = () => {
        if (!loading) {
            setOpeningBalance('');
            setError('');
            setValidationError('');
            onHide();
        }
    };

    return (
        <Modal show={show} onHide={handleClose} centered>
            <Modal.Header closeButton>
                <Modal.Title>Start New Session</Modal.Title>
            </Modal.Header>
            <Form onSubmit={handleSubmit}>
                <Modal.Body>
                    {posTerminal && (
                        <div className="mb-3">
                            <div className="bg-light p-3 rounded">
                                <strong>Terminal:</strong> {posTerminal.name}
                                <br/>
                                <strong>ID:</strong> {posTerminal.id}
                            </div>
                        </div>
                    )}

                    {error && (
                        <Alert variant="danger" className="mb-3">
                            {error}
                        </Alert>
                    )}

                    <Form.Group className="mb-3">
                        <Form.Label>
                            Opening Balance <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="number"
                            step="0.01"
                            min="0"
                            value={openingBalance}
                            onChange={handleInputChange}
                            isInvalid={!!validationError}
                            placeholder="Enter opening balance"
                            disabled={loading}
                            autoFocus
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationError}
                        </Form.Control.Feedback>
                        <Form.Text className="text-muted">
                            Enter the amount of cash in the register at the start of the session
                        </Form.Text>
                    </Form.Group>

                    <div className="bg-info bg-opacity-10 p-3 rounded">
                        <small className="text-info">
                            <strong>Note:</strong> Starting a session will allow transactions to be processed
                            on this terminal. Make sure the opening balance matches the actual cash in the register.
                        </small>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="secondary"
                        onClick={handleClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="success"
                        type="submit"
                        disabled={loading}
                    >
                        {loading ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Starting Session...
                            </>
                        ) : (
                            'Start Session'
                        )}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

export default StartSessionModal;
