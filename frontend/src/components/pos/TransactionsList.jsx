import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate, useParams} from 'react-router-dom';
import {usePOSSessionTransactions} from '../../hooks/usePOSSessionTransactions';
import {usePOSSession} from '../../hooks/usePOSSessions';
import TransactionTypeBadge from './TransactionTypeBadge';

/**
 * Component for displaying a list of transactions for a specific session
 * @returns {JSX.Element} The transactions list component
 */
const TransactionsList = () => {
    const navigate = useNavigate();
    const {sessionId} = useParams();
    const {session} = usePOSSession(sessionId);
    const {
        transactions,
        loading,
        error,
        pagination,
        fetchTransactions,
        setError
    } = usePOSSessionTransactions(sessionId);

    // Search and filter states
    const [searchTerm, setSearchTerm] = useState('');
    const [typeFilter, setTypeFilter] = useState('');
    const [sortField, setSortField] = useState('created');
    const [sortDirection, setSortDirection] = useState('desc');
    const [pageSize, setPageSize] = useState(25);
    const [currentPage, setCurrentPage] = useState(1);

    // Handle search and filtering
    const handleSearch = () => {
        const params = {
            page: currentPage,
            page_size: pageSize,
            ordering: sortDirection === 'desc' ? `-${sortField}` : sortField,
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        if (typeFilter) {
            params.transaction_type = typeFilter;
        }

        fetchTransactions(params);
    };

    // Handle search on Enter key press
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            setCurrentPage(1);
            handleSearch();
        }
    };

    // Handle sort
    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
        setCurrentPage(1);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    // Handle page size change
    const handlePageSizeChange = (e) => {
        setPageSize(parseInt(e.target.value));
        setCurrentPage(1);
    };

    // Update search when dependencies change
    useEffect(() => {
        handleSearch();
    }, [currentPage, pageSize, sortField, sortDirection]);

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pageSize);
        const maxVisiblePages = 5;

        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page
        if (startPage > 1) {
            items.push(
                <Pagination.Item key={1} onClick={() => handlePageChange(1)}>
                    1
                </Pagination.Item>
            );
            if (startPage > 2) {
                items.push(<Pagination.Ellipsis key="start-ellipsis"/>);
            }
        }

        // Visible pages
        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                items.push(<Pagination.Ellipsis key="end-ellipsis"/>);
            }
            items.push(
                <Pagination.Item key={totalPages} onClick={() => handlePageChange(totalPages)}>
                    {totalPages}
                </Pagination.Item>
            );
        }

        return items;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Session Transactions</h2>
                            {session && (
                                <p className="text-muted mb-0">
                                    Session #{session.id} - {session.pos_name}
                                </p>
                            )}
                        </div>
                        <Button
                            variant="outline-secondary"
                            onClick={() => navigate(`/pos/sessions/${sessionId}`)}
                        >
                            Back to Session
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Card */}
            <Card className="mb-4">
                <Card.Body>
                    <Row>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search transactions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Transaction Type</Form.Label>
                                <Form.Select
                                    value={typeFilter}
                                    onChange={(e) => setTypeFilter(e.target.value)}
                                >
                                    <option value="">All Types</option>
                                    <option value="sale">Sale</option>
                                    <option value="refund">Refund</option>
                                    <option value="purchase">Purchase</option>
                                    <option value="refund_purchase">Refund Purchase</option>
                                    <option value="cash_in">Cash In</option>
                                    <option value="cash_out">Cash Out</option>
                                    <option value="expense">Expense</option>
                                    <option value="salary">Salary</option>
                                    <option value="other">Other</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2} className="d-flex align-items-end">
                            <Button variant="outline-primary" onClick={handleSearch}>
                                Search
                            </Button>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Results Card */}
            <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                    <span>
                        {loading ? 'Loading...' : `${pagination.count} Transaction(s) found`}
                    </span>
                    <div className="d-flex align-items-center">
                        <span className="me-2">Show:</span>
                        <Form.Select
                            size="sm"
                            value={pageSize}
                            onChange={handlePageSizeChange}
                            style={{width: 'auto'}}
                        >
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : transactions.length === 0 ? (
                        <div className="text-center p-4">
                            <p className="mb-0">No transactions found.</p>
                        </div>
                    ) : (
                        <Table responsive hover className="mb-0">
                            <thead className="table-light">
                            <tr>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('id')}
                                >
                                    ID {sortField === 'id' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('transaction_type')}
                                >
                                    Type {sortField === 'transaction_type' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('amount')}
                                >
                                    Amount {sortField === 'amount' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th>Description</th>
                                <th>Related Object</th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('created')}
                                >
                                    Created {sortField === 'created' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            {transactions.map((transaction) => (
                                <tr key={transaction.id}>
                                    <td>{transaction.id}</td>
                                    <td>
                                        <TransactionTypeBadge type={transaction.transaction_type} />
                                    </td>
                                    <td>
                                        <strong>${transaction.amount}</strong>
                                    </td>
                                    <td>{transaction.description || '-'}</td>
                                    <td>
                                        {transaction.related_object_type ? (
                                            <small className="text-muted">
                                                {transaction.related_object_type} #{transaction.related_object_id}
                                            </small>
                                        ) : '-'}
                                    </td>
                                    <td>{new Date(transaction.created).toLocaleString()}</td>
                                </tr>
                            ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>

                {/* Pagination */}
                {pagination.count > pageSize && (
                    <Card.Footer>
                        <div className="d-flex justify-content-between align-items-center">
                            <span>
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, pagination.count)} of {pagination.count} entries
                            </span>
                            <Pagination className="mb-0">
                                <Pagination.First
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(1)}
                                />
                                <Pagination.Prev
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(currentPage - 1)}
                                />
                                {generatePaginationItems()}
                                <Pagination.Next
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(currentPage + 1)}
                                />
                                <Pagination.Last
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(Math.ceil(pagination.count / pageSize))}
                                />
                            </Pagination>
                        </div>
                    </Card.Footer>
                )}
            </Card>
        </Container>
    );
};

export default TransactionsList;
