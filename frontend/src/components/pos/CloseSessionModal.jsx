import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Modal, Spinner} from 'react-bootstrap';

/**
 * <PERSON><PERSON> for closing a POS session
 * @param {Object} props - Component props
 * @param {boolean} props.show - Whether to show the modal
 * @param {Function} props.onHide - Function to hide the modal
 * @param {Function} props.onConfirm - Function called when session is closed
 * @param {Object} props.session - The POS session object
 * @returns {JSX.Element} The close session modal component
 */
const CloseSessionModal = ({
                               show,
                               onHide,
                               onConfirm,
                               session
                           }) => {
    const [closingBalance, setClosingBalance] = useState('');
    const [error, setError] = useState('');
    const [validationError, setValidationError] = useState('');
    const [loading, setLoading] = useState(false);

    // Calculate expected balance
    const expectedBalance = session ?
        parseFloat(session.opening_balance) + parseFloat(session.total_sales) - parseFloat(session.total_expenses) : 0;

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setClosingBalance(value);

        // Clear validation error when user starts typing
        if (validationError) {
            setValidationError('');
        }
        if (error) {
            setError('');
        }
    };

    // Validate form
    const validateForm = () => {
        if (!closingBalance.trim()) {
            setValidationError('Closing balance is required');
            return false;
        }

        const balance = parseFloat(closingBalance);
        if (isNaN(balance)) {
            setValidationError('Closing balance must be a valid number');
            return false;
        }

        if (balance < 0) {
            setValidationError('Closing balance cannot be negative');
            return false;
        }

        return true;
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            setLoading(true);
            setError('');
            await onConfirm(parseFloat(closingBalance));

            // Reset form
            setClosingBalance('');
            setValidationError('');
        } catch (err) {
            console.error('Error closing session:', err);

            // Extract error message from response
            if (err.response?.data) {
                if (typeof err.response.data === 'string') {
                    setError(err.response.data);
                } else if (err.response.data.detail) {
                    setError(err.response.data.detail);
                } else if (err.response.data.message) {
                    setError(err.response.data.message);
                } else {
                    // Handle field-specific errors
                    const fieldErrors = [];
                    Object.keys(err.response.data).forEach(field => {
                        if (Array.isArray(err.response.data[field])) {
                            fieldErrors.push(`${field}: ${err.response.data[field].join(', ')}`);
                        } else {
                            fieldErrors.push(`${field}: ${err.response.data[field]}`);
                        }
                    });
                    setError(fieldErrors.join('; '));
                }
            } else {
                setError('Failed to close session. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    // Handle modal close
    const handleClose = () => {
        if (!loading) {
            setClosingBalance('');
            setError('');
            setValidationError('');
            onHide();
        }
    };

    // Calculate difference
    const difference = closingBalance ? parseFloat(closingBalance) - expectedBalance : 0;

    return (
        <Modal show={show} onHide={handleClose} centered size="lg">
            <Modal.Header closeButton>
                <Modal.Title>Close Session</Modal.Title>
            </Modal.Header>
            <Form onSubmit={handleSubmit}>
                <Modal.Body>
                    {session && (
                        <div className="mb-4">
                            <div className="bg-light p-3 rounded">
                                <h6>Session Summary</h6>
                                <div className="row">
                                    <div className="col-md-6">
                                        <strong>Session ID:</strong> {session.id}<br/>
                                        <strong>POS Terminal:</strong> {session.pos_name}<br/>
                                        <strong>User:</strong> {session.user_name}
                                    </div>
                                    <div className="col-md-6">
                                        <strong>Opening Balance:</strong> ${session.opening_balance}<br/>
                                        <strong>Total Sales:</strong> <span className="text-success">${session.total_sales}</span><br/>
                                        <strong>Total Expenses:</strong> <span className="text-danger">${session.total_expenses}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {error && (
                        <Alert variant="danger" className="mb-3">
                            {error}
                        </Alert>
                    )}

                    <div className="mb-3">
                        <div className="bg-info bg-opacity-10 p-3 rounded">
                            <h6>Expected Closing Balance</h6>
                            <div className="d-flex justify-content-between">
                                <span>Opening Balance + Sales - Expenses:</span>
                                <strong>${expectedBalance.toFixed(2)}</strong>
                            </div>
                        </div>
                    </div>

                    <Form.Group className="mb-3">
                        <Form.Label>
                            Actual Closing Balance <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="number"
                            step="0.01"
                            min="0"
                            value={closingBalance}
                            onChange={handleInputChange}
                            isInvalid={!!validationError}
                            placeholder="Enter actual closing balance"
                            disabled={loading}
                            autoFocus
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationError}
                        </Form.Control.Feedback>
                        <Form.Text className="text-muted">
                            Count the actual cash in the register and enter the amount
                        </Form.Text>
                    </Form.Group>

                    {closingBalance && !isNaN(parseFloat(closingBalance)) && (
                        <div className="mb-3">
                            <div className={`p-3 rounded ${difference === 0 ? 'bg-success bg-opacity-10' : 'bg-warning bg-opacity-10'}`}>
                                <h6>Difference Analysis</h6>
                                <div className="d-flex justify-content-between">
                                    <span>Difference (Actual - Expected):</span>
                                    <strong className={difference >= 0 ? 'text-success' : 'text-danger'}>
                                        ${difference.toFixed(2)}
                                    </strong>
                                </div>
                                {difference === 0 && (
                                    <small className="text-success">✓ Perfect match! No discrepancy found.</small>
                                )}
                                {difference > 0 && (
                                    <small className="text-warning">⚠️ Surplus detected. Please verify the count.</small>
                                )}
                                {difference < 0 && (
                                    <small className="text-danger">⚠️ Shortage detected. Please verify the count.</small>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="bg-danger bg-opacity-10 p-3 rounded">
                        <small className="text-danger">
                            <strong>Warning:</strong> Closing the session will finalize all transactions and
                            cannot be undone. Make sure the closing balance is accurate.
                        </small>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="secondary"
                        onClick={handleClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="danger"
                        type="submit"
                        disabled={loading}
                    >
                        {loading ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Closing Session...
                            </>
                        ) : (
                            'Close Session'
                        )}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

export default CloseSessionModal;
