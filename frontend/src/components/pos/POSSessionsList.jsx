import React, {useState, useEffect} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {usePOSSessions} from '../../hooks/usePOSSessions';
import {usePOSTerminals} from '../../hooks/usePOS';
import SessionStatusBadge from './SessionStatusBadge';

/**
 * Component for displaying a list of POS sessions with search, sort, and pagination
 * @returns {JSX.Element} The POS sessions list component
 */
const POSSessionsList = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const {currentUser} = useAuth();
    const {
        sessions,
        loading,
        error,
        pagination,
        fetchSessions,
        setError
    } = usePOSSessions();
    const {posTerminals, fetchPOSTerminals} = usePOSTerminals();

    // Search and filter states
    const [searchTerm, setSearchTerm] = useState('');
    const [posFilter, setPosFilter] = useState(searchParams.get('pos') || '');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFromFilter, setDateFromFilter] = useState('');
    const [dateToFilter, setDateToFilter] = useState('');
    const [sortField, setSortField] = useState('opened_at');
    const [sortDirection, setSortDirection] = useState('desc');
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);

    // Load initial data
    useEffect(() => {
        fetchPOSTerminals();
        handleSearch();
    }, []);

    // Handle search and filtering
    const handleSearch = () => {
        const params = {
            page: currentPage,
            page_size: pageSize,
            ordering: sortDirection === 'desc' ? `-${sortField}` : sortField,
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        if (posFilter) {
            params.pos = posFilter;
        }

        if (statusFilter) {
            params.status = statusFilter;
        }

        if (dateFromFilter) {
            params.opened_at_after = dateFromFilter;
        }

        if (dateToFilter) {
            params.opened_at_before = dateToFilter;
        }

        fetchSessions(params);
    };

    // Handle search on Enter key press
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            setCurrentPage(1);
            handleSearch();
        }
    };

    // Handle sort
    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
        setCurrentPage(1);
    };

    // Handle pagination
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    // Handle page size change
    const handlePageSizeChange = (e) => {
        setPageSize(parseInt(e.target.value));
        setCurrentPage(1);
    };

    // Update search when dependencies change
    useEffect(() => {
        handleSearch();
    }, [currentPage, pageSize, sortField, sortDirection]);

    // Generate pagination items
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = Math.ceil(pagination.count / pageSize);
        const maxVisiblePages = 5;

        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page
        if (startPage > 1) {
            items.push(
                <Pagination.Item key={1} onClick={() => handlePageChange(1)}>
                    1
                </Pagination.Item>
            );
            if (startPage > 2) {
                items.push(<Pagination.Ellipsis key="start-ellipsis"/>);
            }
        }

        // Visible pages
        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                items.push(<Pagination.Ellipsis key="end-ellipsis"/>);
            }
            items.push(
                <Pagination.Item key={totalPages} onClick={() => handlePageChange(totalPages)}>
                    {totalPages}
                </Pagination.Item>
            );
        }

        return items;
    };

    const getPOSTerminalName = (posId) => {
        const terminal = posTerminals.find(t => t.id === posId);
        return terminal ? terminal.name : 'Unknown';
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Sessions</h2>
                        <Button
                            variant="outline-secondary"
                            onClick={() => navigate('/pos/terminals')}
                        >
                            Manage Terminals
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Search and Filter Card */}
            <Card className="mb-4">
                <Card.Body>
                    <Row>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search sessions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>POS Terminal</Form.Label>
                                <Form.Select
                                    value={posFilter}
                                    onChange={(e) => setPosFilter(e.target.value)}
                                >
                                    <option value="">All Terminals</option>
                                    {posTerminals.map(terminal => (
                                        <option key={terminal.id} value={terminal.id}>
                                            {terminal.name}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                >
                                    <option value="">All Statuses</option>
                                    <option value="open">Open</option>
                                    <option value="closed">Closed</option>
                                    <option value="suspended">Suspended</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>From Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={dateFromFilter}
                                    onChange={(e) => setDateFromFilter(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>To Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={dateToFilter}
                                    onChange={(e) => setDateToFilter(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={1} className="d-flex align-items-end">
                            <Button variant="outline-primary" onClick={handleSearch}>
                                Search
                            </Button>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)}>
                    {error}
                </Alert>
            )}

            {/* Results Card */}
            <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                    <span>
                        {loading ? 'Loading...' : `${pagination.count} Session(s) found`}
                    </span>
                    <div className="d-flex align-items-center">
                        <span className="me-2">Show:</span>
                        <Form.Select
                            size="sm"
                            value={pageSize}
                            onChange={handlePageSizeChange}
                            style={{width: 'auto'}}
                        >
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : sessions.length === 0 ? (
                        <div className="text-center p-4">
                            <p className="mb-0">No sessions found.</p>
                        </div>
                    ) : (
                        <Table responsive hover className="mb-0">
                            <thead className="table-light">
                            <tr>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('id')}
                                >
                                    ID {sortField === 'id' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('pos_name')}
                                >
                                    POS Terminal {sortField === 'pos_name' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('user_name')}
                                >
                                    User {sortField === 'user_name' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('status')}
                                >
                                    Status {sortField === 'status' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('opened_at')}
                                >
                                    Opened {sortField === 'opened_at' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('closed_at')}
                                >
                                    Closed {sortField === 'closed_at' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('opening_balance')}
                                >
                                    Opening Balance {sortField === 'opening_balance' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th
                                    style={{cursor: 'pointer'}}
                                    onClick={() => handleSort('total_sales')}
                                >
                                    Total Sales {sortField === 'total_sales' && (sortDirection === 'asc' ? '↑' : '↓')}
                                </th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            {sessions.map((session) => (
                                <tr key={session.id}>
                                    <td>{session.id}</td>
                                    <td>{session.pos_name || getPOSTerminalName(session.pos)}</td>
                                    <td>{session.user_name}</td>
                                    <td><SessionStatusBadge status={session.status} /></td>
                                    <td>{new Date(session.opened_at).toLocaleDateString()}</td>
                                    <td>
                                        {session.closed_at
                                            ? new Date(session.closed_at).toLocaleDateString()
                                            : '-'
                                        }
                                    </td>
                                    <td>${session.opening_balance}</td>
                                    <td>${session.total_sales}</td>
                                    <td>
                                        <Button
                                            variant="outline-primary"
                                            size="sm"
                                            onClick={() => navigate(`/pos/sessions/${session.id}`)}
                                        >
                                            View
                                        </Button>
                                    </td>
                                </tr>
                            ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>

                {/* Pagination */}
                {pagination.count > pageSize && (
                    <Card.Footer>
                        <div className="d-flex justify-content-between align-items-center">
                            <span>
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, pagination.count)} of {pagination.count} entries
                            </span>
                            <Pagination className="mb-0">
                                <Pagination.First
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(1)}
                                />
                                <Pagination.Prev
                                    disabled={currentPage === 1}
                                    onClick={() => handlePageChange(currentPage - 1)}
                                />
                                {generatePaginationItems()}
                                <Pagination.Next
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(currentPage + 1)}
                                />
                                <Pagination.Last
                                    disabled={currentPage === Math.ceil(pagination.count / pageSize)}
                                    onClick={() => handlePageChange(Math.ceil(pagination.count / pageSize))}
                                />
                            </Pagination>
                        </div>
                    </Card.Footer>
                )}
            </Card>
        </Container>
    );
};

export default POSSessionsList;
