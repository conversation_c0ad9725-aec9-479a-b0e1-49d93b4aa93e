import React from 'react';
import {Badge} from 'react-bootstrap';

/**
 * Component for displaying session status with appropriate styling
 * @param {Object} props - Component props
 * @param {string} props.status - The session status
 * @returns {JSX.Element} The session status badge component
 */
const SessionStatusBadge = ({status}) => {
    const getStatusConfig = (status) => {
        const statusConfigs = {
            'open': {
                variant: 'success',
                text: 'Open',
                icon: '🟢'
            },
            'closed': {
                variant: 'secondary',
                text: 'Closed',
                icon: '⚫'
            },
            'suspended': {
                variant: 'warning',
                text: 'Suspended',
                icon: '🟡'
            }
        };

        return statusConfigs[status] || {
            variant: 'secondary',
            text: status || 'Unknown',
            icon: '❓'
        };
    };

    const config = getStatusConfig(status);

    return (
        <Badge bg={config.variant} className="d-inline-flex align-items-center">
            <span className="me-1">{config.icon}</span>
            {config.text}
        </Badge>
    );
};

export default SessionStatusBadge;
