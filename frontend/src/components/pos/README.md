# POS (Point of Sale) Implementation

This directory contains the complete implementation of POS functionality for the ERP frontend application.

## Overview

The POS system allows users to manage point of sale terminals, sessions, and transactions with comprehensive CRUD operations and session management capabilities. The implementation follows React best practices and uses React Bootstrap for styling.

## Features

### POS Terminals Management
- ✅ **List View**: Display all POS terminals with search and filtering
- ✅ **Create Terminal**: Add new POS terminals with warehouse association
- ✅ **Edit Terminal**: Update terminal details (warehouse cannot be changed)
- ✅ **View Terminal**: Display terminal details and active sessions
- ✅ **Delete Terminal**: Remove terminals with confirmation
- ✅ **Session Management**: Start new sessions from terminal view

### POS Sessions Management
- ✅ **List View**: Display all sessions with advanced filtering
- ✅ **Session Details**: View comprehensive session information
- ✅ **Session Actions**: Open, close, suspend, and resume sessions
- ✅ **Financial Tracking**: Track opening/closing balances and differences
- ✅ **Transaction History**: View all transactions within a session

### Session Transactions
- ✅ **Transaction List**: Display all transactions for a session
- ✅ **Transaction Types**: Support for sales, refunds, expenses, etc.
- ✅ **Search & Filter**: Filter by transaction type and search
- ✅ **Real-time Updates**: Automatic session total calculations

### Common Features
- ✅ **Search & Filter**: Comprehensive search and filtering capabilities
- ✅ **Sorting**: Sortable columns with visual indicators
- ✅ **Pagination**: Configurable page sizes with navigation
- ✅ **Error Handling**: User-friendly error messages
- ✅ **Loading States**: User feedback during API operations
- ✅ **Responsive Design**: Mobile-friendly interface
- ✅ **Role-based Access**: Admin-only operations for create/edit/delete

## File Structure

```
pos/
├── README.md                    # This documentation
├── POSTerminalsList.jsx        # Main terminals list with table, search, pagination
├── POSTerminalForm.jsx         # Reusable form component for create/edit
├── POSTerminalCreate.jsx       # Create terminal page
├── POSTerminalEdit.jsx         # Edit terminal page
├── POSTerminalView.jsx         # View terminal details and manage sessions
├── DeleteConfirmModal.jsx      # Delete confirmation modal
├── StartSessionModal.jsx       # Modal for starting new sessions
├── POSSessionsList.jsx         # Sessions list with advanced filtering
├── POSSessionView.jsx          # Session details with transactions
├── CloseSessionModal.jsx       # Modal for closing sessions with balance verification
├── SessionStatusBadge.jsx      # Status display component
├── TransactionsList.jsx        # Transactions list for a session
└── TransactionTypeBadge.jsx    # Transaction type display component
```

## API Integration

### Service Layer
- **posService.js**: Handles POS terminals CRUD operations
- **posSessionsService.js**: Manages POS sessions operations
- **posSessionTransactionsService.js**: Handles session transactions

### Custom Hooks
- **usePOS.js**: Manages POS terminals data and operations
- **usePOSSessions.js**: Manages POS sessions data and operations
- **usePOSSessionTransactions.js**: Manages session transactions data and operations

## Component Patterns

### Data Management
- **Data Fetching**: Uses custom hooks for API integration
- **State Management**: Local component state with proper error and loading states
- **Caching**: Automatic refetching after actions
- **Optimistic Updates**: Updates state before API response for better UX

### Search & Filtering

| Component        | Search Fields | Filter Options                                              |
|------------------|---------------|-------------------------------------------------------------|
| POSTerminalsList | Text search   | Warehouse                                                   |
| POSSessionsList  | Text search   | POS Terminal, Status, Date Range                           |
| TransactionsList | Text search   | Transaction Type                                           |

### Sorting & Pagination

| Component        | Sortable Columns                                                                                                     | Pagination                                                                          |
|------------------|----------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------|
| POSTerminalsList | ID, Name, Created                                                                                                    | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |
| POSSessionsList  | ID, POS Terminal, User, Status, Opened, Closed, Opening Balance, Total Sales                                       | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |
| TransactionsList | ID, Type, Amount, Created                                                                                           | Page size: 10/25/50/100 (selectable), First/Previous/Page Numbers/Next/Last buttons |

### Form Handling
- **Validation**: Client-side validation with custom validation logic
- **Error Handling**: Field-specific error messages, API error extraction and display
- **Libraries**: Uses React's controlled components
- **Submission**: Async submission with loading states and success feedback

### UI Components

- **React-Bootstrap Components**:
    - Layout: Container, Row, Col, Card
    - Forms: Form, Form.Control, Form.Select
    - Feedback: Alert, Badge, Spinner
    - Navigation: Button, Pagination
    - Data Display: Table
    - Modals: Modal

- **Custom Components**:
    - SessionStatusBadge: Color-coded status indicators
    - TransactionTypeBadge: Transaction type with icons
    - Various modals for confirmations and actions

### Session Management Features

#### Starting Sessions
- Opening balance input with validation
- Prevents multiple active sessions per terminal
- Automatic user assignment

#### Closing Sessions
- Closing balance verification
- Difference calculation (actual vs expected)
- Visual indicators for discrepancies
- Confirmation with session summary

#### Session Actions
- Suspend/Resume functionality
- Role-based permissions
- Real-time status updates

### Financial Tracking
- Opening and closing balance management
- Automatic calculation of expected balance
- Difference tracking and alerts
- Transaction categorization (sales vs expenses)

## Navigation

- **Routing**: React Router with route parameters
- **Navigation Patterns**:
    - List → Create/View → Edit
    - Back buttons to return to previous views
    - Automatic redirects after successful operations
- **Menu Integration**: Added to main navigation under "POS" dropdown

## API Endpoints Used

### POS Terminals
- `GET /api/pos/` - List terminals
- `POST /api/pos/` - Create terminal
- `GET /api/pos/{id}/` - Get terminal details
- `PUT /api/pos/{id}/` - Update terminal
- `DELETE /api/pos/{id}/` - Delete terminal
- `POST /api/pos/{id}/start_session/` - Start session

### POS Sessions
- `GET /api/sessions/` - List sessions
- `GET /api/sessions/{id}/` - Get session details
- `POST /api/sessions/{id}/close/` - Close session
- `POST /api/sessions/{id}/suspend/` - Suspend session
- `POST /api/sessions/{id}/resume/` - Resume session

### Session Transactions
- `GET /api/session/{sessionId}/transactions/` - List transactions
- `GET /api/session/{sessionId}/transactions/{id}/` - Get transaction details

## Error Handling

- **Error Display**: Alert components for global errors, field-specific feedback
- **Error Management**: Try/catch blocks, error state in hooks
- **Error Extraction**: Comprehensive error extraction from API responses

## Performance

- **Optimization Techniques**: useCallback for memoized functions, conditional rendering
- **Pagination**: Limits data loading for better performance
- **Optimistic Updates**: Better perceived performance

## Security & Permissions

- **Role-based Access**: Admin-only operations for terminal management
- **Session Ownership**: Users can only manage their own sessions (unless admin)
- **Protected Routes**: All POS routes require authentication

## Usage Examples

### Creating a POS Terminal
1. Navigate to "POS" → "Add Terminal"
2. Fill in terminal name, description, and select warehouse
3. Submit to create the terminal

### Starting a Session
1. Go to terminal view page
2. Click "Start Session"
3. Enter opening balance
4. Session becomes active

### Closing a Session
1. From session view, click "Close Session"
2. Enter actual closing balance
3. Review difference calculation
4. Confirm to close session

## Future Enhancements

- Transaction creation/editing functionality
- Bulk session operations
- Advanced reporting and analytics
- Receipt printing integration
- Barcode scanning support
- Integration with inventory management
- Multi-currency support
