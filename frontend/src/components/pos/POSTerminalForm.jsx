import React, {useState, useEffect} from 'react';
import {<PERSON><PERSON>, But<PERSON>, Card, Form, Spinner} from 'react-bootstrap';
import {useWarehouses} from '../../hooks/useWarehouses';

/**
 * Reusable form component for creating and editing POS terminals
 * @param {Object} props - Component props
 * @param {Object} props.initialData - Initial form data for edit mode
 * @param {Function} props.onSubmit - Form submission handler
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {boolean} props.isEdit - Whether this is the edit mode
 */
const POSTerminalForm = ({
                             initialData = {},
                             onSubmit,
                             loading = false,
                             error = "",
                             isEdit = false
                         }) => {
    const {warehouses, loading: warehousesLoading, fetchWarehouses} = useWarehouses();
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        warehouse: ''
    });
    const [validationErrors, setValidationErrors] = useState({});

    // Load warehouses on component mount
    useEffect(() => {
        fetchWarehouses();
    }, []);

    // Update form data when initialData changes
    useEffect(() => {
        if (initialData && Object.keys(initialData).length > 0) {
            setFormData({
                name: initialData.name || '',
                description: initialData.description || '',
                warehouse: initialData.warehouse || ''
            });
        }
    }, [initialData]);

    // Handle input changes
    const handleInputChange = (e) => {
        const {name, value} = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.name.trim()) {
            errors.name = 'Terminal name is required';
        } else if (formData.name.length > 100) {
            errors.name = 'Terminal name must not exceed 100 characters';
        }

        if (!formData.warehouse) {
            errors.warehouse = 'Warehouse is required';
        }

        if (formData.description && formData.description.length > 1000) {
            errors.description = 'Description must not exceed 1000 characters';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare data for submission
        const submitData = {
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            warehouse: parseInt(formData.warehouse)
        };

        onSubmit(submitData);
    };

    return (
        <Card>
            <Card.Header>
                <h5 className="mb-0">
                    {isEdit ? 'Edit POS Terminal' : 'Create New POS Terminal'}
                </h5>
            </Card.Header>
            <Card.Body>
                {error && (
                    <Alert variant="danger" className="mb-3">
                        {error}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                    {/* Terminal Name */}
                    <Form.Group className="mb-3">
                        <Form.Label>
                            Terminal Name <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            isInvalid={!!validationErrors.name}
                            placeholder="Enter terminal name"
                            disabled={loading || warehousesLoading}
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.name}
                        </Form.Control.Feedback>
                        <Form.Text className="text-muted">
                            Maximum 100 characters
                        </Form.Text>
                    </Form.Group>

                    {/* Warehouse */}
                    <Form.Group className="mb-3">
                        <Form.Label>
                            Warehouse <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Select
                            name="warehouse"
                            value={formData.warehouse}
                            onChange={handleInputChange}
                            isInvalid={!!validationErrors.warehouse}
                            disabled={loading || warehousesLoading || (isEdit && initialData.warehouse)}
                        >
                            <option value="">Select a warehouse</option>
                            {warehouses.map(warehouse => (
                                <option key={warehouse.id} value={warehouse.id}>
                                    {warehouse.name}
                                </option>
                            ))}
                        </Form.Select>
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.warehouse}
                        </Form.Control.Feedback>
                        {isEdit && initialData.warehouse && (
                            <Form.Text className="text-muted">
                                Warehouse cannot be changed after creation
                            </Form.Text>
                        )}
                    </Form.Group>

                    {/* Description */}
                    <Form.Group className="mb-3">
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            name="description"
                            value={formData.description}
                            onChange={handleInputChange}
                            isInvalid={!!validationErrors.description}
                            placeholder="Enter terminal description (optional)"
                            disabled={loading || warehousesLoading}
                        />
                        <Form.Control.Feedback type="invalid">
                            {validationErrors.description}
                        </Form.Control.Feedback>
                        <Form.Text className="text-muted">
                            Maximum 1000 characters
                        </Form.Text>
                    </Form.Group>

                    {/* Submit Buttons */}
                    <div className="d-flex gap-2">
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={loading || warehousesLoading}
                            className="me-2"
                        >
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    {isEdit ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                isEdit ? 'Update Terminal' : 'Create Terminal'
                            )}
                        </Button>
                        <Button
                            variant="secondary"
                            type="button"
                            onClick={() => window.history.back()}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </div>
                </Form>
            </Card.Body>
        </Card>
    );
};

export default POSTerminalForm;
