import React from 'react';
import { Container, Row, Col, <PERSON>, Button } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useEmployees } from '../../hooks/useEmployees';
import EmployeeForm from './EmployeeForm';

/**
 * EmployeeCreate component for creating new employees
 */
const EmployeeCreate = () => {
    const navigate = useNavigate();
    const { createEmployee, loading, error } = useEmployees();

    /**
     * Handle form submission
     */
    const handleSubmit = async (employeeData) => {
        try {
            const newEmployee = await createEmployee(employeeData);
            navigate(`/employees/${newEmployee.id}`, {
                state: { message: 'Employee created successfully!' }
            });
        } catch (err) {
            // Error is handled by the hook and displayed in the form
            console.error('Failed to create employee:', err);
        }
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Create Employee</h2>
                            <p className="text-muted mb-0">
                                Add a new employee to the system
                            </p>
                        </div>
                        <Button
                            variant="outline-secondary"
                            as={Link}
                            to="/employees"
                        >
                            Back to Employees
                        </Button>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <EmployeeForm
                                onSubmit={handleSubmit}
                                loading={loading}
                                error={error}
                                submitButtonText="Create Employee"
                            />
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default EmployeeCreate;
