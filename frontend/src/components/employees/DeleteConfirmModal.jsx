import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

/**
 * DeleteConfirmModal component for confirming employee deletion
 */
const DeleteConfirmModal = ({
    show,
    onHide,
    onConfirm,
    employee,
    loading = false
}) => {
    if (!employee) return null;

    const employeeName = `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim() || 'Unknown Employee';

    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title>Confirm Delete</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Alert variant="warning">
                    <Alert.Heading>Are you sure?</Alert.Heading>
                    <p>
                        You are about to delete the employee <strong>{employeeName}</strong>.
                        This action cannot be undone.
                    </p>
                    <hr />
                    <p className="mb-0">
                        <strong>Note:</strong> Deleting an employee will also remove all associated
                        attendance records and salary history. Consider deactivating the employee
                        instead if you want to preserve historical data.
                    </p>
                </Alert>

                <div className="mt-3">
                    <h6>Employee Details:</h6>
                    <ul className="mb-0">
                        <li><strong>Name:</strong> {employeeName}</li>
                        <li><strong>Email:</strong> {employee.user?.email || 'N/A'}</li>
                        <li><strong>Type:</strong> {employee.type || 'N/A'}</li>
                        <li><strong>Status:</strong> {employee.status || 'N/A'}</li>
                        <li><strong>POS Terminal:</strong> {employee.pos?.name || 'Not Assigned'}</li>
                    </ul>
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="secondary"
                    onClick={onHide}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    variant="danger"
                    onClick={onConfirm}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Deleting...
                        </>
                    ) : (
                        'Delete Employee'
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteConfirmModal;
