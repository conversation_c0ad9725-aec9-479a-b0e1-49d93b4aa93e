import React, { useState, useEffect } from 'react';
import { Form, Button, Row, Col, <PERSON><PERSON>, Spin<PERSON>, Card } from 'react-bootstrap';
import { useEmployeeTypes, useEmployeeStatuses, useUserRoles } from '../../hooks/useEmployees';
import { usePOSTerminals } from '../../hooks/usePOS';

/**
 * EmployeeForm component for creating and editing employees
 */
const EmployeeForm = ({
    employee = null,
    onSubmit,
    loading = false,
    error = null,
    submitButtonText = 'Save Employee'
}) => {
    const { posTerminals, fetchPOSTerminals } = usePOSTerminals();
    const employeeTypes = useEmployeeTypes();
    const employeeStatuses = useEmployeeStatuses();
    const userRoles = useUserRoles();

    // Form state
    const [formData, setFormData] = useState({
        // User fields
        user: {
            first_name: '',
            last_name: '',
            email: '',
            phone_number: '',
            password: '',
            role: 'employee'
        },
        // Employee fields
        address: '',
        type: 'daily',
        hour_rate: '',
        day_rate: '',
        hire_date: '',
        status: 'active',
        pos: '',
        identification: null
    });

    const [validationErrors, setValidationErrors] = useState({});
    const [showPassword, setShowPassword] = useState(false);

    // Load POS terminals on component mount
    useEffect(() => {
        fetchPOSTerminals();
    }, [fetchPOSTerminals]);

    // Populate form with employee data when editing
    useEffect(() => {
        if (employee) {
            setFormData({
                user: {
                    first_name: employee.user?.first_name || '',
                    last_name: employee.user?.last_name || '',
                    email: employee.user?.email || '',
                    phone_number: employee.user?.phone_number || '',
                    password: '', // Don't populate password for security
                    role: employee.user?.role || 'employee'
                },
                address: employee.address || '',
                type: employee.type || 'daily',
                hour_rate: employee.hour_rate || '',
                day_rate: employee.day_rate || '',
                hire_date: employee.hire_date || '',
                status: employee.status || 'active',
                pos: employee.pos?.id || '',
                identification: null // Don't populate file input
            });
        }
    }, [employee]);

    /**
     * Handle input changes
     */
    const handleInputChange = (e) => {
        const { name, value, type, files } = e.target;

        if (name.startsWith('user.')) {
            const userField = name.replace('user.', '');
            setFormData(prev => ({
                ...prev,
                user: {
                    ...prev.user,
                    [userField]: value
                }
            }));
        } else if (type === 'file') {
            setFormData(prev => ({
                ...prev,
                [name]: files[0] || null
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    /**
     * Validate form data
     */
    const validateForm = () => {
        const errors = {};

        // User validation
        if (!formData.user.first_name.trim()) {
            errors['user.first_name'] = 'First name is required';
        }
        if (!formData.user.last_name.trim()) {
            errors['user.last_name'] = 'Last name is required';
        }
        if (!formData.user.email.trim()) {
            errors['user.email'] = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.user.email)) {
            errors['user.email'] = 'Email is invalid';
        }
        if (!formData.user.phone_number.trim()) {
            errors['user.phone_number'] = 'Phone number is required';
        }
        if (!employee && !formData.user.password.trim()) {
            errors['user.password'] = 'Password is required for new employees';
        }
        if (formData.user.password && formData.user.password.length < 6) {
            errors['user.password'] = 'Password must be at least 6 characters';
        }

        // Employee validation
        if (!formData.address.trim()) {
            errors.address = 'Address is required';
        }
        if (!formData.hour_rate || parseFloat(formData.hour_rate) <= 0) {
            errors.hour_rate = 'Hour rate must be greater than 0';
        }
        if (!formData.day_rate || parseFloat(formData.day_rate) <= 0) {
            errors.day_rate = 'Day rate must be greater than 0';
        }
        if (!formData.hire_date) {
            errors.hire_date = 'Hire date is required';
        }
        if (!employee && !formData.identification) {
            errors.identification = 'Identification image is required';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    /**
     * Handle form submission
     */
    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Prepare form data for submission
        const submitData = {
            ...formData,
            // Convert empty strings to null for optional fields
            pos: formData.pos || null,
            // Remove password if empty (for updates)
            user: {
                ...formData.user,
                password: formData.user.password || undefined
            }
        };

        // Remove undefined password for updates
        if (employee && !submitData.user.password) {
            delete submitData.user.password;
        }

        onSubmit(submitData);
    };

    /**
     * Format date for input
     */
    const formatDateForInput = (dateString) => {
        if (!dateString) return '';
        return dateString.split('T')[0]; // Remove time part if present
    };

    return (
        <Form onSubmit={handleSubmit}>
            {error && (
                <Alert variant="danger" className="mb-4">
                    {error}
                </Alert>
            )}

            {/* User Information */}
            <Card className="mb-4">
                <Card.Header>
                    <h5 className="mb-0">User Information</h5>
                </Card.Header>
                <Card.Body>
                    <Row className="g-3">
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>First Name *</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="user.first_name"
                                    value={formData.user.first_name}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors['user.first_name']}
                                    placeholder="Enter first name"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors['user.first_name']}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>Last Name *</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="user.last_name"
                                    value={formData.user.last_name}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors['user.last_name']}
                                    placeholder="Enter last name"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors['user.last_name']}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>Email *</Form.Label>
                                <Form.Control
                                    type="email"
                                    name="user.email"
                                    value={formData.user.email}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors['user.email']}
                                    placeholder="Enter email address"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors['user.email']}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>Phone Number *</Form.Label>
                                <Form.Control
                                    type="tel"
                                    name="user.phone_number"
                                    value={formData.user.phone_number}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors['user.phone_number']}
                                    placeholder="Enter phone number"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors['user.phone_number']}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>Role</Form.Label>
                                <Form.Select
                                    name="user.role"
                                    value={formData.user.role}
                                    onChange={handleInputChange}
                                >
                                    {userRoles.map(role => (
                                        <option key={role.value} value={role.value}>
                                            {role.label}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>
                                    Password {!employee && '*'}
                                    {employee && ' (leave blank to keep current)'}
                                </Form.Label>
                                <div className="d-flex">
                                    <Form.Control
                                        type={showPassword ? "text" : "password"}
                                        name="user.password"
                                        value={formData.user.password}
                                        onChange={handleInputChange}
                                        isInvalid={!!validationErrors['user.password']}
                                        placeholder={employee ? "Enter new password" : "Enter password"}
                                    />
                                    <Button
                                        variant="outline-secondary"
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="ms-2"
                                    >
                                        {showPassword ? 'Hide' : 'Show'}
                                    </Button>
                                </div>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors['user.password']}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Employee Information */}
            <Card className="mb-4">
                <Card.Header>
                    <h5 className="mb-0">Employee Information</h5>
                </Card.Header>
                <Card.Body>
                    <Row className="g-3">
                        <Col md={12}>
                            <Form.Group>
                                <Form.Label>Address *</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={3}
                                    name="address"
                                    value={formData.address}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors.address}
                                    placeholder="Enter full address"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.address}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Employee Type</Form.Label>
                                <Form.Select
                                    name="type"
                                    value={formData.type}
                                    onChange={handleInputChange}
                                >
                                    {employeeTypes.map(type => (
                                        <option key={type.value} value={type.value}>
                                            {type.label}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    name="status"
                                    value={formData.status}
                                    onChange={handleInputChange}
                                >
                                    {employeeStatuses.map(status => (
                                        <option key={status.value} value={status.value}>
                                            {status.label}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>POS Terminal</Form.Label>
                                <Form.Select
                                    name="pos"
                                    value={formData.pos}
                                    onChange={handleInputChange}
                                >
                                    <option value="">Not Assigned</option>
                                    {posTerminals.map(pos => (
                                        <option key={pos.id} value={pos.id}>
                                            {pos.name}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Hour Rate *</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    name="hour_rate"
                                    value={formData.hour_rate}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors.hour_rate}
                                    placeholder="0.00"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.hour_rate}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Day Rate *</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    name="day_rate"
                                    value={formData.day_rate}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors.day_rate}
                                    placeholder="0.00"
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.day_rate}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Hire Date *</Form.Label>
                                <Form.Control
                                    type="date"
                                    name="hire_date"
                                    value={formatDateForInput(formData.hire_date)}
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors.hire_date}
                                />
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.hire_date}
                                </Form.Control.Feedback>
                            </Form.Group>
                        </Col>
                        <Col md={12}>
                            <Form.Group>
                                <Form.Label>
                                    Identification Image {!employee && '*'}
                                </Form.Label>
                                <Form.Control
                                    type="file"
                                    name="identification"
                                    onChange={handleInputChange}
                                    isInvalid={!!validationErrors.identification}
                                    accept="image/*"
                                />
                                <Form.Text className="text-muted">
                                    Upload an image of the employee's identification document.
                                </Form.Text>
                                <Form.Control.Feedback type="invalid">
                                    {validationErrors.identification}
                                </Form.Control.Feedback>
                                {employee?.identification && (
                                    <div className="mt-2">
                                        <small className="text-muted">
                                            Current file: {employee.identification.split('/').pop()}
                                        </small>
                                    </div>
                                )}
                            </Form.Group>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Submit Button */}
            <div className="d-flex justify-content-end gap-2">
                <Button
                    type="submit"
                    variant="primary"
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Saving...
                        </>
                    ) : (
                        submitButtonText
                    )}
                </Button>
            </div>
        </Form>
    );
};

export default EmployeeForm;
