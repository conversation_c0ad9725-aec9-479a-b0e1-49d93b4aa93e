import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, Badge, Card, Col, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useSalaries} from '../../hooks/useSalaries';
import {formatCurrency, getSortIcon} from "../../utils";

/**
 * SalaryList component for displaying salary records
 */
const SalaryList = ({employeeId = null}) => {
    const {salaries, loading, error, pagination, fetchSalaries, fetchEmployeeSalaryHistory, setError} = useSalaries();

    // Filter state
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [minSalary, setMinSalary] = useState('');
    const [maxSalary, setMaxSalary] = useState('');
    const [sortBy, setSortBy] = useState('-created');
    const [pageSize, setPageSize] = useState(10);

    /**
     * Load salary records with current filters
     */
    const loadSalaryRecords = useCallback((page = 1) => {
        const params = {
            page,
            page_size: pageSize,
            ordering: sortBy
        };

        if (startDate) {
            params.created__gte = startDate;
        }
        if (endDate) {
            params.created__lte = endDate;
        }
        if (minSalary) {
            params.salary__gte = minSalary;
        }
        if (maxSalary) {
            params.salary__lte = maxSalary;
        }
        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        if (employeeId) {
            fetchEmployeeSalaryHistory(employeeId, params);
        } else {
            fetchSalaries(params);
        }
    }, [fetchSalaries, fetchEmployeeSalaryHistory, employeeId, searchTerm, startDate, endDate, minSalary, maxSalary, sortBy, pageSize]);

    // Load salary records on component mount and when filters change
    useEffect(() => {
        loadSalaryRecords();
    }, [loadSalaryRecords]);

    /**
     * Handle page change
     */
    const handlePageChange = (page) => {
        loadSalaryRecords(page);
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = sortBy === field ? `-${field}` : field;
        setSortBy(newOrdering);
    };

    /**
     * Format date
     */
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    };

    /**
     * Format duration
     */
    const formatDuration = (hours) => {
        if (!hours) return 'N/A';
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        return `${h}h ${m}m`;
    };

    /**
     * Calculate hourly equivalent
     */
    const calculateHourlyEquivalent = (salary, totalHours) => {
        if (!salary || !totalHours) return 'N/A';
        const hourlyRate = parseFloat(salary) / parseFloat(totalHours);
        return formatCurrency(hourlyRate);
    };

    /**
     * Generate pagination items
     */
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = pagination.total_pages;
        const currentPage = pagination.page;

        if (currentPage > 1) {
            items.push(
                <Pagination.First key="first" onClick={() => handlePageChange(1)}/>
            );
            items.push(
                <Pagination.Prev key="prev" onClick={() => handlePageChange(currentPage - 1)}/>
            );
        }

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        if (currentPage < totalPages) {
            items.push(
                <Pagination.Next key="next" onClick={() => handlePageChange(currentPage + 1)}/>
            );
            items.push(
                <Pagination.Last key="last" onClick={() => handlePageChange(totalPages)}/>
            );
        }

        return items;
    };

    return (
        <div>
            {/* Filters */}
            <Card className="mb-4">
                <Card.Body>
                    <Row className="g-3">
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search by employee name, POS Terminal or notes..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>Start Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>End Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={endDate}
                                    onChange={(e) => setEndDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>Min Salary</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={minSalary}
                                    onChange={(e) => setMinSalary(e.target.value)}
                                    placeholder="0.00"
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group>
                                <Form.Label>Max Salary</Form.Label>
                                <Form.Control
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={maxSalary}
                                    onChange={(e) => setMaxSalary(e.target.value)}
                                    placeholder="0.00"
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)} className="mb-3">
                    {error}
                </Alert>
            )}

            {/* Salary Records Table */}
            <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                    <span>
                        Salary Records ({pagination.count} total)
                    </span>
                    <div className="d-flex align-items-center gap-2">
                        <Form.Select
                            size="sm"
                            value={sortBy}
                            onChange={(e) => handleSort(e.target.value)}
                            style={{width: 'auto'}}
                        >
                            <option value="-created">Newest First</option>
                            <option value="created">Oldest First</option>
                            <option value="-salary">Highest Salary</option>
                            <option value="salary">Lowest Salary</option>
                            <option value="-attendance__total_hours">Most Hours</option>
                            <option value="attendance__total_hours">Least Hours</option>
                        </Form.Select>
                        <Form.Select
                            size="sm"
                            value={pageSize}
                            onChange={(e) => setPageSize(parseInt(e.target.value))}
                            style={{width: 'auto'}}
                        >
                            <option value={10}>10 per page</option>
                            <option value={25}>25 per page</option>
                            <option value={50}>50 per page</option>
                            <option value={100}>100 per page</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : salaries.length === 0 ? (
                        <div className="text-center p-4">
                            <p className="mb-0">No salary records found.</p>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                            <tr>
                                {!employeeId &&
                                    <th onClick={() => handleSort('employee__user__first_name')}>Employee{getSortIcon('employee__user__first_name', sortBy)}</th>}
                                <th onClick={() => handleSort('attendance__time_in')}>
                                    Work Date{getSortIcon('attendance__time_in', sortBy)}
                                </th>
                                <th onClick={() => handleSort('attendance__total_hours')}>
                                    Total Hours{getSortIcon('attendance__total_hours', sortBy)}
                                </th>
                                <th onClick={() => handleSort('salary')}>
                                    Salary Amount{getSortIcon('salary', sortBy)}
                                </th>
                                <th>Hourly Rate</th>
                                <th onClick={() => handleSort('employee__type')}>
                                    Employee Type{getSortIcon('employee__type', sortBy)}
                                </th>
                                <th onClick={() => handleSort('employee__pos__name')}>
                                    POS Terminal{getSortIcon('employee__pos__name', sortBy)}
                                </th>
                                <th>Payment Date</th>
                                <th>Notes</th>
                            </tr>
                            </thead>
                            <tbody>
                            {salaries.map((salary) => (
                                <tr key={salary.id}>
                                    {!employeeId && (
                                        <td>
                                            {salary.employee_name || 'N/A'}
                                        </td>
                                    )}
                                    <td>
                                        {formatDate(salary.work_date || salary.attendance_time_in)}
                                    </td>
                                    <td>
                                        {formatDuration(salary.attendance_total_hours || salary.total_hours)}
                                    </td>
                                    <td>
                                        <strong>{formatCurrency(salary.salary)}</strong>
                                    </td>
                                    <td>
                                        {salary.hourly_equivalent
                                            ? formatCurrency(salary.hourly_equivalent)
                                            : calculateHourlyEquivalent(salary.salary, salary.attendance_total_hours || salary.total_hours)
                                        }
                                    </td>
                                    <td>
                                        <Badge bg="secondary">
                                            {salary.employee_type || 'N/A'}
                                        </Badge>
                                    </td>
                                    <td>
                                        {salary.employee_pos_name || 'Not Assigned'}
                                    </td>
                                    <td>
                                        {formatDate(salary.created)}
                                    </td>
                                    <td>
                                        {salary.notes ? (
                                            <span title={salary.notes}>
                                                    {salary.notes.length > 30
                                                        ? `${salary.notes.substring(0, 30)}...`
                                                        : salary.notes
                                                    }
                                                </span>
                                        ) : (
                                            'No notes'
                                        )}
                                    </td>
                                </tr>
                            ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
                {pagination.total_pages > 1 && (
                    <Card.Footer>
                        <div className="d-flex justify-content-between align-items-center">
                            <span className="text-muted">
                                Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                {pagination.count} entries
                            </span>
                            <Pagination className="mb-0">
                                {generatePaginationItems()}
                            </Pagination>
                        </div>
                    </Card.Footer>
                )}
            </Card>
        </div>
    );
};

export default SalaryList;
