import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, Badge, Card, Col, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useAttendance} from '../../hooks/useAttendance';
import {getSortIcon} from "../../utils";

/**
 * AttendanceList component for displaying attendance records
 */
const AttendanceList = ({employeeId = null}) => {
    const {
        attendanceRecords,
        loading,
        error,
        pagination,
        fetchAttendanceRecords,
        fetchEmployeeAttendance,
        setError
    } = useAttendance();

    // Filter state
    const [startDate, setStartDate] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [endDate, setEndDate] = useState('');
    const [sortBy, setSortBy] = useState('-time_in');
    const [pageSize, setPageSize] = useState(10);

    /**
     * Load attendance records with current filters
     */
    const loadAttendanceRecords = useCallback((page = 1) => {
        const params = {
            page,
            page_size: pageSize,
            ordering: sortBy
        };

        if (startDate) {
            params.time_in__gte = startDate;
        }
        if (endDate) {
            params.time_in__lte = endDate;
        }
        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }

        if (employeeId) {
            fetchEmployeeAttendance(employeeId, params);
        } else {
            fetchAttendanceRecords(params);
        }
    }, [fetchAttendanceRecords, fetchEmployeeAttendance, employeeId, searchTerm, startDate, endDate, sortBy, pageSize]);

    // Load attendance records on component mount and when filters change
    useEffect(() => {
        loadAttendanceRecords();
    }, [loadAttendanceRecords]);


    /**
     * Handle page change
     */
    const handlePageChange = (page) => {
        loadAttendanceRecords(page);
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = sortBy === field ? `-${field}` : field;
        setSortBy(newOrdering);
    };
    /**
     * Format duration
     */
    const formatDuration = (hours) => {
        if (!hours) return 'N/A';
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        return `${h}h ${m}m`;
    };

    /**
     * Get attendance status
     */
    const getAttendanceStatus = (record) => {
        if (!record.time_out) {
            return {status: 'In Progress', variant: 'warning'};
        }
        if (record.total_hours >= 8) {
            return {status: 'Full Day', variant: 'success'};
        }
        if (record.total_hours >= 4) {
            return {status: 'Half Day', variant: 'info'};
        }
        return {status: 'Short Day', variant: 'secondary'};
    };

    /**
     * Generate pagination items
     */
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = pagination.total_pages;
        const currentPage = pagination.page;

        if (currentPage > 1) {
            items.push(
                <Pagination.First key="first" onClick={() => handlePageChange(1)}/>
            );
            items.push(
                <Pagination.Prev key="prev" onClick={() => handlePageChange(currentPage - 1)}/>
            );
        }

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        if (currentPage < totalPages) {
            items.push(
                <Pagination.Next key="next" onClick={() => handlePageChange(currentPage + 1)}/>
            );
            items.push(
                <Pagination.Last key="last" onClick={() => handlePageChange(totalPages)}/>
            );
        }

        return items;
    };

    return (
        <div>
            {/* Filters */}
            <Card className="mb-4">
                <Card.Body>
                    <Row className="g-3">
                        <Col md={4}>
                            <Form.Group>
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Search by employee name, POS Terminal or notes..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>Start Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={3}>
                            <Form.Group>
                                <Form.Label>End Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={endDate}
                                    onChange={(e) => setEndDate(e.target.value)}
                                />
                            </Form.Group>
                        </Col>

                    </Row>
                </Card.Body>
            </Card>

            {/* Error Alert */}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError(null)} className="mb-3">
                    {error}
                </Alert>
            )}

            {/* Attendance Records Table */}
            <Card>
                <Card.Header className="d-flex justify-content-between align-items-center">
                    <span>
                        Attendance Records ({pagination.count} total)
                    </span>
                    <div className="d-flex align-items-center gap-2">
                        <Form.Select
                            size="sm"
                            value={sortBy}
                            onChange={(e) => handleSort(e.target.value)}
                            style={{width: 'auto'}}
                        >
                            <option value="employee__user__first_name">Employee A-Z</option>
                            <option value="-employee__user__first_name">Employee Z-A</option>
                            <option value="employee__pos__name">POS Terminal A-Z</option>
                            <option value="-employee__pos__name">POS Terminal Z-A</option>
                            <option value="-time_in">Time IN Newest First</option>
                            <option value="time_in">Time IN Oldest First</option>
                            <option value="-total_hours">Most Hours</option>
                            <option value="total_hours">Least Hours</option>
                        </Form.Select>
                        <Form.Select
                            size="sm"
                            value={pageSize}
                            onChange={(e) => setPageSize(parseInt(e.target.value))}
                            style={{width: 'auto'}}
                        >
                            <option value={10}>10 per page</option>
                            <option value={25}>25 per page</option>
                            <option value={50}>50 per page</option>
                            <option value={100}>100 per page</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    {loading ? (
                        <div className="text-center p-4">
                            <Spinner animation="border" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </Spinner>
                        </div>
                    ) : attendanceRecords.length === 0 ? (
                        <div className="text-center p-4">
                            <p className="mb-0">No attendance records found.</p>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                            <tr>
                                {!employeeId &&
                                    <th onClick={() => handleSort('employee__user__first_name')}>
                                        Employee{getSortIcon('employee__user__first_name', sortBy)}
                                    </th>
                                }
                                <th onClick={() => handleSort('employee__pos__name')}>
                                    POS Terminal{getSortIcon('employee__pos__name', sortBy)}
                                </th>
                                <th onClick={() => handleSort('time_in')}>
                                    Time In{getSortIcon('time_in', sortBy)}
                                </th>
                                <th onClick={() => handleSort('time_out')}>
                                    Time Out{getSortIcon('time_out', sortBy)}
                                </th>
                                <th>Total Hours</th>
                                <th>Status</th>
                                <th>Notes</th>
                            </tr>
                            </thead>
                            <tbody>
                            {attendanceRecords.map((record) => {
                                const {status, variant} = getAttendanceStatus(record);
                                return (
                                    <tr key={record.id}>
                                        {!employeeId && (
                                            <td>
                                                {record.employee?.user?.first_name} {record.employee?.user?.last_name}
                                            </td>
                                        )}
                                        <td>
                                            {record.employee?.pos?.name || 'Not Assigned'}
                                        </td>
                                        <td>
                                            {record.time_in ? new Date(record.time_in).toLocaleString() : 'N/A'}
                                        </td>
                                        <td>
                                            {record.time_out ? new Date(record.time_out).toLocaleString() : 'Still Working'}
                                        </td>
                                        <td>{formatDuration(record.total_hours)}</td>
                                        <td>
                                            <Badge bg={variant}>{status}</Badge>
                                        </td>
                                        <td>
                                            {record.notes ? (
                                                <span title={record.notes}>
                                                        {record.notes.length > 50
                                                            ? `${record.notes.substring(0, 50)}...`
                                                            : record.notes
                                                        }
                                                    </span>
                                            ) : (
                                                'No notes'
                                            )}
                                        </td>
                                    </tr>
                                );
                            })}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
                {pagination.total_pages > 1 && (
                    <Card.Footer>
                        <div className="d-flex justify-content-between align-items-center">
                            <span className="text-muted">
                                Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                {pagination.count} entries
                            </span>
                            <Pagination className="mb-0">
                                {generatePaginationItems()}
                            </Pagination>
                        </div>
                    </Card.Footer>
                )}
            </Card>
        </div>
    );
};

export default AttendanceList;
