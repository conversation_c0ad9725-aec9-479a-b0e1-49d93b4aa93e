import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useEmployee, useEmployees } from '../../hooks/useEmployees';
import EmployeeForm from './EmployeeForm';

/**
 * EmployeeEdit component for editing existing employees
 */
const EmployeeEdit = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { employee, loading: fetchLoading, error: fetchError } = useEmployee(id);
    const { updateEmployee, loading: updateLoading, error: updateError } = useEmployees();

    /**
     * Handle form submission
     */
    const handleSubmit = async (employeeData) => {
        try {
            const updatedEmployee = await updateEmployee(id, employeeData);
            navigate(`/employees/${updatedEmployee.id}`, {
                state: { message: 'Employee updated successfully!' }
            });
        } catch (err) {
            // Error is handled by the hook and displayed in the form
            console.error('Failed to update employee:', err);
        }
    };

    // Show loading spinner while fetching employee data
    if (fetchLoading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Edit Employee</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body className="text-center p-5">
                                <Spinner animation="border" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </Spinner>
                                <p className="mt-3 mb-0">Loading employee data...</p>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if failed to fetch employee
    if (fetchError) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Edit Employee</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            <Alert.Heading>Error Loading Employee</Alert.Heading>
                            <p className="mb-0">{fetchError}</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show not found if employee doesn't exist
    if (!employee) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Edit Employee</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Employee Not Found</Alert.Heading>
                            <p className="mb-0">
                                The employee you're looking for doesn't exist or has been deleted.
                            </p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    const employeeName = `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim() || 'Unknown Employee';

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Edit Employee</h2>
                            <p className="text-muted mb-0">
                                Editing: {employeeName}
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-primary"
                                as={Link}
                                to={`/employees/${id}`}
                            >
                                View Employee
                            </Button>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <EmployeeForm
                                employee={employee}
                                onSubmit={handleSubmit}
                                loading={updateLoading}
                                error={updateError}
                                submitButtonText="Update Employee"
                            />
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default EmployeeEdit;
