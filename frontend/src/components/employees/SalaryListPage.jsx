import React from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SalaryList from './SalaryList';

/**
 * SalaryListPage component for displaying all salary records
 */
const SalaryListPage = () => {
    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Salary Records</h2>
                            <p className="text-muted mb-0">
                                View and manage employee salary records
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-primary"
                                as={Link}
                                to="/salaries/calculate"
                            >
                                Calculate Salary
                            </Button>
                            <Button
                                variant="outline-info"
                                as={Link}
                                to="/salaries/reports"
                            >
                                Reports
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col>
                    <SalaryList />
                </Col>
            </Row>
        </Container>
    );
};

export default SalaryListPage;
