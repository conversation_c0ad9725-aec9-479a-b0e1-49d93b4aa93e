import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Col, Container, Image, Nav, Row, Spinner, Tab} from 'react-bootstrap';
import {Link, useLocation, useParams} from 'react-router-dom';
import {useEmployee, useEmployeeStatuses, useEmployeeTypes} from '../../hooks/useEmployees';
import AttendanceList from './AttendanceList';
import SalaryList from './SalaryList';
import {formatCurrency} from "../../utils";

/**
 * EmployeeView component for displaying employee details with tabs
 */
const EmployeeView = () => {
    const {id} = useParams();
    const location = useLocation();
    const {employee, loading, error} = useEmployee(id);
    const employeeTypes = useEmployeeTypes();
    const employeeStatuses = useEmployeeStatuses();

    const [activeTab, setActiveTab] = useState('details');
    const [successMessage, setSuccessMessage] = useState('');

    // Show success message if passed from navigation state
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            // Clear the message from location state
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Auto-hide success message after 5 seconds
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    /**
     * Get status badge variant
     */
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'active':
                return 'success';
            case 'inactive':
                return 'warning';
            case 'terminated':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    /**
     * Get employee type badge variant
     */
    const getTypeBadgeVariant = (type) => {
        switch (type) {
            case 'full_time':
                return 'primary';
            case 'part_time':
                return 'info';
            case 'daily':
                return 'secondary';
            default:
                return 'light';
        }
    };

    /**
     * Format date
     */
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    };

    /**
     * Format employee name
     */
    const formatEmployeeName = (employee) => {
        return `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim() || 'Unknown Employee';
    };

    // Show loading spinner
    if (loading) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Employee Details</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body className="text-center p-5">
                                <Spinner animation="border" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </Spinner>
                                <p className="mt-3 mb-0">Loading employee details...</p>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show error if failed to fetch employee
    if (error) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Employee Details</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="danger">
                            <Alert.Heading>Error Loading Employee</Alert.Heading>
                            <p className="mb-0">{error}</p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    // Show not found if employee doesn't exist
    if (!employee) {
        return (
            <Container fluid>
                <Row className="mb-4">
                    <Col>
                        <div className="d-flex justify-content-between align-items-center">
                            <h2>Employee Details</h2>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Employee Not Found</Alert.Heading>
                            <p className="mb-0">
                                The employee you're looking for doesn't exist or has been deleted.
                            </p>
                        </Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    const employeeName = formatEmployeeName(employee);

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{employeeName}</h2>
                            <p className="text-muted mb-0">
                                Employee ID: {employee.id} • {employee.user?.email}
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-primary"
                                as={Link}
                                to={`/employees/${id}/edit`}
                            >
                                Edit Employee
                            </Button>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/employees"
                            >
                                Back to Employees
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Success Message */}
            {successMessage && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="success" dismissible onClose={() => setSuccessMessage('')}>
                            {successMessage}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Employee Summary Card */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row>
                                <Col md={3} className="text-center">
                                    {employee.identification ? (
                                        <Image
                                            src={employee.identification}
                                            alt="Employee ID"
                                            thumbnail
                                            style={{maxWidth: '200px', maxHeight: '200px'}}
                                        />
                                    ) : (
                                        <div
                                            className="bg-light d-flex align-items-center justify-content-center"
                                            style={{width: '200px', height: '200px', margin: '0 auto'}}
                                        >
                                            <span className="text-muted">No Image</span>
                                        </div>
                                    )}
                                </Col>
                                <Col md={9}>
                                    <Row>
                                        <Col md={6}>
                                            <h5>Personal Information</h5>
                                            <p><strong>Name:</strong> {employeeName}</p>
                                            <p><strong>Email:</strong> {employee.user?.email || 'N/A'}</p>
                                            <p><strong>Phone:</strong> {employee.user?.phone_number || 'N/A'}</p>
                                            <p><strong>Role:</strong> {employee.user?.role || 'N/A'}</p>
                                            <p><strong>Address:</strong> {employee.address || 'N/A'}</p>
                                        </Col>
                                        <Col md={6}>
                                            <h5>Employment Details</h5>
                                            <p>
                                                <strong>Type:</strong>{' '}
                                                <Badge bg={getTypeBadgeVariant(employee.type)}>
                                                    {employeeTypes.find(t => t.value === employee.type)?.label || employee.type}
                                                </Badge>
                                            </p>
                                            <p>
                                                <strong>Status:</strong>{' '}
                                                <Badge bg={getStatusBadgeVariant(employee.status)}>
                                                    {employeeStatuses.find(s => s.value === employee.status)?.label || employee.status}
                                                </Badge>
                                            </p>
                                            <p><strong>POS Terminal:</strong> {employee.pos?.name || 'Not Assigned'}</p>
                                            <p><strong>Hire Date:</strong> {formatDate(employee.hire_date)}</p>
                                            <p><strong>Day Rate:</strong> {formatCurrency(employee.day_rate)}</p>
                                            <p><strong>Hour Rate:</strong> {formatCurrency(employee.hour_rate)}</p>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Tabs */}
            <Row>
                <Col>
                    <Tab.Container activeKey={activeTab} onSelect={setActiveTab}>
                        <Nav variant="tabs" className="mb-3">
                            <Nav.Item>
                                <Nav.Link eventKey="details">Details</Nav.Link>
                            </Nav.Item>
                            <Nav.Item>
                                <Nav.Link eventKey="attendance">Attendance</Nav.Link>
                            </Nav.Item>
                            <Nav.Item>
                                <Nav.Link eventKey="salary">Salary History</Nav.Link>
                            </Nav.Item>
                        </Nav>

                        <Tab.Content>
                            <Tab.Pane eventKey="details">
                                <Card>
                                    <Card.Header>
                                        <h5 className="mb-0">Employee Details</h5>
                                    </Card.Header>
                                    <Card.Body>
                                        <Row>
                                            <Col md={6}>
                                                <h6>User Information</h6>
                                                <table className="table table-sm">
                                                    <tbody>
                                                    <tr>
                                                        <td><strong>First Name:</strong></td>
                                                        <td>{employee.user?.first_name || 'N/A'}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Last Name:</strong></td>
                                                        <td>{employee.user?.last_name || 'N/A'}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Email:</strong></td>
                                                        <td>{employee.user?.email || 'N/A'}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Phone Number:</strong></td>
                                                        <td>{employee.user?.phone_number || 'N/A'}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Role:</strong></td>
                                                        <td>{employee.user?.role || 'N/A'}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </Col>
                                            <Col md={6}>
                                                <h6>Employment Information</h6>
                                                <table className="table table-sm">
                                                    <tbody>
                                                    <tr>
                                                        <td><strong>Employee Type:</strong></td>
                                                        <td>
                                                            <Badge bg={getTypeBadgeVariant(employee.type)}>
                                                                {employeeTypes.find(t => t.value === employee.type)?.label || employee.type}
                                                            </Badge>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Status:</strong></td>
                                                        <td>
                                                            <Badge bg={getStatusBadgeVariant(employee.status)}>
                                                                {employeeStatuses.find(s => s.value === employee.status)?.label || employee.status}
                                                            </Badge>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>POS Terminal:</strong></td>
                                                        <td>{employee.pos?.name || 'Not Assigned'}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Hire Date:</strong></td>
                                                        <td>{formatDate(employee.hire_date)}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Day Rate:</strong></td>
                                                        <td>${employee.day_rate}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Hour Rate:</strong></td>
                                                        <td>${employee.hour_rate}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Address:</strong></td>
                                                        <td>{employee.address || 'N/A'}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </Col>
                                        </Row>
                                        <Row className="mt-3">
                                            <Col>
                                                <h6>System Information</h6>
                                                <table className="table table-sm">
                                                    <tbody>
                                                    <tr>
                                                        <td><strong>Created:</strong></td>
                                                        <td>{formatDate(employee.created)}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Last Modified:</strong></td>
                                                        <td>{formatDate(employee.modified)}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </Col>
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Tab.Pane>

                            <Tab.Pane eventKey="attendance">
                                <AttendanceList employeeId={id}/>
                            </Tab.Pane>

                            <Tab.Pane eventKey="salary">
                                <SalaryList employeeId={id}/>
                            </Tab.Pane>
                        </Tab.Content>
                    </Tab.Container>
                </Col>
            </Row>
        </Container>
    );
};

export default EmployeeView;
