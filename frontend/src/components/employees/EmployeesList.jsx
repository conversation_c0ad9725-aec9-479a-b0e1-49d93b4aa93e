import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Col, Container, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {Link} from 'react-router-dom';
import {useEmployees, useEmployeeStatuses, useEmployeeTypes} from '../../hooks/useEmployees';
import {usePOSTerminals} from '../../hooks/usePOS';
import DeleteConfirmModal from './DeleteConfirmModal';
import {formatCurrency, getSortIcon} from "../../utils";

/**
 * EmployeesList component displays a paginated list of employees with search and filtering capabilities
 */
const EmployeesList = () => {
    const {employees, loading, error, pagination, fetchEmployees, deleteEmployee, setError} = useEmployees();
    const {posTerminals, fetchPOSTerminals} = usePOSTerminals();
    const employeeTypes = useEmployeeTypes();
    const employeeStatuses = useEmployeeStatuses();

    // Search and filter state
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [typeFilter, setTypeFilter] = useState('');
    const [posFilter, setPosFilter] = useState('');
    const [sortBy, setSortBy] = useState('-created');
    const [pageSize, setPageSize] = useState(10);

    // Delete modal state
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [employeeToDelete, setEmployeeToDelete] = useState(null);

    /**
     * Load employees with current filters
     */
    const loadEmployees = useCallback((page = 1) => {
        const params = {
            page,
            page_size: pageSize,
            ordering: sortBy
        };

        if (searchTerm.trim()) {
            params.search = searchTerm.trim();
        }
        if (statusFilter) {
            params.status = statusFilter;
        }
        if (typeFilter) {
            params.type = typeFilter;
        }
        if (posFilter) {
            params.pos = posFilter;
        }

        fetchEmployees(params);
    }, [fetchEmployees, searchTerm, statusFilter, typeFilter, posFilter, sortBy, pageSize]);

    // Load employees on component mount and when filters change
    useEffect(() => {
        loadEmployees();
    }, [loadEmployees]);

    // Load POS terminals for filter dropdown
    useEffect(() => {
        fetchPOSTerminals();
    }, [fetchPOSTerminals]);


    /**
     * Handle filter changes
     */
    const handleFilterChange = (filterType, value) => {
        switch (filterType) {
            case 'status':
                setStatusFilter(value);
                break;
            case 'type':
                setTypeFilter(value);
                break;
            case 'pos':
                setPosFilter(value);
                break;
            case 'sort':
                setSortBy(value);
                break;
            case 'pageSize':
                setPageSize(value);
                break;
            default:
                break;
        }
    };

    /**
     * Handle page change
     */
    const handlePageChange = (page) => {
        loadEmployees(page);
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrdering = sortBy === field ? `-${field}` : field;
        setSortBy(newOrdering);
    };


    /**
     * Handle delete employee
     */
    const handleDeleteClick = (employee) => {
        setEmployeeToDelete(employee);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            await deleteEmployee(employeeToDelete.id);
            setShowDeleteModal(false);
            setEmployeeToDelete(null);
            loadEmployees(pagination.page);
        } catch (err) {
            // Error is handled by the hook
        }
    };

    /**
     * Get status badge variant
     */
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'active':
                return 'success';
            case 'inactive':
                return 'warning';
            case 'terminated':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    /**
     * Get employee type badge variant
     */
    const getTypeBadgeVariant = (type) => {
        switch (type) {
            case 'full_time':
                return 'primary';
            case 'part_time':
                return 'info';
            case 'daily':
                return 'secondary';
            default:
                return 'light';
        }
    };

    /**
     * Format employee name
     */
    const formatEmployeeName = (employee) => {
        return `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim() || 'N/A';
    };

    /**
     * Format date
     */
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    };

    /**
     * Generate pagination items
     */
    const generatePaginationItems = () => {
        const items = [];
        const totalPages = pagination.total_pages;
        const currentPage = pagination.page;

        // First page
        if (currentPage > 1) {
            items.push(
                <Pagination.First key="first" onClick={() => handlePageChange(1)}/>
            );
            items.push(
                <Pagination.Prev key="prev" onClick={() => handlePageChange(currentPage - 1)}/>
            );
        }

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Last page
        if (currentPage < totalPages) {
            items.push(
                <Pagination.Next key="next" onClick={() => handlePageChange(currentPage + 1)}/>
            );
            items.push(
                <Pagination.Last key="last" onClick={() => handlePageChange(totalPages)}/>
            );
        }

        return items;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>Employees</h2>
                        <Button
                            variant="primary"
                            as={Link}
                            to="/employees/create"
                        >
                            Add Employee
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Search and Filters */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="g-3">
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>Search</Form.Label>
                                        <Form.Control
                                            type="text"
                                            placeholder="Search by name, email, or address..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>Status</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => handleFilterChange('status', e.target.value)}
                                        >
                                            <option value="">All Statuses</option>
                                            {employeeStatuses.map(status => (
                                                <option key={status.value} value={status.value}>
                                                    {status.label}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>Type</Form.Label>
                                        <Form.Select
                                            value={typeFilter}
                                            onChange={(e) => handleFilterChange('type', e.target.value)}
                                        >
                                            <option value="">All Types</option>
                                            {employeeTypes.map(type => (
                                                <option key={type.value} value={type.value}>
                                                    {type.label}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>POS Terminal</Form.Label>
                                        <Form.Select
                                            value={posFilter}
                                            onChange={(e) => handleFilterChange('pos', e.target.value)}
                                        >
                                            <option value="">All Terminals</option>
                                            {posTerminals.map(pos => (
                                                <option key={pos.id} value={pos.id}>
                                                    {pos.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-3">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError(null)}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Employees Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header className="d-flex justify-content-between align-items-center">
                            <span>
                                Employees ({pagination.count} total)
                            </span>
                            <div className="d-flex align-items-center gap-2">
                                <Form.Select
                                    size="sm"
                                    value={pageSize}
                                    onChange={(e) => handleFilterChange('pageSize', parseInt(e.target.value))}
                                    style={{width: 'auto'}}
                                >
                                    <option value={10}>10 per page</option>
                                    <option value={25}>25 per page</option>
                                    <option value={50}>50 per page</option>
                                    <option value={100}>100 per page</option>
                                </Form.Select>
                                <Form.Select
                                    size="sm"
                                    value={sortBy}
                                    onChange={(e) => handleSort(e.target.value)}
                                    style={{width: 'auto'}}
                                >
                                    <option value="-created">Newest First</option>
                                    <option value="created">Oldest First</option>
                                    <option value="user__first_name">Name A-Z</option>
                                    <option value="-user__first_name">Name Z-A</option>
                                    <option value="hire_date">Hire Date (Oldest)</option>
                                    <option value="-hire_date">Hire Date (Newest)</option>
                                </Form.Select>
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : employees.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="mb-0">No employees found.</p>
                                </div>
                            ) : (
                                <Table responsive hover>
                                    <thead className="listing-table-header">
                                    <tr>
                                        <th onClick={() => handleSort('user__first_name')}>
                                            Name{getSortIcon('user__first_name', sortBy)}
                                        </th>
                                        <th onClick={() => handleSort('user__email')}>Email{getSortIcon('user__email', sortBy)}</th>
                                        <th onClick={() => handleSort('type')}>Type{getSortIcon('type', sortBy)}</th>
                                        <th onClick={() => handleSort('status')}>Status{getSortIcon('status', sortBy)}</th>
                                        <th onClick={() => handleSort('pos__name')}>POS
                                            Terminal{getSortIcon('pos__name', sortBy)}</th>
                                        <th onClick={() => handleSort('hire_date')}>Hire
                                            Date{getSortIcon('hire_date', sortBy)}</th>
                                        <th onClick={() => handleSort('day_rate')}>Day
                                            Rate{getSortIcon('day_rate', sortBy)}</th>
                                        <th onClick={() => handleSort('hour_rate')}>Hour
                                            Rate{getSortIcon('hour_rate', sortBy)}</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {employees.map((employee) => (
                                        <tr key={employee.id}>
                                            <td>
                                                <Link
                                                    to={`/employees/${employee.id}`}
                                                    className="text-decoration-none"
                                                >
                                                    {formatEmployeeName(employee)}
                                                </Link>
                                            </td>
                                            <td>{employee.user?.email || 'N/A'}</td>
                                            <td>
                                                <Badge bg={getTypeBadgeVariant(employee.type)}>
                                                    {employeeTypes.find(t => t.value === employee.type)?.label || employee.type}
                                                </Badge>
                                            </td>
                                            <td>
                                                <Badge bg={getStatusBadgeVariant(employee.status)}>
                                                    {employeeStatuses.find(s => s.value === employee.status)?.label || employee.status}
                                                </Badge>
                                            </td>
                                            <td>{employee.pos?.name || 'Not Assigned'}</td>
                                            <td>{formatDate(employee.hire_date)}</td>
                                            <td>{formatCurrency(employee.day_rate)}</td>
                                            <td>{formatCurrency(employee.hour_rate)}</td>
                                            <td>
                                                <div className="d-flex gap-1">
                                                    <Button
                                                        size="sm"
                                                        variant="outline-primary"
                                                        as={Link}
                                                        to={`/employees/${employee.id}`}
                                                    >
                                                        View
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline-secondary"
                                                        as={Link}
                                                        to={`/employees/${employee.id}/edit`}
                                                    >
                                                        Edit
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline-danger"
                                                        onClick={() => handleDeleteClick(employee)}
                                                    >
                                                        Delete
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                        {pagination.total_pages > 1 && (
                            <Card.Footer>
                                <div className="d-flex justify-content-between align-items-center">
                                    <span className="text-muted">
                                        Showing {((pagination.page - 1) * pagination.page_size) + 1} to{' '}
                                        {Math.min(pagination.page * pagination.page_size, pagination.count)} of{' '}
                                        {pagination.count} entries
                                    </span>
                                    <Pagination className="mb-0">
                                        {generatePaginationItems()}
                                    </Pagination>
                                </div>
                            </Card.Footer>
                        )}
                    </Card>
                </Col>
            </Row>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmModal
                show={showDeleteModal}
                onHide={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteConfirm}
                employee={employeeToDelete}
                loading={loading}
            />
        </Container>
    );
};

export default EmployeesList;
