import React, { useState } from 'react';
import { Contain<PERSON>, Row, Col, Card, Form, But<PERSON>, Alert, Spinner } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAttendance } from '../../hooks/useAttendance';

/**
 * TimeInOutForm component for recording employee time-in and time-out
 */
const TimeInOutForm = ({ type = 'time-in' }) => {
    const navigate = useNavigate();
    const { recordTimeIn, recordTimeOut, loading, error } = useAttendance();

    const [formData, setFormData] = useState({
        username: '',
        password: '',
        image: null,
        notes: ''
    });

    const [validationErrors, setValidationErrors] = useState({});
    const [successMessage, setSuccessMessage] = useState('');

    /**
     * Handle input changes
     */
    const handleInputChange = (e) => {
        const { name, value, type: inputType, files } = e.target;

        if (inputType === 'file') {
            setFormData(prev => ({
                ...prev,
                [name]: files[0] || null
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        // Clear validation error for this field
        if (validationErrors[name]) {
            setValidationErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    /**
     * Validate form data
     */
    const validateForm = () => {
        const errors = {};

        if (!formData.username.trim()) {
            errors.username = 'Username is required';
        }
        if (!formData.password.trim()) {
            errors.password = 'Password is required';
        }
        if (!formData.image) {
            errors.image = 'Photo is required for attendance verification';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    /**
     * Handle form submission
     */
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            let result;
            if (type === 'time-in') {
                result = await recordTimeIn(formData);
            } else {
                result = await recordTimeOut(formData);
            }

            setSuccessMessage(`${type === 'time-in' ? 'Time-in' : 'Time-out'} recorded successfully!`);

            // Reset form
            setFormData({
                username: '',
                password: '',
                image: null,
                notes: ''
            });

            // Clear file input
            const fileInput = document.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.value = '';
            }

            // Auto-hide success message and redirect after 3 seconds
            setTimeout(() => {
                navigate('/attendance');
            }, 3000);

        } catch (err) {
            // Error is handled by the hook
            console.error(`Failed to record ${type}:`, err);
        }
    };

    const isTimeIn = type === 'time-in';
    const title = isTimeIn ? 'Time In' : 'Time Out';
    const buttonText = isTimeIn ? 'Record Time In' : 'Record Time Out';
    const description = isTimeIn
        ? 'Record your arrival time for today'
        : 'Record your departure time for today';

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="text-center">
                        <h2>{title}</h2>
                        <p className="text-muted mb-0">{description}</p>
                    </div>
                </Col>
            </Row>

            <Row className="justify-content-center">
                <Col md={6} lg={4}>
                    <Card>
                        <Card.Header className="text-center">
                            <h5 className="mb-0">{title} Form</h5>
                        </Card.Header>
                        <Card.Body>
                            {successMessage && (
                                <Alert variant="success" className="mb-4">
                                    {successMessage}
                                    <br />
                                    <small>Redirecting to attendance page...</small>
                                </Alert>
                            )}

                            {error && (
                                <Alert variant="danger" className="mb-4">
                                    {error}
                                </Alert>
                            )}

                            <Form onSubmit={handleSubmit}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Username *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        name="username"
                                        value={formData.username}
                                        onChange={handleInputChange}
                                        isInvalid={!!validationErrors.username}
                                        placeholder="Enter your username"
                                        autoComplete="username"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {validationErrors.username}
                                    </Form.Control.Feedback>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label>Password *</Form.Label>
                                    <Form.Control
                                        type="password"
                                        name="password"
                                        value={formData.password}
                                        onChange={handleInputChange}
                                        isInvalid={!!validationErrors.password}
                                        placeholder="Enter your password"
                                        autoComplete="current-password"
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {validationErrors.password}
                                    </Form.Control.Feedback>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label>Photo *</Form.Label>
                                    <Form.Control
                                        type="file"
                                        name="image"
                                        onChange={handleInputChange}
                                        isInvalid={!!validationErrors.image}
                                        accept="image/*"
                                        capture="user"
                                    />
                                    <Form.Text className="text-muted">
                                        Take a photo for attendance verification
                                    </Form.Text>
                                    <Form.Control.Feedback type="invalid">
                                        {validationErrors.image}
                                    </Form.Control.Feedback>
                                </Form.Group>

                                <Form.Group className="mb-4">
                                    <Form.Label>Notes (Optional)</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        name="notes"
                                        value={formData.notes}
                                        onChange={handleInputChange}
                                        placeholder="Add any notes about your attendance..."
                                    />
                                </Form.Group>

                                <div className="d-grid">
                                    <Button
                                        type="submit"
                                        variant={isTimeIn ? "success" : "warning"}
                                        size="lg"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <Spinner
                                                    as="span"
                                                    animation="border"
                                                    size="sm"
                                                    role="status"
                                                    aria-hidden="true"
                                                    className="me-2"
                                                />
                                                Recording...
                                            </>
                                        ) : (
                                            buttonText
                                        )}
                                    </Button>
                                </div>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col className="text-center">
                    <Button
                        variant="outline-secondary"
                        onClick={() => navigate('/attendance')}
                    >
                        Back to Attendance
                    </Button>
                </Col>
            </Row>
        </Container>
    );
};

export default TimeInOutForm;
