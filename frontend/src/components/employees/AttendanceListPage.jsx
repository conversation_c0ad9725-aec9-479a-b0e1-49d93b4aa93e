import React from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';
import AttendanceList from './AttendanceList';

/**
 * AttendanceListPage component for displaying all attendance records
 */
const AttendanceListPage = () => {
    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Attendance Records</h2>
                            <p className="text-muted mb-0">
                                View and manage employee attendance records
                            </p>
                        </div>
                        <div className="d-flex gap-2">
                            <Button
                                variant="outline-primary"
                                as={Link}
                                to="/attendance/time-in"
                            >
                                Time In
                            </Button>
                            <Button
                                variant="outline-secondary"
                                as={Link}
                                to="/attendance/time-out"
                            >
                                Time Out
                            </Button>
                            <Button
                                variant="outline-info"
                                as={Link}
                                to="/attendance/calendar"
                            >
                                Calendar View
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            <Row>
                <Col>
                    <AttendanceList />
                </Col>
            </Row>
        </Container>
    );
};

export default AttendanceListPage;
