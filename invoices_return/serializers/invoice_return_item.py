from rest_framework import serializers

from invoices_return.models.invoice_return_item import InvoiceReturnItem
from products.models import Product


class InvoiceReturnItemSerializer(serializers.ModelSerializer):
    """
    Serializer for the InvoiceReturnItem model.
    Handles creation and updating of InvoiceReturnItem instances.
    """

    product_id = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(), source="product", write_only=True
    )
    product_name = serializers.StringRelatedField(source="product.name", read_only=True)
    total_price = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="Calculated automatically as quantity × unit_price",
    )

    class Meta:
        model = InvoiceReturnItem
        fields = [
            "id",
            "product_id",
            "product_name",
            "quantity",
            "unit_price",
            "total_price",
            "notes",
            "created",
            "modified",
        ]
        read_only_fields = ["id", "created", "modified", "total_price"]

    def validate_quantity(self, value):
        """Validate that quantity is positive."""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0.")
        return value

    def validate_unit_price(self, value):
        """Validate that unit_price is positive."""
        if value <= 0:
            raise serializers.ValidationError("Unit price must be greater than 0.")
        return value

    def validate(self, data):
        """
        Validate the return item data and calculate total_price.
        """
        quantity = data.get("quantity")
        unit_price = data.get("unit_price")

        # If this is an update, get existing values if not provided
        if self.instance:
            quantity = quantity or self.instance.quantity
            unit_price = unit_price or self.instance.unit_price

        # Ensure we have both values
        if quantity is None or unit_price is None:
            raise serializers.ValidationError(
                "Both quantity and unit_price are required."
            )
        return data
