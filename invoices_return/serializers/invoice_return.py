from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from rest_framework import serializers

from invoices_return.models.invoice_return import InvoiceReturn
from invoices_return.serializers.invoice_return_item import InvoiceReturnItemSerializer
from pos.models import POSSession, POSSessionTransaction
from pos.models.pos_session_transaction import TransactionType as POSTransactionType
from warehouses.models.stock_item import StockItem


class InvoiceReturnSerializer(serializers.ModelSerializer):
    """
    Serializer for the InvoiceReturn model.
    Handles creation and updating of InvoiceReturn instances along with their associated items.
    """

    pos_session_id = serializers.PrimaryKeyRelatedField(
        queryset=POSSession.objects.filter(status=POSSession.Status.OPEN).all(),
        source="pos_session",
        write_only=True,
        required=True,
        allow_null=True,
    )
    warehouse_name = serializers.StringRelatedField(
        source="warehouse.name", read_only=True
    )

    items = InvoiceReturnItemSerializer(many=True, required=True)
    total_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="Calculated automatically from items",
    )

    class Meta:
        model = InvoiceReturn
        fields = [
            "id",
            "warehouse_name",
            "pos_session_id",
            "total_amount",
            "payment_method",
            "notes",
            "items",
            "created",
            "modified",
        ]
        read_only_fields = [
            "id",
            "created",
            "modified",
            "total_amount",
        ]

    def validate_items(self, value):
        """Validate that at least one item is provided."""
        if not value:
            raise serializers.ValidationError("At least one return item is required.")
        return value

    def validate(self, data):
        """
        Validate the invoice return data.
        """
        # Validate POS session ownership for non-admin users
        pos_session = data.get("pos_session")
        if pos_session and not self.context["request"].user.is_admin:
            if not pos_session.pos.employee_set.filter(
                user=self.context["request"].user
            ).exists():
                raise serializers.ValidationError(
                    "Cannot create return for POS session not assigned to you"
                )
        if pos_session:
            data["warehouse"] = pos_session.pos.warehouse

        return data

    @transaction.atomic
    def create(self, validated_data):
        """
        Create a new InvoiceReturn with its associated items.
        """
        items_data = validated_data.pop("items", [])
        pos_session = validated_data.get("pos_session", None)

        # Create the invoice return
        invoice_return = InvoiceReturn.objects.create(**validated_data)

        # Create return items and calculate total
        total_amount = Decimal("0.00")
        for item_data in items_data:
            item_data["invoice_return"] = invoice_return
            item = InvoiceReturnItemSerializer().create(item_data)
            total_amount += item.total_price

        # Update total amount
        invoice_return.total_amount = total_amount
        invoice_return.save(update_fields=["total_amount", "modified"])

        # Update stock items (increase quantities)
        self._update_stock_items(invoice_return)

        # Handle POS transaction if session provided
        if pos_session:
            self._handle_pos_transaction(invoice_return, pos_session)

        return invoice_return

    @transaction.atomic
    def update(self, instance, validated_data):
        """
        Update an existing InvoiceReturn with its associated items.
        """
        items_data = validated_data.pop("items", None)
        pos_session = validated_data.get("pos_session", None)

        # Store old values for stock adjustment
        old_items = list(instance.items.all())

        # Update invoice return fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Handle items update if provided
        if items_data is not None:
            # Delete existing items
            instance.items.all().delete()

            # Create new items
            total_amount = Decimal("0.00")
            for item_data in items_data:
                item_data["invoice_return"] = instance
                item = InvoiceReturnItemSerializer().create(item_data)
                total_amount += item.total_price

            # Update total amount
            instance.total_amount = total_amount

        instance.save()

        # Update stock items (reverse old changes and apply new ones)
        if items_data is not None:
            self._reverse_stock_changes(old_items)
            self._update_stock_items(instance)

        # Handle POS transaction update
        if pos_session:
            self._update_pos_transaction(instance, pos_session)

        return instance

    def _reverse_stock_changes(self, old_items):
        """Reverse stock changes from old items (decrease quantities back)."""
        for item in old_items:
            try:
                stock_item = StockItem.objects.get(
                    warehouse=item.invoice_return.warehouse, product=item.product
                )
                # Decrease stock quantity back (reverse the increase)
                stock_item.quantity -= item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                pass  # If stock item doesn't exist, nothing to reverse

    def _update_pos_transaction(self, invoice_return, pos_session):
        """Update existing POS session transaction."""
        try:
            pos_transaction = POSSessionTransaction.objects.get(
                content_type=ContentType.objects.get_for_model(InvoiceReturn),
                object_id=invoice_return.id,
            )
            old_amount = pos_transaction.amount
            pos_transaction.amount = invoice_return.total_amount
            pos_transaction.save(update_fields=["amount", "modified"])

        except POSSessionTransaction.DoesNotExist:
            # Create new transaction if it doesn't exist
            self._handle_pos_transaction(invoice_return, pos_session)

    def _update_stock_items(self, invoice_return):
        """Increase stock quantities for returned items."""
        for item in invoice_return.items.all():
            try:
                stock_item = StockItem.objects.get(
                    warehouse=invoice_return.warehouse, product=item.product
                )
                # Increase stock quantity (return increases available stock)
                stock_item.quantity += item.quantity
                stock_item.save(update_fields=["quantity", "modified"])
            except StockItem.DoesNotExist:
                # If stock item doesn't exist, create it with the returned quantity
                StockItem.objects.create(
                    warehouse=invoice_return.warehouse,
                    product=item.product,
                    quantity=item.quantity,
                )

    def _handle_pos_transaction(self, invoice_return, pos_session):
        """Create POS session transaction for cash returns."""
        POSSessionTransaction.objects.create(
            session=pos_session,
            transaction_type=POSTransactionType.REFUND,
            amount=invoice_return.total_amount,
            content_type=ContentType.objects.get_for_model(InvoiceReturn),
            object_id=invoice_return.id,
            description="Invoice return refund",
        )
