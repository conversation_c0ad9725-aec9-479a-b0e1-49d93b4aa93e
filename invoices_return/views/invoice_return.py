from django.db import transaction
from rest_framework import filters, serializers, status, viewsets
from rest_framework.response import Response

from invoices_return.models.invoice_return import InvoiceReturn
from invoices_return.serializers.invoice_return import InvoiceReturnSerializer
from utils.permissions import IsAdminOrCashierOrManager


class InvoiceReturnViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing invoice returns.

    Provides CRUD operations for invoice returns with special handling for:
    - Creating POS session transactions for cash returns
    - Updating stock items (increasing quantities)
    - Handling customer refund transactions
    - Validating return items
    """

    queryset = InvoiceReturn.objects.all().prefetch_related(
        "items", "items__product", "warehouse"
    )
    serializer_class = InvoiceReturnSerializer
    permission_classes = [IsAdminOrCashierOrManager]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["notes"]
    ordering_fields = ["created", "total_amount"]
    ordering = ["-created"]
    http_method_names = ["get", "post", "put"]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        if self.request.user.is_authenticated and self.request.user.is_admin:
            return self.queryset

        # Non-admin users only see returns from their warehouse/POS
        return self.queryset.filter(warehouse__pos__employee__user=self.request.user)

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create a new invoice return with business logic."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Validate return items
        self._validate_return_items(serializer.validated_data)

        # Save the invoice return (serializer handles stock updates and POS transactions)
        serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def _validate_return_items(self, validated_data):
        """
        Validate that returned items are valid.
        This can be extended to include business rules like checking against previous sales.
        """
        items_data = validated_data.get("items", [])

        if not items_data:
            return

        # Basic validation - can be extended with more business rules
        for item_data in items_data:
            product = item_data.get("product")
            quantity = item_data.get("quantity")
            unit_price = item_data.get("unit_price")

            if not product:
                raise serializers.ValidationError("Product is required for each item")

            if not quantity or quantity <= 0:
                raise serializers.ValidationError("Quantity must be greater than 0")

            if not unit_price or unit_price <= 0:
                raise serializers.ValidationError("Unit price must be greater than 0")
            if unit_price > product.price:
                raise serializers.ValidationError(
                    f"Unit price (${unit_price}) cannot be grater than product price (${product.price})"
                )
