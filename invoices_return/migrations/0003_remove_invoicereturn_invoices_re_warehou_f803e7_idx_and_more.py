# Generated by Django 5.2.1 on 2025-08-04 10:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("invoices_return", "0002_alter_invoicereturn_options_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="invoicereturn",
            name="invoices_re_warehou_f803e7_idx",
        ),
        migrations.RemoveIndex(
            model_name="invoicereturn",
            name="invoices_re_pos_ses_74e546_idx",
        ),
        migrations.RemoveIndex(
            model_name="invoicereturnitem",
            name="invoices_re_invoice_c13225_idx",
        ),
        migrations.RemoveIndex(
            model_name="invoicereturnitem",
            name="invoices_re_product_7a71ae_idx",
        ),
        migrations.AlterField(
            model_name="invoicereturn",
            name="payment_method",
            field=models.Char<PERSON>ield(
                choices=[("cash", "Cash")],
                default="cash",
                help_text="Method of refund",
                max_length=50,
                verbose_name="payment method",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="invoice_return",
            field=models.ForeignKey(
                default=1,
                help_text="The invoice return this item belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="invoices_return.invoicereturn",
                verbose_name="invoice return",
            ),
            preserve_default=False,
        ),
    ]
