# Generated by Django 5.2.1 on 2025-08-04 09:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("invoices_return", "0001_initial"),
        ("pos", "0002_alter_possession_closing_balance_and_more"),
        ("products", "0002_alter_product_cost_alter_product_price"),
        ("warehouses", "0005_alter_inventorycountitem_difference_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="invoicereturn",
            options={
                "ordering": ["-created"],
                "verbose_name": "invoice return",
                "verbose_name_plural": "invoice returns",
            },
        ),
        migrations.AlterModelOptions(
            name="invoicereturnitem",
            options={
                "ordering": ["-created"],
                "verbose_name": "invoice return item",
                "verbose_name_plural": "invoice return items",
            },
        ),
        migrations.RemoveField(
            model_name="invoicereturnitem",
            name="return_invoice",
        ),
        migrations.AddField(
            model_name="invoicereturn",
            name="pos_session",
            field=models.ForeignKey(
                blank=True,
                help_text="The POS session associated with this return (optional)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="invoice_returns",
                to="pos.possession",
                verbose_name="POS session",
            ),
        ),
        migrations.AddField(
            model_name="invoicereturnitem",
            name="invoice_return",
            field=models.ForeignKey(
                blank=True,
                help_text="The invoice return this item belongs to",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="invoices_return.invoicereturn",
                verbose_name="invoice return",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturn",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Additional notes about this invoice return",
                null=True,
                verbose_name="notes",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturn",
            name="payment_method",
            field=models.CharField(
                choices=[
                    ("cash", "Cash"),
                    ("credit", "Credit"),
                    ("card", "Card"),
                    ("bank_transfer", "Bank Transfer"),
                ],
                default="cash",
                help_text="Method of refund",
                max_length=50,
                verbose_name="payment method",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturn",
            name="total_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total amount of the return (calculated from items)",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(0.0)],
                verbose_name="total amount",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturn",
            name="warehouse",
            field=models.ForeignKey(
                help_text="The warehouse where the return is being processed",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoice_returns",
                to="warehouses.warehouse",
                verbose_name="warehouse",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Additional notes about this return item",
                null=True,
                verbose_name="notes",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="product",
            field=models.ForeignKey(
                help_text="The product being returned",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="invoice_return_items",
                to="products.product",
                verbose_name="product",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="quantity",
            field=models.DecimalField(
                decimal_places=3,
                help_text="Quantity of the product being returned",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.001"))],
                verbose_name="quantity",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="total_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Total price (quantity × unit_price)",
                max_digits=12,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
                verbose_name="total price",
            ),
        ),
        migrations.AlterField(
            model_name="invoicereturnitem",
            name="unit_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Price per unit of the returned product",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0.01"))],
                verbose_name="unit price",
            ),
        ),
        migrations.AddIndex(
            model_name="invoicereturn",
            index=models.Index(
                fields=["warehouse", "-created"], name="invoices_re_warehou_f803e7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoicereturn",
            index=models.Index(
                fields=["pos_session", "-created"],
                name="invoices_re_pos_ses_74e546_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="invoicereturnitem",
            index=models.Index(
                fields=["invoice_return", "-created"],
                name="invoices_re_invoice_c13225_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="invoicereturnitem",
            index=models.Index(
                fields=["product", "-created"], name="invoices_re_product_7a71ae_idx"
            ),
        ),
    ]
