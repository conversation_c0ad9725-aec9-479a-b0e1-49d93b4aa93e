# Generated by Django 5.2.1 on 2025-08-04 08:50

import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("products", "0002_alter_product_cost_alter_product_price"),
        ("warehouses", "0005_alter_inventorycountitem_difference_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvoiceReturn",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total amount of the return",
                        max_digits=12,
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[("cash", "Cash")],
                        default="cash",
                        help_text="Method of refund",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about the return",
                        null=True,
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        help_text="The warehouse where the return is being processed",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="invoice_returns",
                        to="warehouses.warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Return",
                "verbose_name_plural": "Invoice Returns",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceReturnItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Quantity of the product being returned",
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                    ),
                ),
                (
                    "unit_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price per unit at the time of return",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total price (quantity × unit_price)",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this return item",
                        null=True,
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        help_text="The product being returned",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="return_items",
                        to="products.product",
                    ),
                ),
                (
                    "return_invoice",
                    models.ForeignKey(
                        help_text="The return this item belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="invoices_return.invoicereturn",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Return Item",
                "verbose_name_plural": "Invoice Return Items",
                "ordering": ["-created"],
            },
        ),
    ]
