from decimal import Decimal

from django.urls import reverse
from rest_framework import status

from invoices_return.models import InvoiceReturn
from pos.models import POSSession, POSSessionTransaction
from utils.test.base_test import BaseTestCase
from utils.test.factories.invoices_return.invoice_return import InvoiceReturnFactory
from utils.test.factories.invoices_return.invoice_return_item import (
    InvoiceReturnItemFactory,
)
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.product.product import ProductFactory
from utils.test.factories.warehouse.stock_item import StockItemFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory
from warehouses.models import StockItem


class InvoiceReturnViewSetTestCase(BaseTestCase):
    """
    Test cases for InvoiceReturnViewSet.

    Tests cover:
    - Authentication and permission testing
    - CRUD operations (Create, Read, Update)
    - Business logic validation
    - Stock management integration
    - POS transaction handling
    - Edge cases and error handling
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("invoices_return:invoice-return-list")
        self.detail_url_name = "invoices_return:invoice-return-detail"

        # Create test products
        self.product1 = ProductFactory.create(
            name="Test Product 1", price=Decimal("15.00"), cost=Decimal("10.00")
        )
        self.product2 = ProductFactory.create(
            name="Test Product 2", price=Decimal("30.00"), cost=Decimal("20.00")
        )

        # Create test POS sessions
        # Use the existing POS from base test setup
        self.pos_session1 = POSSessionFactory.create(
            pos=self.pos,  # Use existing POS from BaseTestCase
            status=POSSession.Status.OPEN,
        )

        # Create another warehouse for the second POS session
        self.warehouse2 = WarehouseFactory.create(name="Test Warehouse 2")
        self.pos_session2 = POSSessionFactory.create(
            pos__warehouse=self.warehouse2, status=POSSession.Status.OPEN
        )

        # Create stock items for products
        self.stock1 = StockItemFactory.create(
            warehouse=self.warehouse, product=self.product1, quantity=Decimal("50.000")
        )
        self.stock2 = StockItemFactory.create(
            warehouse=self.warehouse, product=self.product2, quantity=Decimal("30.000")
        )

        # Create test invoice returns
        self.return1 = InvoiceReturnFactory.create(
            warehouse=self.warehouse,
            pos_session=self.pos_session1,
            payment_method=InvoiceReturn.PaymentMethod.CASH,
            notes="Test return 1",
        )

        self.return2 = InvoiceReturnFactory.create(
            warehouse=self.warehouse,
            pos_session=self.pos_session1,
            payment_method=InvoiceReturn.PaymentMethod.CASH,
            notes="Test return 2",
        )

        # Create test invoice return items
        self.item1 = InvoiceReturnItemFactory.create(
            invoice_return=self.return1,
            product=self.product1,
            quantity=Decimal("5.00"),
            unit_price=Decimal("12.00"),
        )

        self.item2 = InvoiceReturnItemFactory.create(
            invoice_return=self.return1,
            product=self.product2,
            quantity=Decimal("3.00"),
            unit_price=Decimal("22.00"),
        )

        # Recalculate total amounts for the returns
        self.return1.save()  # This will trigger total_amount calculation
        self.return2.save()

    def get_detail_url(self, pk):
        """Helper method to get detail URL for a specific return."""
        return reverse(self.detail_url_name, kwargs={"pk": pk})

    # Authentication & Permission Tests
    def test_list_returns_unauthenticated(self):
        """Unauthenticated users should not be able to list returns."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_returns_as_admin(self):
        """Admin should be able to list all returns."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertGreaterEqual(len(response.data["results"]), 2)

    def test_list_returns_as_manager(self):
        """Manager should be able to list returns."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)

    def test_list_returns_as_cashier(self):
        """Cashier should be able to list returns."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)

    def test_retrieve_return_unauthenticated(self):
        """Unauthenticated users should not be able to retrieve returns."""
        url = self.get_detail_url(self.return1.id)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_return_as_admin(self):
        """Admin should be able to retrieve any return."""
        url = self.get_detail_url(self.return1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.return1.id)
        # Total amount should be calculated from items: (5*12 + 3*22) = 60 + 66 = 126
        # But we need to refresh the return to get the calculated total
        self.return1.refresh_from_db()
        expected_total = str(self.return1.total_amount)
        self.assertEqual(response.data["total_amount"], expected_total)
        self.assertEqual(response.data["payment_method"], "cash")
        self.assertEqual(response.data["notes"], "Test return 1")
        self.assertIn("warehouse_name", response.data)
        self.assertIn("items", response.data)
        self.assertGreaterEqual(len(response.data["items"]), 2)

    def test_retrieve_return_as_manager(self):
        """Manager should be able to retrieve returns."""
        url = self.get_detail_url(self.return1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_return_as_cashier(self):
        """Cashier should be able to retrieve returns."""
        url = self.get_detail_url(self.return1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_nonexistent_return(self):
        """Test retrieving a non-existent return."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_return_as_admin(self):
        """Admin should be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Admin created return",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_price": "12.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "1.00",
                    "unit_price": "22.00",
                },
            ],
        }

        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity
        initial_pos_transaction_count = POSSessionTransaction.objects.count()

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that return was created
        invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
        self.assertEqual(invoice_return.payment_method, "cash")
        self.assertEqual(invoice_return.notes, "Admin created return")
        self.assertEqual(invoice_return.items.count(), 2)

        # Check total amount calculation: (2*12 + 1*22) = 24 + 22 = 46
        expected_total = Decimal("46.00")
        self.assertEqual(invoice_return.total_amount, expected_total)

        # Check that stock was increased (returns add to inventory)
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 + Decimal("2.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 + Decimal("1.000"))

        # Check that POS session transaction was created for cash return
        self.assertEqual(
            POSSessionTransaction.objects.count(), initial_pos_transaction_count + 1
        )
        pos_transaction = POSSessionTransaction.objects.filter(
            object_id=invoice_return.id
        ).first()
        self.assertIsNotNone(pos_transaction)
        self.assertEqual(pos_transaction.amount, invoice_return.total_amount)
        self.assertEqual(pos_transaction.transaction_type, "refund")

    def test_create_return_as_manager(self):
        """Manager should be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Manager return",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.manager_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_return_as_cashier(self):
        """Cashier should be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Cashier return",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.cashier_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_return_unauthenticated(self):
        """Unauthenticated users should not be able to create returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Validation Tests
    def test_create_return_without_items(self):
        """Test creating return without items should fail."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Return without items",
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("items", response.data)

    def test_create_return_empty_items_list(self):
        """Test creating return with empty items list should fail."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_missing_required_fields(self):
        """Test creating return with missing required fields."""
        data = {
            "payment_method": "cash"
            # Missing pos_session_id and items
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_invalid_pos_session(self):
        """Test creating return with non-existent POS session."""
        data = {
            "pos_session_id": 99999,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_closed_pos_session(self):
        """Test creating return with closed POS session should fail."""
        # Create a new warehouse for this test to avoid unique constraint issues
        test_warehouse = WarehouseFactory.create(
            name="Test Warehouse for Closed Session"
        )
        closed_session = POSSessionFactory.create(
            pos__warehouse=test_warehouse, status=POSSession.Status.CLOSED
        )

        data = {
            "pos_session_id": closed_session.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_invalid_item_data(self):
        """Test creating return with invalid item data."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "-1.00",  # Negative quantity
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_negative_unit_price(self):
        """Test creating return with negative unit price."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "-12.00",  # Negative unit price
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_zero_quantity(self):
        """Test creating return with zero quantity."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "0.00",  # Zero quantity
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_zero_unit_price(self):
        """Test creating return with zero unit price."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "0.00",  # Zero unit price
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_return_unit_price_greater_than_product_price(self):
        """Test creating return with unit price greater than product price should fail."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "20.00",  # Greater than product price (15.00)
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("cannot be grater than product price", str(response.data))

    def test_create_return_invalid_payment_method(self):
        """Test creating return with invalid payment method."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "invalid_method",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Update Tests
    def test_update_return_as_admin(self):
        """Admin should be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Updated return notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "13.00",
                }
            ],
        }

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that return was updated
        self.return1.refresh_from_db()
        self.assertEqual(self.return1.payment_method, "cash")
        self.assertEqual(self.return1.notes, "Updated return notes")

        # Check that total amount was recalculated
        expected_total = Decimal("39.00")  # 3 * 13
        self.assertEqual(self.return1.total_amount, expected_total)

    def test_update_return_as_manager(self):
        """Manager should be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Manager updated notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.manager_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_return_as_cashier(self):
        """Cashier should be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "notes": "Cashier updated notes",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }

        response = self.cashier_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_return_unauthenticated(self):
        """Unauthenticated users should not be able to update returns."""
        url = self.get_detail_url(self.return1.id)
        data = {"notes": "Unauthorized update"}

        response = self.client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_nonexistent_return(self):
        """Test updating a non-existent return."""
        url = self.get_detail_url(99999)
        data = {"notes": "Update non-existent"}

        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Business Logic Tests - Stock Management
    def test_stock_increase_on_create(self):
        """Test that stock is increased when creating a return."""
        initial_stock1 = self.stock1.quantity
        initial_stock2 = self.stock2.quantity

        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "5.00",
                    "unit_price": "12.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "3.00",
                    "unit_price": "22.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that stock was increased (returns add to inventory)
        self.stock1.refresh_from_db()
        self.stock2.refresh_from_db()
        self.assertEqual(self.stock1.quantity, initial_stock1 + Decimal("5.000"))
        self.assertEqual(self.stock2.quantity, initial_stock2 + Decimal("3.000"))

    def test_stock_creation_for_nonexistent_stock_item(self):
        """Test that stock item is created with positive quantity if it doesn't exist."""
        # Create a product without stock item
        new_product = ProductFactory.create(
            name="New Product", price=Decimal("25.00"), cost=Decimal("15.00")
        )

        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": new_product.id,
                    "quantity": "2.00",
                    "unit_price": "20.00",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that stock item was created with positive quantity
        stock_item = StockItem.objects.get(
            warehouse=self.warehouse, product=new_product
        )
        self.assertEqual(stock_item.quantity, Decimal("2.000"))

    # POS Transaction Tests
    def test_pos_transaction_creation_for_cash_returns(self):
        """Test that POS transactions are created for cash returns."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_price": "12.00",
                }
            ],
        }

        initial_pos_transaction_count = POSSessionTransaction.objects.count()

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that POS transaction was created
        self.assertEqual(
            POSSessionTransaction.objects.count(), initial_pos_transaction_count + 1
        )

        invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
        pos_transaction = POSSessionTransaction.objects.filter(
            object_id=invoice_return.id
        ).first()

        self.assertIsNotNone(pos_transaction)
        self.assertEqual(pos_transaction.amount, invoice_return.total_amount)
        self.assertEqual(pos_transaction.transaction_type, "refund")

    # Return Items Tests
    def test_return_items_creation(self):
        """Test that return items are created correctly with nested serializer."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "12.00",
                    "notes": "Special return item note",
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that item was created with correct values
        invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
        item = invoice_return.items.first()
        self.assertEqual(item.product, self.product1)
        self.assertEqual(item.quantity, Decimal("3.00"))
        self.assertEqual(item.unit_price, Decimal("12.00"))
        self.assertEqual(item.total_price, Decimal("36.00"))  # 3 * 12
        self.assertEqual(item.notes, "Special return item note")

    def test_total_amount_calculation(self):
        """Test that total_amount is calculated correctly from items."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.50",
                    "unit_price": "10.00",
                },
                {
                    "product_id": self.product2.id,
                    "quantity": "1.25",
                    "unit_price": "20.00",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
        # Item 1: 2.50 * 10.00 = 25.00
        # Item 2: 1.25 * 20.00 = 25.00
        # Total: 50.00
        expected_total = Decimal("50.00")
        self.assertEqual(invoice_return.total_amount, expected_total)
        self.assertEqual(response.data["total_amount"], "50.00")

    # Edge Cases and Additional Features Tests
    def test_return_with_large_amounts(self):
        """Test handling of large return amounts."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "100.00",
                    "unit_price": "15.00",  # Max allowed (product price)
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        if response.status_code == status.HTTP_201_CREATED:
            invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
            expected_total = Decimal("1500.00")  # 100 * 15
            self.assertEqual(invoice_return.total_amount, expected_total)

    def test_precision_handling(self):
        """Test that decimal precision is handled correctly."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.123",  # 3 decimal places for quantity
                    "unit_price": "12.99",  # 2 decimal places for unit_price
                }
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        if response.status_code == status.HTTP_201_CREATED:
            invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
            item = invoice_return.items.first()
            self.assertEqual(item.quantity, Decimal("1.123"))
            self.assertEqual(item.unit_price, Decimal("12.99"))
            # Total price should be calculated correctly and rounded to 2 decimal places
            expected_total = Decimal(
                "14.59"
            )  # 1.123 * 12.99 = 14.58777, rounded to 14.59
            self.assertEqual(item.total_price, expected_total)

    def test_multiple_items_same_product(self):
        """Test creating return with multiple items for the same product."""
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "2.00",
                    "unit_price": "12.00",
                    "notes": "First batch",
                },
                {
                    "product_id": self.product1.id,
                    "quantity": "3.00",
                    "unit_price": "13.00",
                    "notes": "Second batch",
                },
            ],
        }

        response = self.admin_client.post(self.list_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        invoice_return = InvoiceReturn.objects.get(id=response.data["id"])
        self.assertEqual(invoice_return.items.count(), 2)

        # Total should be (2*12) + (3*13) = 24 + 39 = 63
        expected_total = Decimal("63.00")
        self.assertEqual(invoice_return.total_amount, expected_total)

        # Stock should be increased by total quantity: 2 + 3 = 5
        self.stock1.refresh_from_db()
        # Note: This assumes initial stock was not affected by other tests

    # Model String Representation Tests
    def test_return_string_representation(self):
        """Test the string representation of InvoiceReturn model."""
        expected_str = f"Return {self.return1.id} - ${self.return1.total_amount}"
        self.assertEqual(str(self.return1), expected_str)

    def test_return_item_string_representation(self):
        """Test the string representation of InvoiceReturnItem model."""
        expected_str = f"{self.item1.quantity} x {self.item1.product.name} - ${self.item1.total_price}"
        self.assertEqual(str(self.item1), expected_str)

    # Search and Filtering Tests
    def test_search_returns_by_notes(self):
        """Test searching returns by notes."""
        url = f"{self.list_url}?search=Test return 1"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return the return with matching notes
        notes_list = [result["notes"] for result in response.data["results"]]
        self.assertIn("Test return 1", notes_list)

    def test_ordering_returns(self):
        """Test ordering returns by different fields."""
        # Test ordering by created date (default)
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test ordering by total_amount
        url = f"{self.list_url}?ordering=total_amount"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test ordering by created date ascending
        url = f"{self.list_url}?ordering=created"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    # Pagination Tests
    def test_pagination(self):
        """Test that pagination works correctly."""
        # Create additional returns to test pagination (create enough to exceed page size)
        for i in range(25):
            InvoiceReturnFactory.create(
                warehouse=self.warehouse, pos_session=self.pos_session1
            )

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

        # Should have more items than displayed on first page
        total_count = response.data["count"]
        results_count = len(response.data["results"])

        # If pagination is working, either we have a next page or all items fit on one page
        if response.data["next"]:
            self.assertGreater(total_count, results_count)
        else:
            # All items fit on one page, which is also valid
            self.assertEqual(total_count, results_count)

    # Permission and Queryset Filtering Tests
    def test_non_admin_user_queryset_filtering(self):
        """Test that non-admin users only see returns from their warehouse/POS."""
        # This test would need to be implemented based on the specific
        # employee-POS relationship in your system
        pass

    def test_admin_sees_all_returns(self):
        """Test that admin users can see all returns regardless of warehouse."""
        # Create returns in different warehouses
        other_warehouse = WarehouseFactory.create(name="Other Warehouse")
        other_return = InvoiceReturnFactory.create(warehouse=other_warehouse)

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Admin should see returns from all warehouses
        return_ids = [result["id"] for result in response.data["results"]]
        self.assertIn(other_return.id, return_ids)

    # HTTP Method Tests
    def test_http_methods_allowed(self):
        """Test that only allowed HTTP methods work."""
        url = self.get_detail_url(self.return1.id)

        # GET should work
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # PUT should work
        data = {
            "pos_session_id": self.pos_session1.id,
            "payment_method": "cash",
            "items": [
                {
                    "product_id": self.product1.id,
                    "quantity": "1.00",
                    "unit_price": "12.00",
                }
            ],
        }
        response = self.admin_client.put(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # DELETE should not be allowed (not in http_method_names)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
