from decimal import Decimal

from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from invoices_return.models.invoice_return import InvoiceReturn


class InvoiceReturnItem(TimeStampedModel):
    """
    Represents an item within an invoice return.
    Each return item is associated with a return and product, and includes
    details such as quantity, unit price, and total price.
    """

    invoice_return = models.ForeignKey(
        InvoiceReturn,
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name=_("invoice return"),
        help_text=_("The invoice return this item belongs to"),
    )

    product = models.ForeignKey(
        "products.Product",
        on_delete=models.PROTECT,
        related_name="invoice_return_items",
        verbose_name=_("product"),
        help_text=_("The product being returned"),
    )

    quantity = models.DecimalField(
        _("quantity"),
        max_digits=10,
        decimal_places=3,
        help_text=_("Quantity of the product being returned"),
        validators=[MinValueValidator(Decimal("0.001"))],
    )

    unit_price = models.DecimalField(
        _("unit price"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Price per unit of the returned product"),
        validators=[MinValueValidator(Decimal("0.01"))],
    )

    total_price = models.DecimalField(
        _("total price"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Total price (quantity × unit_price)"),
        validators=[MinValueValidator(Decimal("0.01"))],
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this return item"),
    )

    class Meta:
        verbose_name = _("invoice return item")
        verbose_name_plural = _("invoice return items")
        ordering = ["-created"]

    def __str__(self):
        return f"{self.quantity} x {self.product.name} - ${self.total_price}"

    def clean(self):
        """Validate that quantity and unit_price are positive."""
        super().clean()

        if self.quantity is not None and self.quantity <= 0:
            raise ValidationError({"quantity": _("Quantity must be greater than 0.")})

        if self.unit_price is not None and self.unit_price <= 0:
            raise ValidationError(
                {"unit_price": _("Unit price must be greater than 0.")}
            )

    def save(self, *args, **kwargs):
        """Calculate total_price before saving."""
        # Run clean validation
        self.clean()

        # Calculate total_price if not explicitly set or if quantity/unit_price changed
        if self.quantity is not None and self.unit_price is not None:
            calculated_total = self.quantity * self.unit_price
            # Round to 2 decimal places to match field precision
            self.total_price = calculated_total.quantize(Decimal("0.01"))
        super().save(*args, **kwargs)
