from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from pos.models import POSSession
from warehouses.models.warehouse import Warehouse


class InvoiceReturn(TimeStampedModel):
    """
    Model representing a return of an invoice.
    Each return is associated with a warehouse and includes
    financial details such as total amount and payment method.
    """

    class PaymentMethod(models.TextChoices):
        CASH = "cash", _("Cash")

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name="invoice_returns",
        verbose_name=_("warehouse"),
        help_text=_("The warehouse where the return is being processed"),
    )

    pos_session = models.ForeignKey(
        POSSession,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoice_returns",
        verbose_name=_("POS session"),
        help_text=_("The POS session associated with this return (optional)"),
    )

    total_amount = models.DecimalField(
        _("total amount"),
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0.00)],
        help_text=_("Total amount of the return (calculated from items)"),
    )

    payment_method = models.CharField(
        _("payment method"),
        max_length=50,
        choices=PaymentMethod.choices,
        default=PaymentMethod.CASH,
        help_text=_("Method of refund"),
    )

    notes = models.TextField(
        _("notes"),
        blank=True,
        null=True,
        help_text=_("Additional notes about this invoice return"),
    )

    class Meta:
        verbose_name = _("invoice return")
        verbose_name_plural = _("invoice returns")
        ordering = ["-created"]

    def __str__(self):
        return f"Return {self.id} - ${self.total_amount}"

    def calculate_total_amount(self):
        """Calculate and return the total amount from all items."""
        return sum(item.total_price for item in self.items.all())
