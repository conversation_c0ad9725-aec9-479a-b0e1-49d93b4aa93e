from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from invoices_return.views.invoice_return import InvoiceReturnViewSet

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r"invoice-returns", InvoiceReturnViewSet, basename="invoice-return")

# The API URLs are now determined automatically by the router.
app_name = "invoices_return"
urlpatterns = [
    path("", include(router.urls)),
]
