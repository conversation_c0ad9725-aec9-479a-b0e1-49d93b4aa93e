from django.contrib import admin

from .models import InvoiceReturn, InvoiceReturnItem


class InvoiceReturnItemInline(admin.TabularInline):
    model = InvoiceReturnItem
    extra = 0
    readonly_fields = ("total_price", "created", "modified")
    fields = ("product", "quantity", "unit_price", "total_price", "notes")


@admin.register(InvoiceReturn)
class InvoiceReturnAdmin(admin.ModelAdmin):
    list_display = ("id", "warehouse", "total_amount", "payment_method", "created")
    list_filter = ("warehouse", "payment_method", "created", "modified")
    search_fields = ("notes",)
    readonly_fields = ("created", "modified", "total_amount")
    date_hierarchy = "created"
    inlines = [InvoiceReturnItemInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("warehouse", "pos_session")


@admin.register(InvoiceReturnItem)
class InvoiceReturnItemAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "invoice_return",
        "product",
        "quantity",
        "unit_price",
        "total_price",
        "created",
    )
    list_filter = ("product", "created")
    search_fields = ("product__name", "notes")
    readonly_fields = ("created", "modified", "total_price")
    date_hierarchy = "created"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("invoice_return", "product")
