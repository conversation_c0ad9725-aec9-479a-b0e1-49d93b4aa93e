from django_filters.rest_framework import Django<PERSON>ilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.permissions import IsAuthenticated

from products.models.product import Product
from products.serialzers.product import ProductSerializer, ProductReadSerializer
from utils.pagination import PaginationClass
from utils.permissions import Is<PERSON>dminOnly, IsAdminOrManager


class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows products to be viewed or edited.
    """

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    pagination_class = PaginationClass
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["category", "unit_type"]
    search_fields = ["name", "barcode", "description"]
    ordering_fields = ["name", "price", "created"]
    ordering = ["name"]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["update", "partial_update", "destroy"]:
            permission_classes = [IsAdminOnly]
        elif self.action in ["create"]:
            permission_classes = [IsAdminOrManager]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action in ["list", "retrieve"]:
            return ProductReadSerializer
        return ProductSerializer
