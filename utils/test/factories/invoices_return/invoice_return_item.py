from decimal import Decimal

import factory
from factory import fuzzy

from invoices_return.models.invoice_return_item import InvoiceReturnItem
from utils.test.factories.invoices_return.invoice_return import InvoiceReturnFactory
from utils.test.factories.product.product import ProductFactory


class InvoiceReturnItemFactory(factory.django.DjangoModelFactory):
    """Factory for creating InvoiceReturnItem instances."""

    class Meta:
        model = InvoiceReturnItem

    invoice_return = factory.SubFactory(InvoiceReturnFactory)
    product = factory.SubFactory(ProductFactory)
    quantity = fuzzy.FuzzyDecimal(1.000, 10.000, precision=3)
    unit_price = fuzzy.FuzzyDecimal(5.00, 100.00, precision=2)
    notes = factory.Faker("text", max_nb_chars=100)

    @factory.post_generation
    def calculate_total_price(obj, create, extracted, **kwargs):
        """Calculate total_price after creation."""
        if create:
            obj.total_price = obj.quantity * obj.unit_price
            obj.save()

    @classmethod
    def create_with_specific_values(cls, quantity=None, unit_price=None, **kwargs):
        """Create an item with specific quantity and unit_price."""
        if quantity is not None:
            kwargs["quantity"] = quantity
        if unit_price is not None:
            kwargs["unit_price"] = unit_price
        return cls.create(**kwargs)

    @classmethod
    def create_high_value_item(cls, **kwargs):
        """Create a high-value return item."""
        kwargs.setdefault("quantity", Decimal("5.000"))
        kwargs.setdefault("unit_price", Decimal("50.00"))
        return cls.create(**kwargs)

    @classmethod
    def create_low_value_item(cls, **kwargs):
        """Create a low-value return item."""
        kwargs.setdefault("quantity", Decimal("1.000"))
        kwargs.setdefault("unit_price", Decimal("10.00"))
        return cls.create(**kwargs)
