from decimal import Decimal

import factory
from factory import fuzzy

from invoices_return.models.invoice_return import InvoiceReturn
from utils.test.factories.pos.pos_session import POSSessionFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class InvoiceReturnFactory(factory.django.DjangoModelFactory):
    """Factory for creating InvoiceReturn instances."""

    class Meta:
        model = InvoiceReturn

    warehouse = factory.SubFactory(WarehouseFactory)
    pos_session = factory.SubFactory(POSSessionFactory)
    total_amount = fuzzy.FuzzyDecimal(10.00, 1000.00, precision=2)
    payment_method = InvoiceReturn.PaymentMethod.CASH
    notes = factory.Faker("text", max_nb_chars=200)

    @classmethod
    def create_with_items(cls, items_count=2, **kwargs):
        """Create an invoice return with specified number of items."""
        from utils.test.factories.invoices_return.invoice_return_item import (
            InvoiceReturnItemFactory,
        )

        invoice_return = cls.create(**kwargs)

        # Create items
        total_amount = Decimal("0.00")
        for _ in range(items_count):
            item = InvoiceReturnItemFactory.create(invoice_return=invoice_return)
            total_amount += item.total_price

        # Update total amount
        invoice_return.total_amount = total_amount
        invoice_return.save()

        return invoice_return

    @classmethod
    def create_cash_return(cls, **kwargs):
        """Create a cash invoice return."""
        kwargs.setdefault("payment_method", InvoiceReturn.PaymentMethod.CASH)
        return cls.create(**kwargs)

    @classmethod
    def create_with_pos_session(cls, pos_session=None, **kwargs):
        """Create an invoice return with specific POS session."""
        if pos_session:
            kwargs["pos_session"] = pos_session
            kwargs["warehouse"] = pos_session.pos.warehouse
        return cls.create(**kwargs)
