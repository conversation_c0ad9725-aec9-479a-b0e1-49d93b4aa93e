import random
from decimal import Decimal

import factory
from factory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubFactory
from factory.django import DjangoModelFactory

from purchases_return.models.purchase_return_item import PurchaseReturnItem
from utils.test.factories.product.product import ProductFactory


class PurchaseReturnItemFactory(DjangoModelFactory):
    """
    Factory for creating PurchaseReturnItem instances for testing.
    """

    class Meta:
        model = PurchaseReturnItem
        skip_postgeneration_save = True

    # Purchase return will be set when creating items through PurchaseReturnFactory
    # or can be specified directly when creating standalone items
    purchase_return = None

    product = SubFactory(ProductFactory)

    # Generate a random quantity between 1 and 10
    quantity = LazyAttribute(
        lambda _: Decimal(random.uniform(1, 10)).quantize(Decimal("0.001"))
    )

    # Use the product's cost as the unit cost (with some variation)
    unit_cost = LazyAttribute(
        lambda o: o.product.cost
        * Decimal(random.uniform(0.8, 1.2)).quantize(Decimal("0.01"))
    )

    notes = factory.Faker("sentence")

    @classmethod
    def create_with_zero_cost(cls, **kwargs):
        """
        Create a return item with zero cost (damaged item).

        Usage:
            item = PurchaseReturnItemFactory.create_with_zero_cost(purchase_return=return)
        """
        kwargs.update(
            {
                "unit_cost": Decimal("0.00"),
            }
        )
        return cls.create(**kwargs)

    @classmethod
    def create_with_specific_cost(cls, unit_cost, **kwargs):
        """
        Create a return item with a specific unit cost.

        Usage:
            item = PurchaseReturnItemFactory.create_with_specific_cost(
                unit_cost=Decimal('15.50'),
                purchase_return=return
            )
        """
        unit_cost = Decimal(str(unit_cost))
        kwargs.update(
            {
                "unit_cost": unit_cost,
            }
        )
        return cls.create(**kwargs)

    @classmethod
    def create_with_specific_quantity(cls, quantity, **kwargs):
        """
        Create a return item with a specific quantity.

        Usage:
            item = PurchaseReturnItemFactory.create_with_specific_quantity(
                quantity=Decimal('5.500'),
                purchase_return=return
            )
        """
        quantity = Decimal(str(quantity))
        kwargs.update(
            {
                "quantity": quantity,
            }
        )
        return cls.create(**kwargs)

    @classmethod
    def create_for_product(cls, product, **kwargs):
        """
        Create a return item for a specific product.

        Usage:
            item = PurchaseReturnItemFactory.create_for_product(
                product=product,
                purchase_return=return
            )
        """
        kwargs.update(
            {
                "product": product,
                "unit_cost": product.cost,
            }
        )
        return cls.create(**kwargs)
