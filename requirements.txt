# Core
Django==5.2.1
djangorestframework==3.16.0
djangorestframework-simplejwt==5.5.0
PyJWT==2.8.0
python-dotenv==1.0.0

# Database
psycopg2-binary==2.9.10

# Development
django-debug-toolbar==4.3.0
django-extensions==3.2.3

# Code Quality
click==8.2.1
black==23.11.0
flake8==7.1.0

# Documentation
Pillow==10.3.0  # Required for image handling in Django

# Security
django-cors-headers==4.3.1  # For handling CORS if you have a frontend

# API Documentation
drf-yasg[validation]  # For Swagger/OpenAPI documentation

# Testing
pytest==8.2.0
pytest-django==4.8.0
pytest-cov==5.0.0
factory-boy==3.3.0

# Production
gunicorn==21.2.0
whitenoise==6.6.0  # For serving static files in production
python-decouple==3.8  # For managing environment variables
django-filter==25.1
django-environ==0.11.2

psycopg2-binary==2.9.10
black==23.11.0
flake8==7.1.0
django-environ==0.11.2
Faker==37.4.0
django-jazzmin==3.0.1
