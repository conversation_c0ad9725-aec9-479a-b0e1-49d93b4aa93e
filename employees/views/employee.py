from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter

from utils.permissions import IsAdminOnly
from ..models import Employee
from ..serializers import EmployeeSerializer


class EmployeeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing employees with CRUD operations.
    """

    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer
    permission_classes = [IsAdminOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["day_rate", "hour_rate", "pos", "status", "type"]
    search_fields = [
        "address",
        "user__first_name",
        "user__last_name",
        "user__email",
        "user__phone_number",
    ]
    ordering_fields = [
        "user__first_name",
        "user__email",
        "type",
        "day_rate",
        "hire_date",
        "status",
        "pos__name",
        "hour_rate",
    ]
    ordering = ["user__first_name"]
