import random
import time
from datetime import datetime, timedelta, date
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from faker import Faker

from employees.models import Employee, Attendance, Salary
from pos.models import POS

User = get_user_model()


class Command(BaseCommand):
    help = "Generate fake data for employees, attendance, and salaries"

    def add_arguments(self, parser):
        parser.add_argument(
            "--employees",
            type=int,
            default=100,
            help="Number of employees to create (default: 20)",
        )
        parser.add_argument(
            "--attendance-days",
            type=int,
            default=300,
            help="Number of days back to generate attendance (default: 30)",
        )
        parser.add_argument(
            "--attendance-probability",
            type=float,
            default=0.8,
            help="Probability of attendance per day (0.0-1.0, default: 0.8)",
        )
        parser.add_argument(
            "--salary-probability",
            type=float,
            default=0.9,
            help="Probability of salary calculation per attendance (0.0-1.0, default: 0.9)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing employee data before generating new data",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=1000,
            help="Batch size for bulk operations (default: 100)",
        )

    @transaction.atomic
    def handle(self, *args, **options):
        # Start timing
        start_time = time.time()

        fake = Faker()
        employees_count = options["employees"]
        attendance_days = options["attendance_days"]
        attendance_probability = options["attendance_probability"]
        salary_probability = options["salary_probability"]
        clear = options["clear"]
        batch_size = options["batch_size"]

        self.stdout.write(self.style.SUCCESS("🚀 Starting to generate employee data..."))
        self.stdout.write(
            f"📋 Configuration: {employees_count} employees, {attendance_days} days, "
            f"attendance: {attendance_probability:.1%}, salary: {salary_probability:.1%}"
        )
        self.stdout.write(f"⚙️  Performance: batch_size={batch_size}")

        # Clear existing data if requested
        if clear:
            clear_start = time.time()
            self.stdout.write("🗑️  Clearing existing employee data...")
            Salary.objects.all().delete()
            Attendance.objects.all().delete()
            Employee.objects.all().delete()
            # Also delete users that were created for employees
            User.objects.filter(role__in=["employee", "cashier", "manager"]).delete()
            clear_time = time.time() - clear_start
            self.stdout.write(f"✅ Data cleared in {clear_time:.2f} seconds")

        # Get available POS terminals
        pos_terminals = list(POS.objects.all())
        if not pos_terminals:
            self.stdout.write(
                self.style.WARNING("No POS terminals found. Creating a default one...")
            )
            pos_terminal = POS.objects.create(
                name="Default Terminal", description="Default POS terminal for testing"
            )
            pos_terminals = [pos_terminal]

        employees_start = time.time()
        self.stdout.write(f"👥 Creating {employees_count} employees...")

        # Prepare bulk data
        users_to_create = []
        employees_data = []

        # Generate all user and employee data first
        self.stdout.write("  - Preparing user and employee data...")
        for i in range(employees_count):
            first_name = fake.first_name()
            last_name = fake.last_name()
            email = f"{first_name.lower()}.{last_name.lower()}{i}@example.com"

            # Prepare user data
            user_data = {
                "email": email,
                "phone_number": fake.phone_number()[:15],  # Limit phone number length
                "first_name": first_name,
                "last_name": last_name,
                "role": random.choice(["employee", "cashier", "manager"]),
                "is_active": True,
            }
            users_to_create.append(user_data)

            # Prepare employee data
            employee_type = random.choice(Employee.EmployeeType.choices)[0]
            status = random.choice(Employee.Status.choices)[0]

            # Generate realistic rates based on employee type
            if employee_type == Employee.EmployeeType.FULL_TIME:
                hour_rate = Decimal(random.uniform(15, 35)).quantize(Decimal("0.01"))
                day_rate = Decimal(random.uniform(120, 280)).quantize(Decimal("0.01"))
            elif employee_type == Employee.EmployeeType.PART_TIME:
                hour_rate = Decimal(random.uniform(12, 25)).quantize(Decimal("0.01"))
                day_rate = Decimal(random.uniform(80, 200)).quantize(Decimal("0.01"))
            else:  # DAILY
                hour_rate = Decimal(random.uniform(10, 20)).quantize(Decimal("0.01"))
                day_rate = Decimal(random.uniform(60, 160)).quantize(Decimal("0.01"))

            employee_data = {
                "address": fake.address(),
                "type": employee_type,
                "hour_rate": hour_rate,
                "day_rate": day_rate,
                "hire_date": fake.date_between(start_date="-2y", end_date="today"),
                "status": status,
                "pos": random.choice(pos_terminals) if random.random() > 0.3 else None,
                "user_index": i,  # To link with user later
                "display_name": f"{first_name} {last_name} ({employee_type})",
            }
            employees_data.append(employee_data)

        # Bulk create users
        self.stdout.write("  - Bulk creating users...")
        user_objects = []
        for user_data in users_to_create:
            user = User(
                email=user_data["email"],
                phone_number=user_data["phone_number"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                role=user_data["role"],
                is_active=user_data["is_active"],
            )
            user.set_password("password123")
            user_objects.append(user)

        # Use bulk_create for users with batch processing
        created_users = User.objects.bulk_create(user_objects, batch_size=batch_size)
        self.stdout.write(
            f"  - Created {len(created_users)} users in batches of {batch_size}"
        )

        # Bulk create employees
        self.stdout.write("  - Bulk creating employees...")
        employee_objects = []

        for i, employee_data in enumerate(employees_data):
            employee = Employee(
                user=created_users[i],
                address=employee_data["address"],
                type=employee_data["type"],
                hour_rate=employee_data["hour_rate"],
                day_rate=employee_data["day_rate"],
                hire_date=employee_data["hire_date"],
                status=employee_data["status"],
                pos=employee_data["pos"],
            )
            employee_objects.append(employee)

            # Show progress for large datasets
            if (i + 1) % 50 == 0 or i == len(employees_data) - 1:
                self.stdout.write(
                    f"    - Prepared {i + 1}/{len(employees_data)} employees"
                )

        # Use bulk_create for employees with batch processing
        Employee.objects.bulk_create(employee_objects, batch_size=batch_size)

        # Refresh employees list to get IDs and relationships
        employees = list(
            Employee.objects.filter(user__in=created_users).select_related(
                "user", "pos"
            )
        )
        self.stdout.write(
            f"  - Created {len(employees)} employees in batches of {batch_size}"
        )

        # Display sample of created employees
        for i, employee_data in enumerate(employees_data[:5]):  # Show first 5
            self.stdout.write(f"    ✓ {employee_data['display_name']}")
        if len(employees_data) > 5:
            self.stdout.write(f"    ... and {len(employees_data) - 5} more employees")

        # Generate attendance records using bulk operations
        self.stdout.write(
            f"\nGenerating attendance records for the last {attendance_days} days..."
        )

        # Prepare attendance data
        attendance_objects = []
        start_date = date.today() - timedelta(days=attendance_days)
        total_possible_records = 0

        self.stdout.write("  - Preparing attendance data...")
        for employee in employees:
            # Skip terminated employees for recent attendance
            if employee.status == Employee.Status.TERMINATED:
                continue

            current_date = start_date
            employee_attendance_count = 0

            while current_date <= date.today():
                # Skip weekends for some realism (optional)
                if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                    total_possible_records += 1
                    if random.random() < attendance_probability:
                        # Generate realistic work hours
                        start_hour = random.randint(7, 9)  # 7 AM to 9 AM
                        start_minute = random.randint(0, 59)

                        time_in = timezone.make_aware(
                            datetime.combine(current_date, datetime.min.time())
                            + timedelta(hours=start_hour, minutes=start_minute)
                        )

                        # Work duration between 4-10 hours
                        work_hours = random.uniform(4, 10)
                        time_out = time_in + timedelta(hours=work_hours)

                        # Sometimes people forget to clock out
                        if random.random() < 0.1:  # 10% chance of no time_out
                            time_out = None

                        attendance = Attendance(
                            employee=employee,
                            time_in=time_in,
                            time_out=time_out,
                            notes=fake.sentence() if random.random() < 0.3 else "",
                            total_hours=round(
                                (time_out - time_in).total_seconds() / 3600, 2
                            )
                            if time_out
                            else Decimal("0.0"),
                        )
                        attendance_objects.append(attendance)
                        employee_attendance_count += 1

                current_date += timedelta(days=1)

            # Show progress for employees with attendance
            if employee_attendance_count > 0:
                self.stdout.write(
                    f"    - {employee.user.first_name}: {employee_attendance_count} records"
                )

        # Bulk create attendance records
        self.stdout.write(
            f"  - Bulk creating {len(attendance_objects)} attendance records..."
        )
        # Use larger batch size for attendance records if default is small
        attendance_batch_size = max(batch_size, 200)

        # Process in batches to show progress
        for i in range(0, len(attendance_objects), attendance_batch_size):
            batch = attendance_objects[i : i + attendance_batch_size]
            Attendance.objects.bulk_create(batch, batch_size=attendance_batch_size)
            self.stdout.write(
                f"    - Processed batch {i // attendance_batch_size + 1}/{(len(attendance_objects) + attendance_batch_size - 1) // attendance_batch_size}"
            )

        # Get the created attendance records with their IDs
        attendance_records = list(
            Attendance.objects.filter(
                employee__in=employees,
                time_in__gte=timezone.make_aware(
                    datetime.combine(start_date, datetime.min.time())
                ),
            ).select_related("employee")
        )

        self.stdout.write(
            f"  - Created {len(attendance_records)} attendance records from {total_possible_records} possible workdays"
        )

        # Generate salary records using bulk operations
        self.stdout.write("\nGenerating salary records...")

        # Prepare salary data
        salary_objects = []
        processed_count = 0

        self.stdout.write("  - Calculating salaries for completed attendance...")
        for attendance in attendance_records:
            # Only create salary for completed attendance (with time_out)
            if attendance.time_out and random.random() < salary_probability:
                employee = attendance.employee

                # Calculate salary based on employee type and hours worked
                if employee.type == Employee.EmployeeType.DAILY:
                    # Daily workers get day rate regardless of hours (up to 8 hours)
                    if attendance.total_hours <= 8:
                        salary_amount = employee.day_rate
                    else:
                        # Overtime for daily workers
                        overtime_hours = attendance.total_hours - 8
                        salary_amount = employee.day_rate + (
                            employee.hour_rate * Decimal(overtime_hours)
                        )
                else:
                    # Hourly calculation for full-time and part-time
                    salary_amount = employee.hour_rate * Decimal(attendance.total_hours)

                # Add some random variation (bonuses/deductions)
                notes = ""
                if random.random() < 0.1:  # 10% chance of bonus
                    salary_amount *= Decimal(random.uniform(1.1, 1.3))
                    notes = "Performance bonus applied"
                elif random.random() < 0.05:  # 5% chance of deduction
                    salary_amount *= Decimal(random.uniform(0.8, 0.95))
                    notes = "Deduction applied"
                else:
                    notes = fake.sentence() if random.random() < 0.2 else ""

                salary_amount = salary_amount.quantize(Decimal("0.01"))

                salary = Salary(
                    employee=employee,
                    attendance=attendance,
                    salary=salary_amount,
                    notes=notes,
                )
                salary_objects.append(salary)

            processed_count += 1
            # Show progress for large datasets
            if processed_count % 500 == 0:
                self.stdout.write(
                    f"    - Processed {processed_count}/{len(attendance_records)} attendance records"
                )

        # Bulk create salary records
        self.stdout.write(f"  - Bulk creating {len(salary_objects)} salary records...")
        # Use larger batch size for salary records if default is small
        salary_batch_size = max(batch_size, 200)

        # Process in batches
        for i in range(0, len(salary_objects), salary_batch_size):
            batch = salary_objects[i : i + salary_batch_size]
            Salary.objects.bulk_create(batch, batch_size=salary_batch_size)
            self.stdout.write(
                f"    - Processed salary batch {i // salary_batch_size + 1}/{(len(salary_objects) + salary_batch_size - 1) // salary_batch_size}"
            )

            # Get the created salary records count
            salary_records_count = len(salary_objects)
            self.stdout.write(f"  - Created {salary_records_count} salary records")

        # Calculate generation time
        generation_time = time.time() - employees_start
        self.stdout.write(
            f"⏱️  Data generation completed in {generation_time:.2f} seconds"
        )

        # Generate some statistics
        total_employees = Employee.objects.count()
        active_employees = Employee.objects.filter(
            status=Employee.Status.ACTIVE
        ).count()
        total_attendance = Attendance.objects.count()
        total_salaries = Salary.objects.count()

        # Calculate total salary amount
        total_salary_amount = sum(s.salary for s in Salary.objects.all())

        self.stdout.write(
            self.style.SUCCESS(
                f"\n{'=' * 50}\n"
                f"Employee data generation completed successfully!\n"
                f"{'=' * 50}\n"
                f"📊 STATISTICS:\n"
                f"  👥 Total Employees: {total_employees}\n"
                f"  ✅ Active Employees: {active_employees}\n"
                f"  📅 Attendance Records: {total_attendance}\n"
                f"  💰 Salary Records: {total_salaries}\n"
                f"  💵 Total Salary Amount: ${total_salary_amount:,.2f}\n"
                f"{'=' * 50}\n"
                f"🔑 TEST CREDENTIALS:\n"
                f"  All users have password: password123\n"
                f"  Example emails: {employees[0].user.email if employees else 'N/A'}\n"
                f"{'=' * 50}"
            )
        )

        # Show breakdown by employee type
        for emp_type, emp_type_label in Employee.EmployeeType.choices:
            count = Employee.objects.filter(type=emp_type).count()
            if count > 0:
                self.stdout.write(f"  {emp_type_label}: {count} employees")

        # Show breakdown by status
        for status, status_label in Employee.Status.choices:
            count = Employee.objects.filter(status=status).count()
            if count > 0:
                self.stdout.write(f"  {status_label}: {count} employees")

        # Final timing information
        total_time = time.time() - start_time
        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎉 PERFORMANCE SUMMARY:\n"
                f"  ⏱️  Total execution time: {total_time:.2f} seconds\n"
                f"  📊 Records per second: {(total_employees + total_attendance + total_salaries) / total_time:.1f}\n"
                f"  🚀 Using bulk_create for optimal performance\n"
                f"{'=' * 50}"
            )
        )
