import io
import os
import shutil
from datetime import date, timedelta
from decimal import Decimal

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from PIL import Image
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from employees.models import Employee
from users.models import User
from utils.test.base_test import BaseTestCase
from utils.test.factories.employee.employee import EmployeeFactory
from utils.test.factories.pos.pos import POSFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class EmployeeViewSetTestCase(BaseTestCase):
    """
    Test cases for EmployeeViewSet.
    """

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # URLs
        self.list_url = reverse("employees:employee-list")
        self.detail_url_name = "employees:employee-detail"

        # Create additional test employees
        self.test_warehouse = WarehouseFactory.create(name="Test Warehouse 2")
        self.test_pos = POSFactory.create(
            warehouse=self.test_warehouse, name="Test POS 2"
        )

        # Create test employees with different attributes for filtering/searching
        self.employee1 = EmployeeFactory.create(
            user__first_name="<PERSON>",
            user__last_name="<PERSON>",
            user__role=User.Role.CASHIER,
            address="123 Main Street, Cairo",
            type=Employee.EmployeeType.FULL_TIME,
            hour_rate=Decimal("25.00"),
            day_rate=Decimal("200.00"),
            hire_date=date.today() - timedelta(days=100),
            status=Employee.Status.ACTIVE,
            pos=self.pos,
        )

        self.employee2 = EmployeeFactory.create(
            user__first_name="Bob",
            user__last_name="Smith",
            user__role=User.Role.MANAGER,
            address="456 Oak Avenue, Alexandria",
            type=Employee.EmployeeType.PART_TIME,
            hour_rate=Decimal("30.00"),
            day_rate=Decimal("240.00"),
            hire_date=date.today() - timedelta(days=50),
            status=Employee.Status.ACTIVE,
            pos=self.test_pos,
        )

        self.employee3 = EmployeeFactory.create(
            user__first_name="Charlie",
            user__last_name="Brown",
            user__role=User.Role.CASHIER,
            address="789 Pine Road, Giza",
            type=Employee.EmployeeType.DAILY,
            hour_rate=Decimal("20.00"),
            day_rate=Decimal("160.00"),
            hire_date=date.today() - timedelta(days=200),
            status=Employee.Status.INACTIVE,
            pos=None,
        )

    def tearDown(self):
        """Clean up uploaded files after each test."""
        super().tearDown()

        # Clean up media files created during tests
        media_root = settings.MEDIA_ROOT
        identification_dir = os.path.join(media_root, "identification")

        if os.path.exists(identification_dir):
            shutil.rmtree(identification_dir)

    def get_detail_url(self, employee_id):
        """Helper method to get detail URL for an employee."""
        return reverse(self.detail_url_name, kwargs={"pk": employee_id})

    def generate_image_file(self):
        """Generate a temporary image file"""
        image = Image.new("RGB", (100, 100), color="red")
        temp_file = io.BytesIO()
        image.save(temp_file, format="JPEG")
        temp_file.seek(0)
        return SimpleUploadedFile(
            "test.jpg", temp_file.read(), content_type="image/jpeg"
        )

    def create_test_data(self):
        """Generate test data for creating an employee."""
        return {
            "user.email": "<EMAIL>",
            "user.phone_number": "01234567890",
            "user.password": "testpass123",
            "user.first_name": "Test",
            "user.last_name": "User",
            "user.role": User.Role.CASHIER,
            "address": "Test Address",
            "type": Employee.EmployeeType.FULL_TIME,
            "hour_rate": "25.00",
            "day_rate": "200.00",
            "hire_date": date.today().isoformat(),
            "status": Employee.Status.ACTIVE,
            "pos": self.pos.id,
            "identification": self.generate_image_file(),
        }

    # Authentication Tests
    def test_list_employees_unauthenticated(self):
        """Test that unauthenticated users cannot list employees."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_employee_unauthenticated(self):
        """Test that unauthenticated users cannot retrieve employees."""
        url = self.get_detail_url(self.employee1.id)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_employee_unauthenticated(self):
        """Test that unauthenticated users cannot create employees."""
        data = self.create_test_data()
        response = self.client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Permission Tests
    def test_list_employees_as_manager(self):
        """Test that manager users cannot list employees (admin only)."""
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_employees_as_cashier(self):
        """Test that cashier users cannot list employees (admin only)."""
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_employee_as_manager(self):
        """Test that manager users cannot create employees (admin only)."""
        data = self.create_test_data()
        response = self.manager_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_employee_as_cashier(self):
        """Test that cashier users cannot create employees (admin only)."""
        data = self.create_test_data()
        response = self.cashier_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_employee_as_manager(self):
        """Manager should not be able to retrieve employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)
        response = self.manager_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_employee_as_cashier(self):
        """Cashier should not be able to retrieve employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)
        response = self.cashier_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_employee_as_manager(self):
        """Manager should not be able to update employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)
        data = {"address": "Manager Update"}

        response = self.manager_client.patch(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_employee_as_cashier(self):
        """Cashier should not be able to update employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)
        data = {"address": "Cashier Update"}

        response = self.cashier_client.patch(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_employee_as_manager(self):
        """Manager should not be able to delete employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)

        response = self.manager_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_employee_as_cashier(self):
        """Cashier should not be able to delete employees (admin only)."""
        url = self.get_detail_url(self.employee1.id)

        response = self.cashier_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    # List Tests
    def test_list_employees_as_admin(self):
        """Admin should see all employees including base test employees."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should include manager_employee, cashier_employee, and our 3 test employees
        self.assertEqual(len(response.data["results"]), 5)

        # Check that employees are ordered by user__first_name (default ordering)
        employee_names = [emp["user"]["first_name"] for emp in response.data["results"]]
        self.assertEqual(employee_names, sorted(employee_names))

    def test_list_employees_pagination(self):
        """Test that pagination works correctly."""
        # Create more employees to test pagination
        for i in range(25):
            EmployeeFactory.create(
                user__first_name=f"Employee{i:02d}",
                user__email=f"emp{i}@example.com",
                user__phone_number=f"0123456{i:04d}",
            )

        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check pagination structure
        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertIn("results", response.data)

        # Should have 20 results per page (default page size)
        self.assertEqual(len(response.data["results"]), 20)
        self.assertEqual(response.data["count"], 30)  # 5 existing + 25 new

    # Retrieve Tests
    def test_retrieve_employee_as_admin(self):
        """Admin should be able to retrieve any employee."""
        url = self.get_detail_url(self.employee1.id)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check response data
        self.assertEqual(response.data["id"], self.employee1.id)
        self.assertEqual(response.data["user"]["first_name"], "Alice")
        self.assertEqual(response.data["user"]["last_name"], "Johnson")
        self.assertEqual(response.data["address"], "123 Main Street, Cairo")
        self.assertEqual(response.data["type"], Employee.EmployeeType.FULL_TIME)
        self.assertEqual(response.data["hour_rate"], "25.00")
        self.assertEqual(response.data["day_rate"], "200.00")
        self.assertEqual(response.data["status"], Employee.Status.ACTIVE)

    def test_retrieve_nonexistent_employee(self):
        """Test retrieving a non-existent employee."""
        url = self.get_detail_url(99999)
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Create Tests
    def test_create_employee_as_admin_success(self):
        """
        Test that admin can create employees with nested user data using factory.
        Since the API requires identification field and doesn't support nested multipart data,
        we test the validation behavior instead.
        """
        data = self.create_test_data()
        initial_count = Employee.objects.count()
        response = self.admin_client.post(self.list_url, data, format="multipart")
        # This will fail due to missing identification field, which is expected
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Verify employee was created
        self.assertEqual(Employee.objects.count(), initial_count + 1)
        self.assertEqual(response.data["address"], data["address"])
        self.assertEqual(response.data["type"], data["type"])
        self.assertEqual(response.data["hour_rate"], data["hour_rate"])
        self.assertEqual(response.data["day_rate"], data["day_rate"])
        self.assertEqual(response.data["status"], data["status"])
        self.assertEqual(response.data["pos"], data["pos"])
        self.assertEqual(response.data["user"]["first_name"], data["user.first_name"])
        self.assertEqual(response.data["user"]["last_name"], data["user.last_name"])
        self.assertEqual(response.data["user"]["role"], data["user.role"])
        self.assertEqual(response.data["user"]["email"], data["user.email"])
        self.assertEqual(
            response.data["user"]["phone_number"], data["user.phone_number"]
        )

    def test_create_employee_validation_without_identification(self):
        """Test that identification field is required for employee creation."""
        data = self.create_test_data()
        data.pop("identification")
        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("identification", response.data)
        self.assertEqual(response.data["identification"][0].code, "required")

    def test_create_employee_without_pos_validation(self):
        """Test that employees can be created without POS assignment but still need identification."""
        data = self.create_test_data()
        data.pop("pos")
        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_employee_with_invalid_data(self):
        """Test creating employee with invalid data."""
        data = self.create_test_data()
        data["user.email"] = "invalid-email"
        data["user.phone_number"] = "123"
        data["user.password"] = "short"
        data["user.first_name"] = ""
        data["user.role"] = "invalid_role"
        data["address"] = ""
        data["type"] = "invalid_type"
        data["hour_rate"] = "-10.00"
        data["day_rate"] = "-10.00"
        data["hire_date"] = "invalid_date"
        data["status"] = "invalid_status"
        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # Check that validation errors are returned
        self.assertIn("user", response.data)
        self.assertIn("email", response.data["user"])
        self.assertIn("role", response.data["user"])
        self.assertIn("address", response.data)
        self.assertIn("type", response.data)
        self.assertIn("hour_rate", response.data)
        self.assertIn("day_rate", response.data)
        self.assertIn("hire_date", response.data)
        self.assertIn("status", response.data)

    def test_create_employee_missing_required_fields(self):
        """Test creating employee with missing required fields."""
        data = {
            "address": "Test Address",
        }

        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Check that required field errors are returned
        self.assertIn("user", response.data)
        self.assertIn("hour_rate", response.data)
        self.assertIn("day_rate", response.data)
        self.assertIn("hire_date", response.data)

    def test_create_employee_duplicate_email(self):
        """Test creating employee with duplicate email."""
        data = self.create_test_data()
        data["user.email"] = self.employee1.user.email  # Duplicate email

        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["user"],
            [ErrorDetail(string="This email is already in use.", code="invalid")],
        )

    def test_create_employee_duplicate_phone(self):
        """Test creating employee with duplicate phone number."""
        data = self.create_test_data()
        data["user.phone_number"] = self.employee1.user.phone_number  # Duplicate phone

        response = self.admin_client.post(self.list_url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data["user"],
            [
                ErrorDetail(
                    string="This phone number is already in use.", code="invalid"
                )
            ],
        )

    # Update Tests
    def test_update_employee_as_admin(self):
        """Admin should be able to update employees using PATCH (partial update)."""
        url = self.get_detail_url(self.employee1.id)
        data = self.create_test_data()
        data["user.first_name"] = "Updated"
        data["user.last_name"] = "Name"
        data["user.role"] = User.Role.MANAGER
        data["address"] = "Updated Address"
        data["type"] = Employee.EmployeeType.PART_TIME
        data["hour_rate"] = "35.00"
        data["day_rate"] = "280.00"
        data["user.email"] = self.employee1.user.email
        data["user.phone_number"] = self.employee1.user.phone_number

        # Use PATCH instead of PUT to avoid requiring identification field
        response = self.admin_client.put(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check that employee was updated
        self.employee1.refresh_from_db()
        self.assertEqual(self.employee1.user.first_name, "Updated")
        self.assertEqual(self.employee1.user.last_name, "Name")
        self.assertEqual(self.employee1.user.role, User.Role.MANAGER)
        self.assertEqual(self.employee1.address, "Updated Address")
        self.assertEqual(self.employee1.type, Employee.EmployeeType.PART_TIME)
        self.assertEqual(self.employee1.hour_rate, Decimal("35.00"))
        self.assertEqual(self.employee1.day_rate, Decimal("280.00"))

    def test_partial_update_employee_as_admin(self):
        """Admin should be able to partially update employees."""
        url = self.get_detail_url(self.employee1.id)
        data = {
            "address": "Partially Updated Address",
            "status": Employee.Status.TERMINATED,
        }

        original_first_name = self.employee1.user.first_name
        response = self.admin_client.patch(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only specified fields were updated
        self.employee1.refresh_from_db()
        self.assertEqual(self.employee1.address, "Partially Updated Address")
        self.assertEqual(self.employee1.status, Employee.Status.TERMINATED)
        self.assertEqual(
            self.employee1.user.first_name, original_first_name
        )  # Unchanged

    # Delete Tests
    def test_delete_employee_as_admin(self):
        """Admin should be able to delete employees."""
        url = self.get_detail_url(self.employee3.id)
        initial_count = Employee.objects.count()

        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that employee was deleted
        self.assertEqual(Employee.objects.count(), initial_count - 1)
        self.assertFalse(Employee.objects.filter(id=self.employee3.id).exists())

    def test_delete_nonexistent_employee(self):
        """Test deleting a non-existent employee."""
        url = self.get_detail_url(99999)
        response = self.admin_client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # Filtering Tests
    def test_filter_employees_by_day_rate(self):
        """Test filtering employees by day_rate."""
        url = f"{self.list_url}?day_rate=200.00"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only employee1 with day_rate=200.00
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    def test_filter_employees_by_hour_rate(self):
        """Test filtering employees by hour_rate."""
        url = f"{self.list_url}?hour_rate=30.00"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only employee2 with hour_rate=30.00
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee2.id)

    def test_filter_employees_by_pos(self):
        """Test filtering employees by POS."""
        url = f"{self.list_url}?pos={self.pos.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employees assigned to self.pos (employee1, manager_employee, cashier_employee)
        employee_ids = [emp["id"] for emp in response.data["results"]]
        self.assertIn(self.employee1.id, employee_ids)
        self.assertIn(self.manager_employee.id, employee_ids)
        self.assertIn(self.cashier_employee.id, employee_ids)
        self.assertNotIn(self.employee2.id, employee_ids)  # assigned to test_pos
        self.assertNotIn(self.employee3.id, employee_ids)  # no POS

    def test_filter_employees_multiple_filters(self):
        """Test filtering employees with multiple filters."""
        url = f"{self.list_url}?pos={self.pos.id}&day_rate=200.00"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only employee1 (matches both pos and day_rate)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    # Search Tests
    def test_search_employees_by_address(self):
        """Test searching employees by address."""
        url = f"{self.list_url}?search=Cairo"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employee1 with address containing "Cairo"
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    def test_search_employees_by_first_name(self):
        """Test searching employees by user first name."""
        url = f"{self.list_url}?search=Alice"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employee1 with first_name="Alice"
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    def test_search_employees_by_last_name(self):
        """Test searching employees by user last name."""
        url = f"{self.list_url}?search=Smith"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employee2 with last_name="Smith"
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee2.id)

    def test_search_employees_case_insensitive(self):
        """Test that search is case insensitive."""
        url = f"{self.list_url}?search=alice"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employee1 even with lowercase search
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    def test_search_employees_partial_match(self):
        """Test searching employees with partial matches."""
        url = f"{self.list_url}?search=Main"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employee1 with address containing "Main Street"
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.employee1.id)

    # Ordering Tests
    def test_default_ordering_by_first_name(self):
        """Test that employees are ordered by user__first_name by default."""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that employees are ordered by first name
        first_names = [emp["user"]["first_name"] for emp in response.data["results"]]
        self.assertEqual(first_names, sorted(first_names))

    def test_ordering_by_day_rate(self):
        """Test ordering employees by day_rate."""
        url = f"{self.list_url}?ordering=day_rate"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that employees are ordered by day_rate (ascending)
        day_rates = [Decimal(emp["day_rate"]) for emp in response.data["results"]]
        self.assertEqual(day_rates, sorted(day_rates))

    def test_ordering_by_day_rate_descending(self):
        """Test ordering employees by day_rate in descending order."""
        url = f"{self.list_url}?ordering=-day_rate"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that employees are ordered by day_rate (descending)
        day_rates = [Decimal(emp["day_rate"]) for emp in response.data["results"]]
        self.assertEqual(day_rates, sorted(day_rates, reverse=True))

    def test_ordering_by_hire_date(self):
        """Test ordering employees by hire_date."""
        url = f"{self.list_url}?ordering=hire_date"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that employees are ordered by hire_date (ascending)
        hire_dates = [emp["hire_date"] for emp in response.data["results"]]
        self.assertEqual(hire_dates, sorted(hire_dates))

    def test_ordering_by_first_name_explicit(self):
        """Test explicit ordering by user__first_name."""
        url = f"{self.list_url}?ordering=user__first_name"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that employees are ordered by first name
        first_names = [emp["user"]["first_name"] for emp in response.data["results"]]
        self.assertEqual(first_names, sorted(first_names))

    def test_invalid_ordering_field(self):
        """Test that invalid ordering fields are ignored."""
        url = f"{self.list_url}?ordering=invalid_field"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should fall back to default ordering (user__first_name)
        first_names = [emp["user"]["first_name"] for emp in response.data["results"]]
        self.assertEqual(first_names, sorted(first_names))

    # Combined Tests
    def test_filter_search_and_ordering_combined(self):
        """Test combining filtering, searching, and ordering."""
        # Create additional employee for better testing
        EmployeeFactory.create(
            user__first_name="Zara",
            user__last_name="Johnson",
            user__role=User.Role.CASHIER,
            address="999 Main Boulevard, Cairo",
            type=Employee.EmployeeType.FULL_TIME,
            hour_rate=Decimal("25.00"),
            day_rate=Decimal("200.00"),
            hire_date=date.today() - timedelta(days=150),
            status=Employee.Status.ACTIVE,
            pos=self.pos,
        )

        # Filter by pos, search for "Main", and order by first name descending
        url = (
            f"{self.list_url}?pos={self.pos.id}&search=Main&ordering=-user__first_name"
        )
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return employees with "Main" in address, assigned to pos, ordered by first name desc
        self.assertEqual(len(response.data["results"]), 2)  # Alice and Zara
        first_names = [emp["user"]["first_name"] for emp in response.data["results"]]
        self.assertEqual(first_names, ["Zara", "Alice"])  # Descending order

    # Edge Cases
    def test_empty_search_query(self):
        """Test that empty search query returns all employees."""
        url = f"{self.list_url}?search="
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return all employees (same as no search)
        all_response = self.admin_client.get(self.list_url)
        self.assertEqual(
            len(response.data["results"]), len(all_response.data["results"])
        )

    def test_nonexistent_pos_filter(self):
        """Test filtering by non-existent POS."""
        url = f"{self.list_url}?pos=99999"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_employee_string_representation(self):
        """Test the string representation of Employee model."""
        expected_str = f"{self.employee1.user.first_name} {self.employee1.user.last_name} - {self.employee1.user.role} - {self.employee1.pos.name}"
        self.assertEqual(str(self.employee1), expected_str)

        # Test employee without POS
        expected_str_no_pos = f"{self.employee3.user.first_name} {self.employee3.user.last_name} - {self.employee3.user.role} - No POS"
        self.assertEqual(str(self.employee3), expected_str_no_pos)
