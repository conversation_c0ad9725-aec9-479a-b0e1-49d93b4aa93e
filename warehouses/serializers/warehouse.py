from rest_framework import serializers
from ..models import Warehouse


class WarehouseSerializer(serializers.ModelSerializer):
    """
    Serializer for the Warehouse model.
    Handles serialization and deserialization of Warehouse instances.
    """

    class Meta:
        model = Warehouse
        fields = ["id", "name", "location", "description", "created", "modified"]
        read_only_fields = ["id", "created", "modified"]

    def validate_name(self, value):
        """Ensure warehouse name is unique and properly formatted."""
        if not value.strip():
            raise serializers.ValidationError("Warehouse name cannot be empty.")
        return value.strip()

    def validate_location(self, value):
        """Ensure location is not empty."""
        if not value.strip():
            raise serializers.ValidationError("Location cannot be empty.")
        return value.strip()
