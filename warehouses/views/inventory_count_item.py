from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework import mixins, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from utils.pagination import PaginationClass
from utils.permissions import IsAdminOrManager

from ..models import InventoryCount, InventoryCountItem
from ..serializers import InventoryCountItemSerializer


class InventoryCountItemViewSet(
    mixins.ListModelMixin,
    GenericViewSet,
):
    """
    ViewSet for managing inventory count items.
    """

    serializer_class = InventoryCountItemSerializer
    permission_classes = [IsAdminOrManager]

    def get_queryset(self):
        """
        Return inventory count items for the specified inventory count.
        """
        inventory_count_id = self.kwargs.get("inventory_count_pk")
        if self.request.user.is_admin:
            return InventoryCountItem.objects.filter(
                inventory_count_id=inventory_count_id
            ).select_related("stock_item__product")
        return InventoryCountItem.objects.filter(
            inventory_count_id=inventory_count_id,
            recorded_quantity__isnull=True,
            inventory_count__warehouse__pos__employee__user=self.request.user,
        ).select_related("stock_item__product")

    def validate_bulk_update(self, inventory_count):
        # When an inventory count is first created, it's in 'draft' status and hasn't started.
        # Once saved as draft, it initializes the first set of inventory count items.
        # After this, the inventory count transitions to 'in progress' status.
        if inventory_count.status == InventoryCount.Status.DRAFT:
            inventory_count.start(self.request.user)
            inventory_count.refresh_from_db()

        if inventory_count.status != InventoryCount.Status.IN_PROGRESS:
            return Response(
                {"error": "Can only update items for inventory counts in progress"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["patch"])
    def bulk_update(self, request, inventory_count_pk=None):
        """
        Bulk update multiple inventory count items.

        Expected request data format:
        [
            {
                "id": 1,
                "recorded_quantity": 10,
                "notes": "Updated notes"
            },
            {
                "id": 2,
                "recorded_quantity": 20,
                "notes": "Another update"
            }
        ]
        """

        # Verify the inventory count exists and is in progress
        inventory_count = get_object_or_404(InventoryCount, pk=inventory_count_pk)
        self.validate_bulk_update(inventory_count)

        if not isinstance(request.data, list):
            return Response(
                {"error": "Expected a list of items"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all item IDs from the request
        item_ids = [
            item.get("id") for item in request.data if item.get("id") is not None
        ]

        # Get all items that belong to this inventory count
        items = InventoryCountItem.objects.filter(
            id__in=item_ids,
            inventory_count_id=inventory_count_pk,
            recorded_quantity__isnull=True,
        )

        if len(items) != len(item_ids):
            return Response(
                {
                    "error": "One or more items not found in this inventory count or recorded quantity set before"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create a mapping of ID to item for faster lookup
        item_map = {item.id: item for item in items}

        # Validate all updates first
        updates = []
        for item_data in request.data:
            item_id = item_data.get("id")
            item = item_map.get(item_id)
            # Validate the update data
            serializer = self.get_serializer(item, data=item_data, partial=True)
            if not serializer.is_valid():
                return Response(
                    {
                        "error": f"Validation error for item {item_id}",
                        "details": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            updates.append((item, serializer.validated_data))

        # If we got here, all updates are valid - apply them in a transaction
        try:
            with transaction.atomic():
                updated_items = []
                for item, validated_data in updates:
                    # Update only the allowed fields
                    for field in ["recorded_quantity", "notes"]:
                        if field in validated_data:
                            setattr(item, field, validated_data[field])

                    item.system_quantity = item.stock_item.quantity
                    item.save()
                    # Update the stock item quantity if there's a difference
                    if item.difference != 0:
                        item.stock_item.quantity = item.recorded_quantity
                        item.stock_item.save(update_fields=["quantity"])
                    updated_items.append(item)

                # Refresh the inventory count status if needed
                inventory_count.refresh_from_db()
                # Check if the inventory count is completed
                if not inventory_count.items.filter(
                    recorded_quantity__isnull=True
                ).exists():
                    inventory_count.complete()
                # Serialize the updated items
                serializer = self.get_serializer(updated_items, many=True)
                return Response(serializer.data)

        except Exception as e:
            return Response(
                {"error": "Failed to update items", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
